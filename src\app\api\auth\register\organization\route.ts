import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { hashPassword } from '@/lib/auth'
import { organizationRegistrationSchema } from '@/lib/validations'
import { UserType, MembershipStatus } from '@prisma/client'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validation des données
    const validationResult = organizationRegistrationSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Données invalides', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const data = validationResult.data

    // Vérifier si l'email existe déjà
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'Un compte avec cet email existe déjà' },
        { status: 409 }
      )
    }

    // Hasher le mot de passe
    const hashedPassword = await hashPassword(data.password)

    // C<PERSON>er l'utilisateur et son profil
    const user = await prisma.user.create({
      data: {
        email: data.email,
        password: hashedPassword,
        name: `${data.firstName} ${data.lastName}`,
        phone: data.phone,
        userType: UserType.ORGANIZATION,
        profile: {
          create: {
            firstName: data.firstName,
            lastName: data.lastName,
            address: data.address,
            city: data.city,
            postalCode: data.postalCode,
            country: data.country,
            organizationName: data.organizationName,
            siret: data.siret || null,
            website: data.website || null,
            description: data.description,
            membershipStatus: MembershipStatus.PENDING
          }
        }
      },
      include: {
        profile: true
      }
    })

    // Log de l'activité
    await prisma.activityLog.create({
      data: {
        action: 'USER_REGISTRATION',
        description: `Nouvelle inscription organisation: ${data.organizationName}`,
        userId: user.id,
        metadata: {
          userType: 'ORGANIZATION',
          organizationName: data.organizationName,
          organizationType: data.organizationType,
          sector: data.sector
        }
      }
    })

    // Retourner la réponse (sans le mot de passe)
    const { password: _, ...userWithoutPassword } = user
    
    return NextResponse.json({
      message: 'Inscription réussie',
      user: userWithoutPassword
    }, { status: 201 })

  } catch (error) {
    console.error('Erreur lors de l\'inscription organisation:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
