import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const organizationTypes = await prisma.organizationType.findMany({
      where: { isActive: true },
      orderBy: { order: 'asc' },
      select: {
        id: true,
        name: true
      }
    })

    return NextResponse.json(organizationTypes)
  } catch (error) {
    console.error('Erreur lors de la récupération des types d\'organisation:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
