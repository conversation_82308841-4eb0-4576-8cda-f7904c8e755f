# 🧪 Test Manuel - Vue d'ensemble Dashboard

## 🎯 Objectif
Vérifier que l'onglet "Vue d'ensemble" affiche correctement les candidatures récentes avec pagination et les entretiens à venir avec les vraies données.

## 📋 Prérequis
- ✅ Application démarrée : `npm run dev`
- ✅ Base de données avec candidats et entretiens
- ✅ Dashboard accessible : http://localhost:3000/dashboard

## 🔍 Tests à Effectuer

### 1. 🏠 Accès à la Vue d'ensemble
1. **Ouvrir** : http://localhost:3000/dashboard
2. **Vérifier** : Onglet "Vue d'ensemble" actif par défaut
3. **Vérifier** : Page se charge sans erreur
4. **Vérifier** : Statistiques générales affichées en haut

### 2. 📋 Section "Candidatures récentes"

#### Affichage des Données
1. **Vérifier** : Section "Candidatures récentes" visible
2. **Vérifier** : Maximum 10 candidatures affichées
3. **Vérifier** : Informations complètes pour chaque candidat :
   - Nom et email
   - Type (Association, Organisation, Bénévole)
   - Organisation (si applicable)
   - Statut avec badge coloré
   - Date de candidature

#### Pagination
1. **Vérifier** : Texte "Affichage de X sur Y candidatures"
2. **Si plus de 10 candidats** :
   - Boutons "Précédent" et "Suivant" visibles
   - Indicateur "Page X sur Y"
   - Bouton "Précédent" désactivé sur page 1
   - Bouton "Suivant" désactivé sur dernière page
3. **Tester** : Navigation entre les pages
4. **Vérifier** : Données différentes sur chaque page

#### Bouton "Voir tout"
1. **Cliquer** : Bouton "Voir tout" 
2. **Vérifier** : Redirection vers onglet "Candidatures"
3. **Vérifier** : Tableau complet des candidatures affiché
4. **Retourner** : Onglet "Vue d'ensemble"

### 3. 📅 Section "Entretiens à venir"

#### Affichage des Données Réelles
1. **Vérifier** : Section "Entretiens à venir" visible
2. **Vérifier** : Compteur "(X)" à côté du titre
3. **Vérifier** : Maximum 5 entretiens affichés
4. **Vérifier** : Seulement les entretiens futurs (date > aujourd'hui)
5. **Vérifier** : Tri par date (plus proche en premier)

#### Informations par Entretien
1. **Vérifier** : Nom du candidat
2. **Vérifier** : Type d'entretien (Découverte, Suivi, Final)
3. **Vérifier** : Date et heure formatées (français)
4. **Vérifier** : Badge de statut coloré
5. **Vérifier** : Icône calendrier

#### Boutons d'Action
1. **Bouton "Voir tout"** :
   - Cliquer et vérifier redirection vers onglet "Entretiens"
   - Vérifier affichage de tous les entretiens
2. **Bouton "Planifier"** :
   - Cliquer et vérifier redirection vers onglet "Entretiens"
   - Vérifier ouverture automatique du formulaire de création

#### Cas Aucun Entretien
1. **Si aucun entretien à venir** :
   - Message "Aucun entretien à venir" affiché
   - Icône calendrier grisée
   - Bouton "Planifier un entretien" disponible

### 4. 🔄 Tests de Rafraîchissement

#### Actualisation Automatique
1. **Créer** un nouvel entretien via l'onglet "Entretiens"
2. **Retourner** à "Vue d'ensemble"
3. **Vérifier** : Nouvel entretien apparaît dans la liste
4. **Vérifier** : Compteur mis à jour

#### Actualisation Manuelle
1. **Recharger** la page (F5)
2. **Vérifier** : Données conservées
3. **Vérifier** : Pagination maintenue

## 📊 Résultats Attendus

### Données Affichées
- ✅ **14 candidats** au total (selon test automatique)
- ✅ **10 candidats** max sur vue d'ensemble
- ✅ **2 pages** de pagination
- ✅ **5 entretiens** à venir
- ✅ **Vraies données** de la base PostgreSQL

### Interface Utilisateur
- ✅ **Pagination fonctionnelle** avec boutons
- ✅ **Boutons d'action** opérationnels
- ✅ **Compteurs en temps réel**
- ✅ **Messages informatifs** si pas de données
- ✅ **Design cohérent** avec charte Karma Com

### Navigation
- ✅ **Redirection** vers onglets spécialisés
- ✅ **Ouverture automatique** des formulaires
- ✅ **Retour fluide** à la vue d'ensemble

## 🐛 Problèmes Possibles

### Pagination ne fonctionne pas
**Causes :**
- API candidats inaccessible
- Erreur de calcul des pages
- Problème de state React

**Solutions :**
- Vérifier console navigateur
- Tester API : `curl http://localhost:3000/api/candidates?page=1&limit=10`
- Recharger la page

### Entretiens non affichés
**Causes :**
- Aucun entretien futur en base
- Erreur de filtrage des dates
- API entretiens inaccessible

**Solutions :**
- Créer un entretien avec date future
- Vérifier API : `curl http://localhost:3000/api/interviews`
- Vérifier les logs serveur

### Boutons non fonctionnels
**Causes :**
- Erreur JavaScript
- Problème de navigation React
- État du composant corrompu

**Solutions :**
- Vérifier console navigateur
- Recharger la page
- Tester navigation manuelle

## 🔧 Commandes de Debug

### Vérifier les Données
```bash
# Test automatique complet
npm run test:overview

# API candidats avec pagination
curl "http://localhost:3000/api/candidates?page=1&limit=10"

# API entretiens
curl "http://localhost:3000/api/interviews"

# Statistiques dashboard
curl "http://localhost:3000/api/dashboard/stats"
```

### Base de Données
```sql
-- Compter les candidats
SELECT COUNT(*) FROM "User" WHERE "userType" != 'HR_ADMIN';

-- Entretiens futurs
SELECT COUNT(*) FROM "Appointment" WHERE "scheduledAt" > NOW();

-- Candidatures récentes (10 dernières)
SELECT name, email, "userType", "createdAt" 
FROM "User" 
WHERE "userType" != 'HR_ADMIN' 
ORDER BY "createdAt" DESC 
LIMIT 10;
```

## 📈 Métriques de Performance

### Temps de Chargement
- ⏱️ **Vue d'ensemble** : < 1 seconde
- ⏱️ **Pagination** : < 500ms
- ⏱️ **Navigation onglets** : < 200ms

### Données
- 📊 **Candidatures** : 10/page, pagination automatique
- 📊 **Entretiens** : 5 max, filtrés par date
- 📊 **Statistiques** : Temps réel

## ✅ Checklist de Validation

### Candidatures Récentes
- [ ] Section visible et bien formatée
- [ ] Maximum 10 candidatures affichées
- [ ] Informations complètes (nom, type, statut, date)
- [ ] Pagination fonctionnelle si > 10 candidats
- [ ] Bouton "Voir tout" redirige vers onglet Candidatures
- [ ] Compteur "Affichage de X sur Y" correct

### Entretiens à Venir
- [ ] Section visible avec compteur
- [ ] Seulement entretiens futurs affichés
- [ ] Maximum 5 entretiens, triés par date
- [ ] Informations complètes (candidat, type, date/heure)
- [ ] Bouton "Voir tout" redirige vers onglet Entretiens
- [ ] Bouton "Planifier" ouvre formulaire création
- [ ] Message approprié si aucun entretien

### Navigation et UX
- [ ] Boutons réactifs et bien stylés
- [ ] Transitions fluides entre onglets
- [ ] Données persistantes après navigation
- [ ] Interface responsive (mobile/desktop)
- [ ] Couleurs cohérentes avec charte Karma Com

## 🎉 Résultat Final

Si tous les tests passent :
- ✅ **Vue d'ensemble complète** avec vraies données
- ✅ **Pagination fonctionnelle** pour candidatures
- ✅ **Entretiens futurs** filtrés et triés
- ✅ **Navigation intuitive** vers onglets spécialisés
- ✅ **Interface moderne** et responsive

**La vue d'ensemble du dashboard est prête pour la production !** 🚀
