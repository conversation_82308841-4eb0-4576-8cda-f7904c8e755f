-- Script d'initialisation de la base de données PostgreSQL
-- Ce script sera exécuté automatiquement lors du premier démarrage du conteneur

-- Créer des extensions utiles
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Créer un utilisateur admin par défaut (sera remplacé par les migrations Prisma)
-- Ce script sert principalement à s'assurer que la base est prête
