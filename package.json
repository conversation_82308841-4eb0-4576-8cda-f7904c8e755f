{"name": "karma-com-dashboard", "version": "1.0.0", "description": "Système de gestion d'adhésion pour Karma Com Solidarité", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:init": "node scripts/init-db.js", "db:reset": "prisma migrate reset --force && npm run db:init", "db:seed-constants": "node scripts/seed-constants.js", "db:seed-fake": "node scripts/seed-fake-data.js", "test:inscriptions": "node scripts/test-inscriptions.js", "test:dashboard": "node scripts/test-dashboard.js", "test:auth": "node scripts/test-auth.js", "diagnose": "node scripts/diagnose-error.js", "quick-test": "node scripts/quick-test.js", "test:design": "node scripts/test-design.js", "test:loading": "node scripts/test-dashboard-loading.js", "check:dashboard": "node scripts/check-dashboard-content.js", "test:tabs": "node scripts/test-dashboard-tabs.js", "test:interviews": "node scripts/test-interview-creation.js", "test:overview": "node scripts/test-overview-tab.js", "test:interview-mgmt": "node scripts/test-interview-management.js", "test:interview-style": "node scripts/test-interview-styling.js", "test:candidate-status": "node scripts/test-candidate-status-dropdown.js", "test:docker": "bash scripts/test-docker.sh", "test:docker-compose": "bash scripts/test-docker-compose.sh", "validate:gitlab-ci": "bash scripts/validate-gitlab-ci.sh", "prepare:vps": "bash scripts/prepare-vps-deploy.sh", "deploy:vps": "bash deploy-to-vps.sh", "fix:dashboard": "bash scripts/fix-dashboard.sh", "setup:complete": "bash scripts/setup-complete.sh"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@prisma/client": "^6.11.1", "autoprefixer": "^10.4.16", "bcryptjs": "^2.4.3", "date-fns": "^2.30.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "next": "14.0.4", "postcss": "^8.4.32", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "tailwindcss": "^3.3.6", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.4", "node-fetch": "^2.6.7", "prisma": "^6.11.1", "typescript": "^5"}}