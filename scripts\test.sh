#!/bin/bash

# Script de test pour Karma Com Dashboard
# Ce script vérifie que tous les composants fonctionnent correctement

set -e

echo "🧪 Tests de validation pour Karma Com Dashboard"
echo "==============================================="

# Fonction pour tester une URL
test_url() {
    local url=$1
    local description=$2
    
    echo "🔍 Test: $description"
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200\|302"; then
        echo "✅ $description - OK"
        return 0
    else
        echo "❌ $description - ÉCHEC"
        return 1
    fi
}

# Fonction pour tester une API
test_api() {
    local url=$1
    local method=$2
    local data=$3
    local description=$4
    
    echo "🔍 Test API: $description"
    if [ "$method" = "POST" ]; then
        response=$(curl -s -X POST -H "Content-Type: application/json" -d "$data" "$url" -w "%{http_code}")
    else
        response=$(curl -s "$url" -w "%{http_code}")
    fi
    
    http_code="${response: -3}"
    if [[ "$http_code" =~ ^[2-4][0-9][0-9]$ ]]; then
        echo "✅ $description - OK (HTTP $http_code)"
        return 0
    else
        echo "❌ $description - ÉCHEC (HTTP $http_code)"
        return 1
    fi
}

# Vérifier que l'application est démarrée
echo "🚀 Vérification du démarrage de l'application..."
if ! curl -s http://localhost:3000 > /dev/null; then
    echo "❌ L'application n'est pas accessible sur http://localhost:3000"
    echo "💡 Assurez-vous que l'application est démarrée avec 'npm run dev'"
    exit 1
fi

echo "✅ Application accessible"

# Tests des pages principales
echo ""
echo "📄 Test des pages principales..."
test_url "http://localhost:3000" "Page d'accueil"
test_url "http://localhost:3000/inscription/association" "Formulaire inscription association"
test_url "http://localhost:3000/inscription/organisation" "Formulaire inscription organisation"
test_url "http://localhost:3000/inscription/benevole" "Formulaire inscription bénévole"
test_url "http://localhost:3000/connexion" "Page de connexion"
test_url "http://localhost:3000/contact" "Page de contact"
test_url "http://localhost:3000/dashboard" "Dashboard (peut nécessiter une authentification)"

# Tests des API endpoints
echo ""
echo "🔌 Test des API endpoints..."

# Test de l'API de santé (si elle existe)
# test_api "http://localhost:3000/api/health" "GET" "" "API Health Check"

# Test des API d'inscription (avec des données de test)
test_data_association='{
    "email": "<EMAIL>",
    "password": "testpassword123",
    "confirmPassword": "testpassword123",
    "firstName": "Test",
    "lastName": "Association",
    "organizationName": "Association Test",
    "description": "Description de test pour association",
    "acceptTerms": true,
    "acceptRGPD": true
}'

echo "🔍 Test API inscription association (données invalides attendues)..."
test_api "http://localhost:3000/api/auth/register/association" "POST" "$test_data_association" "API Inscription Association"

# Vérification de la base de données
echo ""
echo "🗄️ Vérification de la base de données..."
if docker ps | grep -q karma-com-postgres; then
    echo "✅ Container PostgreSQL en cours d'exécution"
    
    # Test de connexion à la base de données
    if docker exec karma-com-postgres pg_isready -U karma_user > /dev/null 2>&1; then
        echo "✅ Base de données PostgreSQL accessible"
    else
        echo "❌ Base de données PostgreSQL non accessible"
    fi
else
    echo "❌ Container PostgreSQL non trouvé"
fi

# Vérification de pgAdmin
echo ""
echo "🔧 Vérification de pgAdmin..."
if curl -s http://localhost:5050 > /dev/null; then
    echo "✅ pgAdmin accessible sur http://localhost:5050"
else
    echo "❌ pgAdmin non accessible"
fi

# Tests de performance basiques
echo ""
echo "⚡ Tests de performance basiques..."
echo "🔍 Temps de réponse de la page d'accueil..."
response_time=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:3000)
echo "⏱️ Temps de réponse: ${response_time}s"

if (( $(echo "$response_time < 2.0" | bc -l) )); then
    echo "✅ Performance acceptable (< 2s)"
else
    echo "⚠️ Performance lente (> 2s)"
fi

# Vérification des fichiers critiques
echo ""
echo "📁 Vérification des fichiers critiques..."
critical_files=(
    "package.json"
    "next.config.js"
    "tailwind.config.js"
    "prisma/schema.prisma"
    "docker-compose.yml"
    ".env"
    "src/app/page.tsx"
    "src/app/layout.tsx"
)

for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file manquant"
    fi
done

# Résumé
echo ""
echo "📊 Résumé des tests"
echo "==================="
echo "✅ Tests terminés"
echo ""
echo "💡 Conseils :"
echo "  • Vérifiez les logs de l'application si des tests échouent"
echo "  • Assurez-vous que tous les services Docker sont démarrés"
echo "  • Consultez le README.md pour plus d'informations"
echo ""
echo "🔗 Liens utiles :"
echo "  • Application: http://localhost:3000"
echo "  • pgAdmin: http://localhost:5050"
echo "  • Documentation: README.md"
