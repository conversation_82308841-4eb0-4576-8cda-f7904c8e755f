export interface RecentApplication {
  id: string
  name: string
  email: string
  type: string
  organization: string | null
  status: string
  date: string
}

export interface DashboardStats {
  totalCandidates: number
  pendingApplications: number
  scheduledInterviews: number
  activeMembers: number
}

export interface DashboardData {
  overview: DashboardStats
  recentApplications: RecentApplication[]
}
