const { PrismaClient } = require('@prisma/client')

async function diagnosePrismaConnection() {
  console.log('🔍 Diagnostic de la connexion Prisma')
  console.log('===================================')
  
  let prisma
  
  try {
    // Tenter de créer une instance Prisma
    console.log('📦 Création de l\'instance Prisma...')
    prisma = new PrismaClient()
    console.log('✅ Instance Prisma créée')
    
    // Tester la connexion
    console.log('🔌 Test de connexion à la base de données...')
    await prisma.$connect()
    console.log('✅ Connexion à la base de données réussie')
    
    // Tester une requête simple
    console.log('📊 Test d\'une requête simple...')
    const userCount = await prisma.user.count()
    console.log(`✅ Requête réussie - ${userCount} utilisateurs trouvés`)
    
    // Tester les différents types d'utilisateurs
    console.log('👥 Test des types d\'utilisateurs...')
    const userTypes = await prisma.user.groupBy({
      by: ['userType'],
      _count: { id: true }
    })
    
    console.log('✅ Types d\'utilisateurs:')
    userTypes.forEach(type => {
      console.log(`   ${type.userType}: ${type._count.id}`)
    })
    
    // Tester les profils
    console.log('📋 Test des profils...')
    const profileCount = await prisma.profile.count()
    console.log(`✅ ${profileCount} profils trouvés`)
    
    // Tester les statuts de membership
    console.log('📈 Test des statuts de membership...')
    const statusStats = await prisma.profile.groupBy({
      by: ['membershipStatus'],
      _count: { id: true }
    })
    
    console.log('✅ Statuts de membership:')
    statusStats.forEach(status => {
      console.log(`   ${status.membershipStatus}: ${status._count.id}`)
    })
    
    console.log('\n🎉 Tous les tests Prisma sont passés!')
    return true
    
  } catch (error) {
    console.log('\n❌ Erreur détectée:')
    console.log(`   Type: ${error.constructor.name}`)
    console.log(`   Message: ${error.message}`)
    
    if (error.code) {
      console.log(`   Code: ${error.code}`)
    }
    
    if (error.meta) {
      console.log(`   Meta: ${JSON.stringify(error.meta, null, 2)}`)
    }
    
    console.log('\n💡 Solutions possibles:')
    
    if (error.message.includes('Environment variable not found')) {
      console.log('   • Vérifiez que le fichier .env existe et contient DATABASE_URL')
      console.log('   • Vérifiez que DATABASE_URL pointe vers le bon port (5434)')
    }
    
    if (error.message.includes('Can\'t reach database server')) {
      console.log('   • Vérifiez que PostgreSQL est démarré: docker-compose up -d postgres')
      console.log('   • Vérifiez que le port 5434 est accessible')
    }
    
    if (error.message.includes('Unknown argument')) {
      console.log('   • Régénérez le client Prisma: npx prisma generate')
      console.log('   • Appliquez les migrations: npx prisma migrate dev')
    }
    
    if (error.message.includes('Table') && error.message.includes('does not exist')) {
      console.log('   • Appliquez les migrations: npx prisma migrate dev')
      console.log('   • Ou réinitialisez la base: npm run db:reset')
    }
    
    return false
    
  } finally {
    if (prisma) {
      await prisma.$disconnect()
    }
  }
}

async function testDashboardAPI() {
  console.log('\n🌐 Test de l\'API Dashboard')
  console.log('===========================')
  
  try {
    const fetch = require('node-fetch')
    const response = await fetch('http://localhost:3000/api/dashboard/stats')
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ API Dashboard fonctionne')
      console.log(`   Total candidats: ${data.overview?.totalCandidates || 'N/A'}`)
      return true
    } else {
      const errorText = await response.text()
      console.log(`❌ API Dashboard erreur ${response.status}`)
      console.log(`   Réponse: ${errorText}`)
      return false
    }
  } catch (error) {
    console.log('❌ Erreur lors du test API:', error.message)
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 L\'application n\'est pas démarrée. Lancez: npm run dev')
    }
    
    return false
  }
}

async function checkEnvironment() {
  console.log('\n🔧 Vérification de l\'environnement')
  console.log('===================================')
  
  // Vérifier les variables d'environnement
  console.log('📋 Variables d\'environnement:')
  console.log(`   DATABASE_URL: ${process.env.DATABASE_URL ? '✅ Définie' : '❌ Manquante'}`)
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'development'}`)
  
  // Vérifier les fichiers importants
  const fs = require('fs')
  const files = [
    '.env',
    'prisma/schema.prisma',
    'src/lib/prisma.ts'
  ]
  
  console.log('\n📁 Fichiers importants:')
  files.forEach(file => {
    const exists = fs.existsSync(file)
    console.log(`   ${file}: ${exists ? '✅ Existe' : '❌ Manquant'}`)
  })
  
  // Vérifier node_modules
  const nodeModulesExists = fs.existsSync('node_modules/@prisma/client')
  console.log(`   node_modules/@prisma/client: ${nodeModulesExists ? '✅ Existe' : '❌ Manquant'}`)
  
  if (!nodeModulesExists) {
    console.log('\n💡 Le client Prisma n\'est pas installé. Lancez:')
    console.log('   npm install')
    console.log('   npx prisma generate')
  }
}

async function main() {
  console.log('🚨 Diagnostic des erreurs Dashboard')
  console.log('====================================')
  
  // Vérifier l'environnement
  await checkEnvironment()
  
  // Tester Prisma
  const prismaOk = await diagnosePrismaConnection()
  
  // Tester l'API si Prisma fonctionne
  if (prismaOk) {
    await testDashboardAPI()
  }
  
  console.log('\n📋 Résumé du diagnostic')
  console.log('=======================')
  
  if (prismaOk) {
    console.log('✅ Base de données: OK')
    console.log('💡 Si l\'erreur persiste, redémarrez l\'application: npm run dev')
  } else {
    console.log('❌ Base de données: Problème détecté')
    console.log('💡 Suivez les solutions proposées ci-dessus')
  }
}

main().catch(console.error)
