#!/bin/bash

# Script pour mettre à jour le branding (logo et favicon)
echo "🎨 Mise à Jour Branding - Karma Com Solidarité"
echo "=============================================="

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
log "Vérification des prérequis..."

if [ ! -f "package.json" ]; then
    error "Ce script doit être exécuté depuis la racine du projet"
    exit 1
fi

if [ ! -d "public" ]; then
    error "Répertoire public non trouvé"
    exit 1
fi

success "Prérequis validés"

# Mise à jour du logo
log "Mise à jour du logo..."

# Vérifier si le nouveau logo existe
if [ -f "public/Logo.svg" ]; then
    success "Nouveau logo trouvé dans public/Logo.svg"
else
    warning "Logo non trouvé dans public/Logo.svg"
fi

# Vérifier si le composant Logo utilise le bon chemin
if grep -q 'src="/Logo.svg"' src/components/ui/Logo.tsx; then
    success "Composant Logo configuré pour utiliser /Logo.svg"
else
    warning "Composant Logo pourrait nécessiter une mise à jour"
fi

# Mise à jour du favicon
log "Mise à jour du favicon..."

# Copier le nouveau favicon si disponible
if [ -f "public/favicon.ico" ]; then
    success "Favicon trouvé dans public/favicon.ico"
    
    # Créer une sauvegarde de l'ancien favicon
    if [ -f "public/favicon.ico.backup" ]; then
        log "Sauvegarde du favicon existante"
    else
        cp public/favicon.ico public/favicon.ico.backup
        success "Sauvegarde de l'ancien favicon créée"
    fi
else
    warning "Favicon non trouvé dans public/favicon.ico"
fi

# Vérification des métadonnées
log "Vérification des métadonnées..."

if grep -q "Karma Com Solidarité" src/app/layout.tsx; then
    success "Métadonnées du site configurées"
else
    warning "Métadonnées du site pourraient nécessiter une mise à jour"
fi

# Nettoyage des anciens assets
log "Nettoyage des anciens assets..."

old_assets=(
    "public/Logo horizontal 1.svg"
    "public/Favicon.png"
    "public/Favicon.svg"
    "public/Favicon dark.svg"
    "src/app/public/Logo 1 - SVG – 3.svg"
    "src/app/public/Logo arche.svg"
    "src/app/public/Logo horizontal 1.svg"
    "src/app/public/file.svg"
    "src/app/public/globe.svg"
    "src/app/public/next.svg"
    "src/app/public/vercel.svg"
    "src/app/public/window.svg"
)

for asset in "${old_assets[@]}"; do
    if [ -f "$asset" ]; then
        log "Suppression de l'ancien asset: $asset"
        rm -f "$asset"
        success "Asset supprimé: $asset"
    fi
done

# Vérification des imports dans les composants
log "Vérification des imports dans les composants..."

components_to_check=(
    "src/components/ui/Logo.tsx"
    "src/components/layout/Header.tsx"
    "src/components/layout/Footer.tsx"
    "src/app/page.tsx"
    "src/app/dashboard/page.tsx"
)

for component in "${components_to_check[@]}"; do
    if [ -f "$component" ]; then
        if grep -q "Logo" "$component"; then
            success "Composant $component utilise le Logo"
        fi
    fi
done

# Test de build pour vérifier que tout fonctionne
log "Test de build pour vérifier les assets..."

export DATABASE_URL="***********************************/dummy"
export NODE_ENV="production"
export NEXT_TELEMETRY_DISABLED=1

if npm run build > /dev/null 2>&1; then
    success "Build réussi avec les nouveaux assets"
else
    warning "Build échoué, vérifiez les chemins des assets"
fi

# Génération d'un favicon à partir du logo SVG (optionnel)
log "Génération d'assets supplémentaires..."

if command -v convert &> /dev/null; then
    log "ImageMagick détecté, génération d'assets supplémentaires..."
    
    # Générer favicon-16x16.png
    if [ -f "public/Logo.svg" ]; then
        convert public/Logo.svg -resize 16x16 public/favicon-16x16.png 2>/dev/null && success "favicon-16x16.png généré"
        convert public/Logo.svg -resize 32x32 public/favicon-32x32.png 2>/dev/null && success "favicon-32x32.png généré"
        convert public/Logo.svg -resize 192x192 public/android-chrome-192x192.png 2>/dev/null && success "android-chrome-192x192.png généré"
        convert public/Logo.svg -resize 512x512 public/android-chrome-512x512.png 2>/dev/null && success "android-chrome-512x512.png généré"
    fi
else
    log "ImageMagick non disponible, génération d'assets supplémentaires ignorée"
fi

# Mise à jour du manifest.json (optionnel)
log "Création du manifest.json..."

cat > public/manifest.json << 'EOF'
{
  "name": "Karma Com Solidarité",
  "short_name": "Karma Com",
  "description": "Plateforme de gestion d'adhésion pour associations, organisations et bénévoles",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#1e3a5f",
  "icons": [
    {
      "src": "/android-chrome-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/android-chrome-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
EOF

success "manifest.json créé"

# Résumé des modifications
echo ""
log "Résumé des Modifications Branding"
echo "=================================="

echo ""
echo "✅ Modifications appliquées:"
echo "   - Logo mis à jour vers /public/Logo.svg"
echo "   - Composant Logo configuré pour utiliser Image de Next.js"
echo "   - Favicon vérifié dans /public/favicon.ico"
echo "   - Anciens assets nettoyés"
echo "   - manifest.json créé"
echo ""
echo "🎨 Assets principaux:"
echo "   - Logo: /public/Logo.svg"
echo "   - Favicon: /public/favicon.ico"
echo "   - Manifest: /public/manifest.json"
echo ""
echo "🔧 Composants mis à jour:"
echo "   - src/components/ui/Logo.tsx (utilise Image de Next.js)"
echo "   - Tous les composants utilisant Logo sont compatibles"
echo ""
echo "📊 Métadonnées:"
echo "   - Titre: Karma Com Solidarité - Gestion d'Adhésion"
echo "   - Description: Plateforme de gestion d'adhésion"
echo "   - Thème: #1e3a5f (bleu foncé)"
echo ""
echo "🧪 Tests recommandés:"
echo "   1. npm run dev (vérifier le logo dans l'interface)"
echo "   2. npm run build (vérifier que les assets sont correctement inclus)"
echo "   3. Tester sur différentes tailles d'écran"
echo ""

success "Mise à jour du branding terminée"
