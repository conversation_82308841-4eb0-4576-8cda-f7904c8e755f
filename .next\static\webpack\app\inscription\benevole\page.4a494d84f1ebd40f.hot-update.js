"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inscription/benevole/page",{

/***/ "(app-pages-browser)/./src/app/inscription/benevole/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/inscription/benevole/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BenevoleInscriptionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BenevoleInscriptionPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Informations de contact\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        // Informations personnelles\n        firstName: \"\",\n        lastName: \"\",\n        phone: \"\",\n        dateOfBirth: \"\",\n        // Adresse\n        address: \"\",\n        city: \"\",\n        postalCode: \"\",\n        country: \"France\",\n        // Compétences et disponibilités\n        skills: [],\n        customSkills: \"\",\n        availability: \"\",\n        experience: \"\",\n        motivation: \"\",\n        // Préférences\n        preferredDepartments: [],\n        remoteWork: false,\n        // Conditions\n        acceptTerms: false,\n        acceptRGPD: false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const availableSkills = [\n        \"Communication\",\n        \"Marketing digital\",\n        \"D\\xe9veloppement web\",\n        \"Design graphique\",\n        \"Gestion de projet\",\n        \"Comptabilit\\xe9\",\n        \"Juridique\",\n        \"Traduction\",\n        \"R\\xe9daction\",\n        \"Photographie\",\n        \"Vid\\xe9o\",\n        \"Formation\",\n        \"\\xc9v\\xe9nementiel\",\n        \"Logistique\",\n        \"Autre\"\n    ];\n    const departments = [\n        \"RH/Asso\",\n        \"Communication\",\n        \"Partenariats & RSE\",\n        \"IT\",\n        \"Design\",\n        \"Juridique\",\n        \"Comptabilit\\xe9\",\n        \"Auto & AI\"\n    ];\n    const availabilityOptions = [\n        \"Quelques heures par semaine\",\n        \"Une demi-journ\\xe9e par semaine\",\n        \"Une journ\\xe9e par semaine\",\n        \"Plusieurs jours par semaine\",\n        \"Ponctuellement selon les projets\",\n        \"Disponibilit\\xe9 flexible\"\n    ];\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === \"checkbox\" ? e.target.checked : value\n            }));\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n    };\n    const handleSkillChange = (skill)=>{\n        setFormData((prev)=>({\n                ...prev,\n                skills: prev.skills.includes(skill) ? prev.skills.filter((s)=>s !== skill) : [\n                    ...prev.skills,\n                    skill\n                ]\n            }));\n    };\n    const handleDepartmentChange = (department)=>{\n        setFormData((prev)=>({\n                ...prev,\n                preferredDepartments: prev.preferredDepartments.includes(department) ? prev.preferredDepartments.filter((d)=>d !== department) : [\n                    ...prev.preferredDepartments,\n                    department\n                ]\n            }));\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Validation email\n        if (!formData.email) {\n            newErrors.email = \"L'email est requis\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Format d'email invalide\";\n        }\n        // Validation mot de passe\n        if (!formData.password) {\n            newErrors.password = \"Le mot de passe est requis\";\n        } else if (formData.password.length < 8) {\n            newErrors.password = \"Le mot de passe doit contenir au moins 8 caract\\xe8res\";\n        }\n        if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = \"Les mots de passe ne correspondent pas\";\n        }\n        // Validation champs requis\n        const requiredFields = [\n            \"firstName\",\n            \"lastName\",\n            \"motivation\"\n        ];\n        requiredFields.forEach((field)=>{\n            if (!formData[field]) {\n                newErrors[field] = \"Ce champ est requis\";\n            }\n        });\n        // Validation date de naissance\n        if (formData.dateOfBirth) {\n            const birthDate = new Date(formData.dateOfBirth);\n            const today = new Date();\n            const age = today.getFullYear() - birthDate.getFullYear();\n            if (age < 16) {\n                newErrors.dateOfBirth = \"Vous devez avoir au moins 16 ans\";\n            }\n        }\n        // Validation acceptation des conditions\n        if (!formData.acceptTerms) {\n            newErrors.acceptTerms = \"Vous devez accepter les conditions d'utilisation\";\n        }\n        if (!formData.acceptRGPD) {\n            newErrors.acceptRGPD = \"Vous devez accepter la politique de confidentialit\\xe9\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"/api/auth/register/volunteer\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (response.ok) {\n                alert(\"Inscription r\\xe9ussie ! Vous recevrez un email de confirmation.\");\n                // Réinitialiser le formulaire\n                setFormData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    firstName: \"\",\n                    lastName: \"\",\n                    phone: \"\",\n                    dateOfBirth: \"\",\n                    address: \"\",\n                    city: \"\",\n                    postalCode: \"\",\n                    country: \"France\",\n                    skills: [],\n                    customSkills: \"\",\n                    availability: \"\",\n                    experience: \"\",\n                    motivation: \"\",\n                    preferredDepartments: [],\n                    remoteWork: false,\n                    acceptTerms: false,\n                    acceptRGPD: false\n                });\n            } else {\n                // Afficher les erreurs de validation\n                if (data.details) {\n                    const newErrors = {};\n                    data.details.forEach((error)=>{\n                        if (error.path && error.path.length > 0) {\n                            newErrors[error.path[0]] = error.message;\n                        }\n                    });\n                    setErrors(newErrors);\n                } else {\n                    alert(data.error || \"Une erreur est survenue lors de l'inscription.\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Erreur lors de l'inscription:\", error);\n            alert(\"Une erreur est survenue. Veuillez r\\xe9essayer.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-karma py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"text-white\",\n                                        size: 32\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Devenir B\\xe9n\\xe9vole\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Rejoignez notre \\xe9quipe de b\\xe9n\\xe9voles et contribuez \\xe0 des projets solidaires\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Informations de connexion\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Email *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            name: \"email\",\n                                                            value: formData.email,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.email ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 36\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"T\\xe9l\\xe9phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            name: \"phone\",\n                                                            value: formData.phone,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"+33 1 23 45 67 89\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Mot de passe *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            name: \"password\",\n                                                            value: formData.password,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.password ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"Minimum 8 caract\\xe8res\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.password\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 39\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Confirmer le mot de passe *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            name: \"confirmPassword\",\n                                                            value: formData.confirmPassword,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.confirmPassword ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"R\\xe9p\\xe9tez le mot de passe\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.confirmPassword\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 46\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Informations personnelles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Pr\\xe9nom *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"firstName\",\n                                                            value: formData.firstName,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.firstName ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"Votre pr\\xe9nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.firstName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 40\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Nom *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"lastName\",\n                                                            value: formData.lastName,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.lastName ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"Votre nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.lastName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 39\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Date de naissance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            name: \"dateOfBirth\",\n                                                            value: formData.dateOfBirth,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.dateOfBirth ? \"border-red-500\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.dateOfBirth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.dateOfBirth\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 42\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Adresse\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Adresse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"address\",\n                                                            value: formData.address,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"Num\\xe9ro et nom de rue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Ville\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"city\",\n                                                                    value: formData.city,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    placeholder: \"Ville\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Code postal\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"postalCode\",\n                                                                    value: formData.postalCode,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    placeholder: \"75001\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Pays\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    name: \"country\",\n                                                                    value: formData.country,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"France\",\n                                                                            children: \"France\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 408,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Belgique\",\n                                                                            children: \"Belgique\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Suisse\",\n                                                                            children: \"Suisse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Canada\",\n                                                                            children: \"Canada\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Autre\",\n                                                                            children: \"Autre\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 412,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Comp\\xe9tences et exp\\xe9rience\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Comp\\xe9tences (s\\xe9lectionnez toutes celles qui s'appliquent)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 md:grid-cols-3 gap-3 mt-2\",\n                                                            children: availableSkills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-2 cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: formData.skills.includes(skill),\n                                                                            onChange: ()=>handleSkillChange(skill),\n                                                                            className: \"rounded border-gray-300 text-accent-600 focus:ring-accent-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: skill\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, skill, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Autres comp\\xe9tences\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"customSkills\",\n                                                            value: formData.customSkills,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"D\\xe9crivez d'autres comp\\xe9tences non list\\xe9es...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Exp\\xe9rience en b\\xe9n\\xe9volat\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"experience\",\n                                                            value: formData.experience,\n                                                            onChange: handleInputChange,\n                                                            rows: 3,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"D\\xe9crivez votre exp\\xe9rience en b\\xe9n\\xe9volat ou dans des projets similaires...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Disponibilit\\xe9s et pr\\xe9f\\xe9rences\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Disponibilit\\xe9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"availability\",\n                                                            value: formData.availability,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"S\\xe9lectionnez votre disponibilit\\xe9\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                availabilityOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: option,\n                                                                        children: option\n                                                                    }, option, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"P\\xf4les d'int\\xe9r\\xeat (s\\xe9lectionnez ceux qui vous int\\xe9ressent)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-3 mt-2\",\n                                                            children: departments.map((dept)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-2 cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: formData.preferredDepartments.includes(dept),\n                                                                            onChange: ()=>handleDepartmentChange(dept),\n                                                                            className: \"rounded border-gray-300 text-accent-600 focus:ring-accent-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: dept\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, dept, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            name: \"remoteWork\",\n                                                            checked: formData.remoteWork,\n                                                            onChange: handleInputChange,\n                                                            className: \"rounded border-gray-300 text-accent-600 focus:ring-accent-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: \"Je suis disponible pour du travail \\xe0 distance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Motivation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"karma-label\",\n                                                    children: \"Pourquoi souhaitez-vous devenir b\\xe9n\\xe9vole chez Karma Com ? *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    name: \"motivation\",\n                                                    value: formData.motivation,\n                                                    onChange: handleInputChange,\n                                                    rows: 4,\n                                                    className: \"karma-input \".concat(errors.motivation ? \"border-red-500\" : \"\"),\n                                                    placeholder: \"Expliquez vos motivations et ce que vous esp\\xe9rez apporter \\xe0 notre association...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.motivation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm mt-1\",\n                                                    children: errors.motivation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 39\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        name: \"acceptTerms\",\n                                                        checked: formData.acceptTerms,\n                                                        onChange: handleInputChange,\n                                                        className: \"mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: [\n                                                            \"J'accepte les\",\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/conditions\",\n                                                                className: \"text-accent-600 hover:underline\",\n                                                                children: \"conditions d'utilisation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"de Karma Com Solidarit\\xe9 *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.acceptTerms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: errors.acceptTerms\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 40\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        name: \"acceptRGPD\",\n                                                        checked: formData.acceptRGPD,\n                                                        onChange: handleInputChange,\n                                                        className: \"mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: [\n                                                            \"J'accepte la\",\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/politique-confidentialite\",\n                                                                className: \"text-accent-600 hover:underline\",\n                                                                children: \"politique de confidentialit\\xe9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"et le traitement de mes donn\\xe9es personnelles selon le RGPD *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.acceptRGPD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: errors.acceptRGPD\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 39\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"bg-gradient-to-r from-accent-600 to-accent-700 text-white font-medium py-3 px-12 rounded-lg hover:from-accent-700 hover:to-accent-800 transition-all duration-200 transform hover:scale-105 text-lg \".concat(isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                                        children: isSubmitting ? \"Inscription en cours...\" : \"Rejoindre l'\\xe9quipe\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                lineNumber: 602,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, this);\n}\n_s(BenevoleInscriptionPage, \"tg1ceH2vs8LTtrjsBpOHp/ohRaY=\");\n_c = BenevoleInscriptionPage;\nvar _c;\n$RefreshReg$(_c, \"BenevoleInscriptionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inscription/benevole/page.tsx\n"));

/***/ })

});