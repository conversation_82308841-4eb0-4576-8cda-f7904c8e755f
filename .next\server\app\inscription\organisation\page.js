/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/inscription/organisation/page";
exports.ids = ["app/inscription/organisation/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finscription%2Forganisation%2Fpage&page=%2Finscription%2Forganisation%2Fpage&appPaths=%2Finscription%2Forganisation%2Fpage&pagePath=private-next-app-dir%2Finscription%2Forganisation%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finscription%2Forganisation%2Fpage&page=%2Finscription%2Forganisation%2Fpage&appPaths=%2Finscription%2Forganisation%2Fpage&pagePath=private-next-app-dir%2Finscription%2Forganisation%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'inscription',\n        {\n        children: [\n        'organisation',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/inscription/organisation/page.tsx */ \"(rsc)/./src/app/inscription/organisation/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/inscription/organisation/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/inscription/organisation/page\",\n        pathname: \"/inscription/organisation\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finscription%2Forganisation%2Fpage&page=%2Finscription%2Forganisation%2Fpage&appPaths=%2Finscription%2Forganisation%2Fpage&pagePath=private-next-app-dir%2Finscription%2Forganisation%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cinscription%5Corganisation%5Cpage.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cinscription%5Corganisation%5Cpage.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/inscription/organisation/page.tsx */ \"(ssr)/./src/app/inscription/organisation/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDaGFtemEuYmVkb3VpJTVDRG9jdW1lbnRzJTVDbWVzRG9jcyU1Q0FJJTVDS0NTJTVDYXVnbWVudC1rY3MlNUNzcmMlNUNhcHAlNUNpbnNjcmlwdGlvbiU1Q29yZ2FuaXNhdGlvbiU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2thcm1hLWNvbS1kYXNoYm9hcmQvPzgxN2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxoYW16YS5iZWRvdWlcXFxcRG9jdW1lbnRzXFxcXG1lc0RvY3NcXFxcQUlcXFxcS0NTXFxcXGF1Z21lbnQta2NzXFxcXHNyY1xcXFxhcHBcXFxcaW5zY3JpcHRpb25cXFxcb3JnYW5pc2F0aW9uXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cinscription%5Corganisation%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/inscription/organisation/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/inscription/organisation/page.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrganisationInscriptionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _hooks_useConstants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useConstants */ \"(ssr)/./src/hooks/useConstants.ts\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction OrganisationInscriptionPage() {\n    const { constants, loading: constantsLoading } = (0,_hooks_useConstants__WEBPACK_IMPORTED_MODULE_4__.useConstants)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Informations de contact\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        // Informations personnelles du représentant\n        firstName: \"\",\n        lastName: \"\",\n        phone: \"\",\n        // Informations de l'organisation\n        organizationName: \"\",\n        siret: \"\",\n        website: \"\",\n        description: \"\",\n        organizationType: \"\",\n        sector: \"\",\n        employeeCount: \"\",\n        // Adresse\n        address: \"\",\n        city: \"\",\n        postalCode: \"\",\n        country: \"France\",\n        // Partenariat\n        partnershipType: \"\",\n        motivation: \"\",\n        budget: \"\",\n        // Conditions\n        acceptTerms: false,\n        acceptRGPD: false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Utiliser les constantes de la base de données ou des valeurs par défaut\n    const organizationTypes = constants?.organizationTypes || [];\n    const sectors = constants?.sectors || [];\n    const partnershipTypes = constants?.partnershipTypes || [];\n    const employeeCountOptions = constants?.employeeCountOptions || [];\n    const budgetOptions = constants?.budgetOptions || [];\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === \"checkbox\" ? e.target.checked : value\n            }));\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Validation email\n        if (!formData.email) {\n            newErrors.email = \"L'email est requis\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Format d'email invalide\";\n        }\n        // Validation mot de passe\n        if (!formData.password) {\n            newErrors.password = \"Le mot de passe est requis\";\n        } else if (formData.password.length < 8) {\n            newErrors.password = \"Le mot de passe doit contenir au moins 8 caract\\xe8res\";\n        }\n        if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = \"Les mots de passe ne correspondent pas\";\n        }\n        // Validation champs requis\n        const requiredFields = [\n            \"firstName\",\n            \"lastName\",\n            \"organizationName\",\n            \"organizationType\",\n            \"description\"\n        ];\n        requiredFields.forEach((field)=>{\n            if (!formData[field]) {\n                newErrors[field] = \"Ce champ est requis\";\n            }\n        });\n        // Validation SIRET\n        if (formData.siret && !/^\\d{14}$/.test(formData.siret)) {\n            newErrors.siret = \"Le SIRET doit contenir 14 chiffres\";\n        }\n        // Validation acceptation des conditions\n        if (!formData.acceptTerms) {\n            newErrors.acceptTerms = \"Vous devez accepter les conditions d'utilisation\";\n        }\n        if (!formData.acceptRGPD) {\n            newErrors.acceptRGPD = \"Vous devez accepter la politique de confidentialit\\xe9\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"/api/auth/register/organization\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (response.ok) {\n                alert(\"Inscription r\\xe9ussie ! Vous recevrez un email de confirmation.\");\n                // Réinitialiser le formulaire\n                setFormData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    firstName: \"\",\n                    lastName: \"\",\n                    phone: \"\",\n                    organizationName: \"\",\n                    siret: \"\",\n                    website: \"\",\n                    description: \"\",\n                    organizationType: \"\",\n                    sector: \"\",\n                    employeeCount: \"\",\n                    address: \"\",\n                    city: \"\",\n                    postalCode: \"\",\n                    country: \"France\",\n                    partnershipType: \"\",\n                    motivation: \"\",\n                    budget: \"\",\n                    acceptTerms: false,\n                    acceptRGPD: false\n                });\n            } else {\n                // Afficher les erreurs de validation\n                if (data.details) {\n                    const newErrors = {};\n                    data.details.forEach((error)=>{\n                        if (error.path && error.path.length > 0) {\n                            newErrors[error.path[0]] = error.message;\n                        }\n                    });\n                    setErrors(newErrors);\n                } else {\n                    alert(data.error || \"Une erreur est survenue lors de l'inscription.\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Erreur lors de l'inscription:\", error);\n            alert(\"Une erreur est survenue. Veuillez r\\xe9essayer.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-karma py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"text-white\",\n                                        size: 32\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Inscription Organisation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Entreprises et institutions, rejoignez notre r\\xe9seau de partenaires engag\\xe9s\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            constantsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-secondary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"Chargement des options...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"form-title flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"mr-3 text-secondary-600\",\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Informations de connexion\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Email *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                name: \"email\",\n                                                                value: formData.email,\n                                                                onChange: handleInputChange,\n                                                                className: `karma-input ${errors.email ? \"border-red-500\" : \"\"}`,\n                                                                placeholder: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: errors.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 36\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"T\\xe9l\\xe9phone\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"tel\",\n                                                                name: \"phone\",\n                                                                value: formData.phone,\n                                                                onChange: handleInputChange,\n                                                                className: \"karma-input\",\n                                                                placeholder: \"+33 1 23 45 67 89\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Mot de passe *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"password\",\n                                                                name: \"password\",\n                                                                value: formData.password,\n                                                                onChange: handleInputChange,\n                                                                className: `karma-input ${errors.password ? \"border-red-500\" : \"\"}`,\n                                                                placeholder: \"Minimum 8 caract\\xe8res\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: errors.password\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 39\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Confirmer le mot de passe *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"password\",\n                                                                name: \"confirmPassword\",\n                                                                value: formData.confirmPassword,\n                                                                onChange: handleInputChange,\n                                                                className: `karma-input ${errors.confirmPassword ? \"border-red-500\" : \"\"}`,\n                                                                placeholder: \"R\\xe9p\\xe9tez le mot de passe\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: errors.confirmPassword\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 46\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"form-title flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"mr-3 text-secondary-600\",\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Repr\\xe9sentant de l'organisation\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Pr\\xe9nom *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"firstName\",\n                                                                value: formData.firstName,\n                                                                onChange: handleInputChange,\n                                                                className: `karma-input ${errors.firstName ? \"border-red-500\" : \"\"}`,\n                                                                placeholder: \"Votre pr\\xe9nom\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: errors.firstName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 40\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Nom *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"lastName\",\n                                                                value: formData.lastName,\n                                                                onChange: handleInputChange,\n                                                                className: `karma-input ${errors.lastName ? \"border-red-500\" : \"\"}`,\n                                                                placeholder: \"Votre nom\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: errors.lastName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 39\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"form-title flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"mr-3 text-secondary-600\",\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Informations de l'organisation\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Nom de l'organisation *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"organizationName\",\n                                                                value: formData.organizationName,\n                                                                onChange: handleInputChange,\n                                                                className: `karma-input ${errors.organizationName ? \"border-red-500\" : \"\"}`,\n                                                                placeholder: \"Nom officiel de votre organisation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.organizationName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: errors.organizationName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 47\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"karma-label\",\n                                                                        children: \"Type d'organisation *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        name: \"organizationType\",\n                                                                        value: formData.organizationType,\n                                                                        onChange: handleInputChange,\n                                                                        className: `karma-input ${errors.organizationType ? \"border-red-500\" : \"\"}`,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"S\\xe9lectionnez un type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 336,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            organizationTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: type.name,\n                                                                                    children: type.name\n                                                                                }, type.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                    lineNumber: 338,\n                                                                                    columnNumber: 25\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    errors.organizationType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-sm mt-1\",\n                                                                        children: errors.organizationType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"karma-label\",\n                                                                        children: \"Secteur d'activit\\xe9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        name: \"sector\",\n                                                                        value: formData.sector,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"karma-input\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"S\\xe9lectionnez un secteur\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            sectors.map((sector)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: sector.name,\n                                                                                    children: sector.name\n                                                                                }, sector.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                    lineNumber: 353,\n                                                                                    columnNumber: 25\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"karma-label\",\n                                                                        children: \"SIRET\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        name: \"siret\",\n                                                                        value: formData.siret,\n                                                                        onChange: handleInputChange,\n                                                                        className: `karma-input ${errors.siret ? \"border-red-500\" : \"\"}`,\n                                                                        placeholder: \"14 chiffres\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    errors.siret && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-sm mt-1\",\n                                                                        children: errors.siret\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 38\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"karma-label\",\n                                                                        children: \"Site web\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"url\",\n                                                                        name: \"website\",\n                                                                        value: formData.website,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"karma-input\",\n                                                                        placeholder: \"https://votre-site.com\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"karma-label\",\n                                                                        children: \"Nombre d'employ\\xe9s\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        name: \"employeeCount\",\n                                                                        value: formData.employeeCount,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"karma-input\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"S\\xe9lectionnez\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 391,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            employeeCountOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: option,\n                                                                                    children: option\n                                                                                }, option, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                    lineNumber: 393,\n                                                                                    columnNumber: 25\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Description de l'organisation *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                name: \"description\",\n                                                                value: formData.description,\n                                                                onChange: handleInputChange,\n                                                                rows: 4,\n                                                                className: `karma-input ${errors.description ? \"border-red-500\" : \"\"}`,\n                                                                placeholder: \"D\\xe9crivez les activit\\xe9s et missions de votre organisation...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: errors.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"form-title flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mr-3 text-secondary-600\",\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Adresse\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Adresse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"address\",\n                                                                value: formData.address,\n                                                                onChange: handleInputChange,\n                                                                className: \"karma-input\",\n                                                                placeholder: \"Num\\xe9ro et nom de rue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"karma-label\",\n                                                                        children: \"Ville\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        name: \"city\",\n                                                                        value: formData.city,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"karma-input\",\n                                                                        placeholder: \"Ville\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"karma-label\",\n                                                                        children: \"Code postal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        name: \"postalCode\",\n                                                                        value: formData.postalCode,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"karma-input\",\n                                                                        placeholder: \"75001\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"karma-label\",\n                                                                        children: \"Pays\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        name: \"country\",\n                                                                        value: formData.country,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"karma-input\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"France\",\n                                                                                children: \"France\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 463,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Belgique\",\n                                                                                children: \"Belgique\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 464,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Suisse\",\n                                                                                children: \"Suisse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 465,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Canada\",\n                                                                                children: \"Canada\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 466,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Autre\",\n                                                                                children: \"Autre\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 467,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"form-title flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-3 text-secondary-600\",\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Partenariat souhait\\xe9\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Type de partenariat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                name: \"partnershipType\",\n                                                                value: formData.partnershipType,\n                                                                onChange: handleInputChange,\n                                                                className: \"karma-input\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"S\\xe9lectionnez un type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    partnershipTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: type.name,\n                                                                            children: type.name\n                                                                        }, type.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Budget envisag\\xe9 (optionnel)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                name: \"budget\",\n                                                                value: formData.budget,\n                                                                onChange: handleInputChange,\n                                                                className: \"karma-input\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"S\\xe9lectionnez une fourchette\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 504,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    budgetOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: option,\n                                                                            children: option\n                                                                        }, option, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"karma-label\",\n                                                                children: \"Motivation et objectifs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                name: \"motivation\",\n                                                                value: formData.motivation,\n                                                                onChange: handleInputChange,\n                                                                rows: 4,\n                                                                className: \"karma-input\",\n                                                                placeholder: \"Expliquez vos motivations et objectifs pour ce partenariat...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-section\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            name: \"acceptTerms\",\n                                                            checked: formData.acceptTerms,\n                                                            onChange: handleInputChange,\n                                                            className: \"mt-1 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: [\n                                                                \"J'accepte les\",\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"/conditions\",\n                                                                    className: \"text-secondary-600 hover:underline\",\n                                                                    children: \"conditions d'utilisation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" \",\n                                                                \"de Karma Com Solidarit\\xe9 *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.acceptTerms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm\",\n                                                    children: errors.acceptTerms\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 40\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            name: \"acceptRGPD\",\n                                                            checked: formData.acceptRGPD,\n                                                            onChange: handleInputChange,\n                                                            className: \"mt-1 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: [\n                                                                \"J'accepte la\",\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"/politique-confidentialite\",\n                                                                    className: \"text-secondary-600 hover:underline\",\n                                                                    children: \"politique de confidentialit\\xe9\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" \",\n                                                                \"et le traitement de mes donn\\xe9es personnelles selon le RGPD *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.acceptRGPD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm\",\n                                                    children: errors.acceptRGPD\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 39\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: `bg-gradient-to-r from-secondary-600 to-secondary-700 text-white font-medium py-3 px-12 rounded-lg hover:from-secondary-700 hover:to-secondary-800 transition-all duration-200 transform hover:scale-105 text-lg ${isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                                            children: isSubmitting ? \"Inscription en cours...\" : \"Soumettre ma candidature\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                lineNumber: 582,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/inscription/organisation/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Logo */ \"(ssr)/./src/components/ui/Logo.tsx\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n\n\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-karma py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: \"md\",\n                                        showText: true,\n                                        className: \"text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 max-w-md\",\n                                    children: \"Karma Com Solidarit\\xe9 soutient ses associations partenaires dans la r\\xe9alisation de leurs projets. Ensemble, nous construisons un r\\xe9seau solidaire et engag\\xe9.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-primary-400 transition-colors duration-200\",\n                                            \"aria-label\": \"Facebook\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-primary-400 transition-colors duration-200\",\n                                            \"aria-label\": \"Twitter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-primary-400 transition-colors duration-200\",\n                                            \"aria-label\": \"LinkedIn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Liens rapides\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/inscription/association\",\n                                                className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                children: \"Inscription Association\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/inscription/organisation\",\n                                                className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                children: \"Inscription Organisation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/inscription/benevole\",\n                                                className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                children: \"Devenir B\\xe9n\\xe9vole\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/dashboard\",\n                                                className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                children: \"Dashboard RH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"tel:+33123456789\",\n                                                    className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                    children: \"+33 1 23 45 67 89\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"text-primary-400 mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"123 Rue de la Solidarit\\xe9\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 43\n                                                        }, undefined),\n                                                        \"75001 Paris, France\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" Karma Com Solidarit\\xe9. Tous droits r\\xe9serv\\xe9s.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/mentions-legales\",\n                                        className: \"text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200\",\n                                        children: \"Mentions l\\xe9gales\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/politique-confidentialite\",\n                                        className: \"text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200\",\n                                        children: \"Politique de confidentialit\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/rgpd\",\n                                        className: \"text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200\",\n                                        children: \"RGPD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_Logo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Logo */ \"(ssr)/./src/components/ui/Logo.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: \"Accueil\",\n            href: \"/\"\n        },\n        {\n            name: \"Associations\",\n            href: \"/inscription/association\"\n        },\n        {\n            name: \"Organisations\",\n            href: \"/inscription/organisation\"\n        },\n        {\n            name: \"B\\xe9n\\xe9voles\",\n            href: \"/inscription/benevole\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    const isActive = (href)=>{\n        return pathname === href || href !== \"/\" && pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg border-b border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-karma\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: \"md\",\n                                showText: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-primary-600 bg-primary-50 border-b-2 border-primary-600\" : \"text-gray-700 hover:text-primary-600 hover:bg-gray-50\"}`,\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/connexion\",\n                                    className: \"flex items-center space-x-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Connexion\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"karma-button-primary\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-50 transition-colors duration-200\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 29\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 47\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-primary-600 bg-primary-50\" : \"text-gray-700 hover:text-primary-600 hover:bg-gray-50\"}`,\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-200 flex flex-col space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/connexion\",\n                                        className: \"flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Connexion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"karma-button-primary text-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Logo.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Logo.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Logo = ({ size = \"md\", showText = true, className = \"\" })=>{\n    const sizeClasses = {\n        sm: \"h-8 w-8\",\n        md: \"h-12 w-12\",\n        lg: \"h-16 w-16\",\n        xl: \"h-24 w-24\"\n    };\n    const textSizeClasses = {\n        sm: \"text-lg\",\n        md: \"text-xl\",\n        lg: \"text-2xl\",\n        xl: \"text-3xl\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center space-x-3 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${sizeClasses[size]} relative`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    viewBox: \"0 0 100 100\",\n                    className: \"w-full h-full\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"logoGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"100%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#0ea5e9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#ec4899\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                            x: \"5\",\n                            y: \"5\",\n                            width: \"90\",\n                            height: \"90\",\n                            rx: \"20\",\n                            ry: \"20\",\n                            fill: \"#f8fafc\",\n                            stroke: \"#e2e8f0\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M25 20 L25 80 L35 80 L35 55 L50 70 L65 70 L45 50 L65 30 L50 30 L35 45 L35 20 Z\",\n                            fill: \"url(#logoGradient)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"70\",\n                            cy: \"30\",\n                            r: \"8\",\n                            fill: \"#ec4899\",\n                            opacity: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `font-bold text-primary-700 ${textSizeClasses[size]} leading-tight`,\n                        children: \"Karma Com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `font-medium text-secondary-600 ${size === \"sm\" ? \"text-xs\" : size === \"md\" ? \"text-sm\" : \"text-base\"} leading-tight`,\n                        children: \"Solidarit\\xe9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Logo);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useConstants.ts":
/*!***********************************!*\
  !*** ./src/hooks/useConstants.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConstants: () => (/* binding */ useConstants),\n/* harmony export */   useDepartments: () => (/* binding */ useDepartments),\n/* harmony export */   useOrganizationTypes: () => (/* binding */ useOrganizationTypes),\n/* harmony export */   useSectors: () => (/* binding */ useSectors),\n/* harmony export */   useSkills: () => (/* binding */ useSkills)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useConstants() {\n    const [constants, setConstants] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchConstants = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/constants\");\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            setConstants(data);\n        } catch (err) {\n            console.error(\"Erreur lors du chargement des constantes:\", err);\n            setError(err instanceof Error ? err.message : \"Erreur inconnue\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchConstants();\n    }, []);\n    return {\n        constants,\n        loading,\n        error,\n        refetch: fetchConstants\n    };\n}\n// Hook spécialisé pour les types d'organisations\nfunction useOrganizationTypes() {\n    const [organizationTypes, setOrganizationTypes] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const fetchOrganizationTypes = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/constants/organization-types\");\n                if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n                const data = await response.json();\n                setOrganizationTypes(data);\n            } catch (err) {\n                console.error(\"Erreur lors du chargement des types d'organisation:\", err);\n                setError(err instanceof Error ? err.message : \"Erreur inconnue\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchOrganizationTypes();\n    }, []);\n    return {\n        organizationTypes,\n        loading,\n        error\n    };\n}\n// Hook spécialisé pour les secteurs\nfunction useSectors() {\n    const [sectors, setSectors] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const fetchSectors = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/constants/sectors\");\n                if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n                const data = await response.json();\n                setSectors(data);\n            } catch (err) {\n                console.error(\"Erreur lors du chargement des secteurs:\", err);\n                setError(err instanceof Error ? err.message : \"Erreur inconnue\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSectors();\n    }, []);\n    return {\n        sectors,\n        loading,\n        error\n    };\n}\n// Hook spécialisé pour les compétences\nfunction useSkills() {\n    const [skills, setSkills] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [skillsByCategory, setSkillsByCategory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const fetchSkills = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/constants/skills\");\n                if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n                const data = await response.json();\n                setSkills(data.skills);\n                setSkillsByCategory(data.skillsByCategory);\n            } catch (err) {\n                console.error(\"Erreur lors du chargement des comp\\xe9tences:\", err);\n                setError(err instanceof Error ? err.message : \"Erreur inconnue\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSkills();\n    }, []);\n    return {\n        skills,\n        skillsByCategory,\n        loading,\n        error\n    };\n}\n// Hook spécialisé pour les départements\nfunction useDepartments() {\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const fetchDepartments = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/constants/departments\");\n                if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n                const data = await response.json();\n                setDepartments(data);\n            } catch (err) {\n                console.error(\"Erreur lors du chargement des d\\xe9partements:\", err);\n                setError(err instanceof Error ? err.message : \"Erreur inconnue\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchDepartments();\n    }, []);\n    return {\n        departments,\n        loading,\n        error\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useConstants.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f9200e9d0ff7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2FybWEtY29tLWRhc2hib2FyZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YTBiNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY5MjAwZTlkMGZmN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/inscription/organisation/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/inscription/organisation/page.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\mesDocs\AI\KCS\augment-kcs\src\app\inscription\organisation\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Karma Com Solidarit\\xe9 - Gestion d'Adh\\xe9sion\",\n    description: \"Plateforme de gestion d'adh\\xe9sion pour Karma Com Solidarit\\xe9 - Associations, Organisations et B\\xe9n\\xe9voles\",\n    keywords: \"karma com, solidarit\\xe9, adh\\xe9sion, associations, b\\xe9n\\xe9voles, organisations\",\n    authors: [\n        {\n            name: \"Karma Com Solidarit\\xe9\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBdUI7S0FBRTtJQUMzQ0MsVUFBVTtBQUNaLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV2IsK0pBQWU7c0JBQzlCLDRFQUFDYztnQkFBSUQsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2thcm1hLWNvbS1kYXNoYm9hcmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0thcm1hIENvbSBTb2xpZGFyaXTDqSAtIEdlc3Rpb24gZFxcJ0FkaMOpc2lvbicsXG4gIGRlc2NyaXB0aW9uOiAnUGxhdGVmb3JtZSBkZSBnZXN0aW9uIGRcXCdhZGjDqXNpb24gcG91ciBLYXJtYSBDb20gU29saWRhcml0w6kgLSBBc3NvY2lhdGlvbnMsIE9yZ2FuaXNhdGlvbnMgZXQgQsOpbsOpdm9sZXMnLFxuICBrZXl3b3JkczogJ2thcm1hIGNvbSwgc29saWRhcml0w6ksIGFkaMOpc2lvbiwgYXNzb2NpYXRpb25zLCBiw6luw6l2b2xlcywgb3JnYW5pc2F0aW9ucycsXG4gIGF1dGhvcnM6IFt7IG5hbWU6ICdLYXJtYSBDb20gU29saWRhcml0w6knIH1dLFxuICB2aWV3cG9ydDogJ3dpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImZyXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsInZpZXdwb3J0IiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finscription%2Forganisation%2Fpage&page=%2Finscription%2Forganisation%2Fpage&appPaths=%2Finscription%2Forganisation%2Fpage&pagePath=private-next-app-dir%2Finscription%2Forganisation%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();