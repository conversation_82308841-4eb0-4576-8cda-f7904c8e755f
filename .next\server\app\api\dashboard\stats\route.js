"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_hamza_bedoui_Documents_mesDocs_AI_KCS_augment_kcs_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/stats/route.ts */ \"(rsc)/./src/app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_hamza_bedoui_Documents_mesDocs_AI_KCS_augment_kcs_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/dashboard/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/stats/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/dashboard/stats/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function GET(request) {\n    try {\n        // Vérifier l'authentification (optionnelle pour les tests)\n        const user = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getUserFromRequest)(request);\n        // Pour les tests, permettre l'accès sans authentification\n        // En production, décommenter la ligne suivante :\n        // if (!user || user.userType !== UserType.HR_ADMIN) {\n        //   return NextResponse.json(\n        //     { error: 'Accès non autorisé' },\n        //     { status: 403 }\n        //   )\n        // }\n        // Récupérer les statistiques\n        const [totalCandidates, pendingApplications, approvedMembers, scheduledInterviews, recentApplications] = await Promise.all([\n            // Total des candidats\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.count({\n                where: {\n                    userType: {\n                        in: [\n                            _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.ASSOCIATION,\n                            _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.ORGANIZATION,\n                            _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.VOLUNTEER\n                        ]\n                    }\n                }\n            }),\n            // Candidatures en attente\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.profile.count({\n                where: {\n                    membershipStatus: _prisma_client__WEBPACK_IMPORTED_MODULE_3__.MembershipStatus.PENDING\n                }\n            }),\n            // Membres approuvés/actifs\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.profile.count({\n                where: {\n                    membershipStatus: {\n                        in: [\n                            _prisma_client__WEBPACK_IMPORTED_MODULE_3__.MembershipStatus.APPROVED,\n                            _prisma_client__WEBPACK_IMPORTED_MODULE_3__.MembershipStatus.ACTIVE\n                        ]\n                    }\n                }\n            }),\n            // Entretiens programmés\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.appointment.count({\n                where: {\n                    scheduledAt: {\n                        gte: new Date()\n                    },\n                    status: {\n                        in: [\n                            \"SCHEDULED\",\n                            \"CONFIRMED\"\n                        ]\n                    }\n                }\n            }),\n            // Candidatures récentes (derniers 30 jours)\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findMany({\n                where: {\n                    userType: {\n                        in: [\n                            _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.ASSOCIATION,\n                            _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.ORGANIZATION,\n                            _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.VOLUNTEER\n                        ]\n                    }\n                },\n                include: {\n                    profile: true\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: 10\n            })\n        ]);\n        // Statistiques par type d'utilisateur\n        const userTypeStats = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.groupBy({\n            by: [\n                \"userType\"\n            ],\n            where: {\n                userType: {\n                    in: [\n                        _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.ASSOCIATION,\n                        _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.ORGANIZATION,\n                        _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.VOLUNTEER\n                    ]\n                }\n            },\n            _count: {\n                id: true\n            }\n        });\n        // Statistiques par statut\n        const statusStats = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.profile.groupBy({\n            by: [\n                \"membershipStatus\"\n            ],\n            _count: {\n                id: true\n            }\n        });\n        // Évolution mensuelle (derniers 6 mois)\n        const sixMonthsAgo = new Date();\n        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);\n        const monthlyStats = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.groupBy({\n            by: [\n                \"createdAt\"\n            ],\n            where: {\n                userType: {\n                    in: [\n                        _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.ASSOCIATION,\n                        _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.ORGANIZATION,\n                        _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserType.VOLUNTEER\n                    ]\n                },\n                createdAt: {\n                    gte: sixMonthsAgo\n                }\n            },\n            _count: {\n                id: true\n            }\n        });\n        // Formater les données pour le frontend\n        const stats = {\n            overview: {\n                totalCandidates,\n                pendingApplications,\n                approvedMembers,\n                scheduledInterviews\n            },\n            userTypes: userTypeStats.reduce((acc, stat)=>{\n                acc[stat.userType] = stat._count.id;\n                return acc;\n            }, {}),\n            statuses: statusStats.reduce((acc, stat)=>{\n                acc[stat.membershipStatus] = stat._count.id;\n                return acc;\n            }, {}),\n            recentApplications: recentApplications.map((user)=>({\n                    id: user.id,\n                    name: user.name,\n                    email: user.email,\n                    type: user.userType === \"ASSOCIATION\" ? \"Association\" : user.userType === \"ORGANIZATION\" ? \"Organisation\" : \"B\\xe9n\\xe9vole\",\n                    organization: user.profile?.organizationName || null,\n                    status: user.profile?.membershipStatus?.toLowerCase() || \"pending\",\n                    date: user.createdAt.toISOString().split(\"T\")[0],\n                    userType: user.userType,\n                    membershipStatus: user.profile?.membershipStatus,\n                    createdAt: user.createdAt\n                })),\n            monthlyGrowth: monthlyStats.map((stat)=>({\n                    month: stat.createdAt,\n                    count: stat._count.id\n                }))\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(stats);\n    } catch (error) {\n        console.error(\"Erreur lors de la r\\xe9cup\\xe9ration des statistiques:\", error);\n        // Log détaillé pour le debugging\n        if (error instanceof Error) {\n            console.error(\"Message d'erreur:\", error.message);\n            console.error(\"Stack trace:\", error.stack);\n        }\n        // Retourner une erreur plus détaillée en mode développement\n        const isDevelopment = \"development\" === \"development\";\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Erreur interne du serveur\",\n            details: isDevelopment ? {\n                message: error instanceof Error ? error.message : \"Erreur inconnue\",\n                type: error instanceof Error ? error.constructor.name : \"Unknown\"\n            } : undefined\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getTokenFromRequest: () => (/* binding */ getTokenFromRequest),\n/* harmony export */   getUserFromRequest: () => (/* binding */ getUserFromRequest),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.NEXTAUTH_SECRET || \"fallback-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hashedPassword);\n}\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        return null;\n    }\n}\nfunction getTokenFromRequest(request) {\n    const authHeader = request.headers.get(\"authorization\");\n    if (authHeader && authHeader.startsWith(\"Bearer \")) {\n        return authHeader.substring(7);\n    }\n    // Fallback to cookie\n    const token = request.cookies.get(\"auth-token\")?.value;\n    return token || null;\n}\nfunction getUserFromRequest(request) {\n    const token = getTokenFromRequest(request);\n    if (!token) return null;\n    return verifyToken(token);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rYXJtYS1jb20tZGFzaGJvYXJkLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();