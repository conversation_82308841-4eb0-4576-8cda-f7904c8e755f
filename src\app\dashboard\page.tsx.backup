'use client'

import React, { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import Logo from '@/components/ui/Logo'
export const dynamic = 'force-dynamic'

import {
  Users,
  User,
  Building2,
  Heart,
  Calendar,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Search,
  Filter,
  Download,
  Bell,
  Settings,
  Edit,
  Play,
  Pause,
  StopCircle
} from 'lucide-react'

// Fonction helper pour les labels des types d'entretien
function getInterviewTypeLabel(type: string): string {
  switch (type) {
    case 'DISCOVERY':
      return 'Découverte'
    case 'INTEGRATION':
      return 'Intégration'
    case 'FOLLOW_UP':
      return 'Suivi'
    case 'INTERVIEW':
      return 'Entretien'
    default:
      return type
  }
}

// Interface pour les candidatures récentes
interface RecentApplication {
  id: string
  name: string
  email: string
  type: string
  organization: string | null
  status: string
  date: string
  userType?: string
  membershipStatus?: string
  createdAt?: string
}

// Interface pour les entretiens
interface Interview {
  id: string
  title: string
  description: string
  candidateId: string
  candidateName: string
  candidateEmail: string
  scheduledAt: string
  type: 'DISCOVERY' | 'FOLLOW_UP' | 'FINAL'
  status: 'SCHEDULED' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED'
  notes?: string
  createdAt: string
  createdBy?: {
    id: string
    name: string
    email: string
  }
}

// Interface pour les formulaires
interface InterviewFormData {
  title: string
  description: string
  candidateId: string
  scheduledAt: string
  type: 'DISCOVERY' | 'FOLLOW_UP' | 'FINAL'
}

export default function DashboardPage() {
  // Accès direct au dashboard sans authentification
  const isAuthenticated = true // Toujours authentifié
  const authLoading = false // Pas de chargement d'auth

  const [activeTab, setActiveTab] = useState('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [loading, setLoading] = useState(false) // Commencer sans chargement
  const [error, setError] = useState<string | null>(null)

  // États pour les entretiens et candidatures
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [interviewsLoading, setInterviewsLoading] = useState(false)
  const [showInterviewForm, setShowInterviewForm] = useState(false)
  const [interviewFormData, setInterviewFormData] = useState<InterviewFormData>({
    title: '',
    description: '',
    candidateId: '',
    scheduledAt: '',
    type: 'DISCOVERY'
  })
  const [candidates, setCandidates] = useState<RecentApplication[]>([])
  const [selectedCandidate, setSelectedCandidate] = useState<string>('')
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  // États pour les bénévoles (nouveau système)
  const [volunteers, setVolunteers] = useState<RecentApplication[]>([])
  const [volunteersLoading, setVolunteersLoading] = useState(false)
  const [volunteersPage, setVolunteersPage] = useState(1)
  const [volunteersTotal, setVolunteersTotal] = useState(0)
  const volunteersLimit = 10

  // États pour la pagination des candidatures récentes
  const [recentApplicationsPage, setRecentApplicationsPage] = useState(1)
  const [recentApplicationsTotal, setRecentApplicationsTotal] = useState(0)
  const recentApplicationsLimit = 10

  // États pour la gestion des entretiens
  const [editingInterview, setEditingInterview] = useState<Interview | null>(null)
  const [showInterviewDetails, setShowInterviewDetails] = useState<string | null>(null)
  const [startingInterview, setStartingInterview] = useState<Interview | null>(null)

  // Accès direct au dashboard - pas de fonctions d'authentification nécessaires

  // Fonctions utilitaires
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Association':
        return <Heart className="text-red-500" size={16} />
      case 'Organisation':
        return <Building2 className="text-blue-500" size={16} />
      case 'Bénévole':
        return <Users className="text-green-500" size={16} />
      default:
        return <Users className="text-gray-500" size={16} />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      // Statuts candidatures
      pending: { label: 'En attente', color: 'bg-yellow-100 text-yellow-800 border border-yellow-200' },
      approved: { label: 'Approuvé', color: 'bg-green-100 text-green-800 border border-green-200' },
      active: { label: 'Actif', color: 'bg-blue-100 text-blue-800 border border-blue-200' },
      rejected: { label: 'Rejeté', color: 'bg-red-100 text-red-800 border border-red-200' },

      // Statuts entretiens
      SCHEDULED: { label: 'Programmé', color: 'bg-blue-100 text-blue-800 border border-blue-200' },
      CONFIRMED: { label: 'Confirmé', color: 'bg-green-100 text-green-800 border border-green-200' },
      COMPLETED: { label: 'Terminé', color: 'bg-gray-100 text-gray-800 border border-gray-200' },
      CANCELLED: { label: 'Annulé', color: 'bg-red-100 text-red-800 border border-red-200' },

      // Alias pour compatibilité
      confirmed: { label: 'Confirmé', color: 'bg-green-100 text-green-800 border border-green-200' },
      completed: { label: 'Terminé', color: 'bg-gray-100 text-gray-800 border border-gray-200' },
      cancelled: { label: 'Annulé', color: 'bg-red-100 text-red-800 border border-red-200' },
      scheduled: { label: 'Programmé', color: 'bg-blue-100 text-blue-800 border border-blue-200' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  // Fonction pour traduire les types d'entretiens
  const getInterviewTypeLabel = (type: string) => {
    const typeLabels = {
      DISCOVERY: 'Découverte',
      FOLLOW_UP: 'Suivi',
      FINAL: 'Final',
      // Alias pour compatibilité
      discovery: 'Découverte',
      follow_up: 'Suivi',
      final: 'Final'
    }

    return typeLabels[type as keyof typeof typeLabels] || type
  }

  // Charger les entretiens
  const loadInterviews = useCallback(async () => {
    try {
      setInterviewsLoading(true)
      console.log('📅 Chargement des entretiens...')
      const response = await fetch('/api/interviews')
      if (response.ok) {
        const data = await response.json()
        console.log('📅 Entretiens chargés:', data.length, 'entretiens')
        setInterviews(data)
      } else {
        console.error('❌ Erreur chargement entretiens:', response.status)
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des entretiens:', error)
    } finally {
      setInterviewsLoading(false)
    }
  }, [])

  // Charger les candidats
  const loadCandidates = useCallback(async (page = 1, limit = 50) => {
    try {
      console.log('👥 Chargement des candidats...')
      const response = await fetch(`/api/candidates?page=${page}&limit=${limit}`)
      if (response.ok) {
        const data = await response.json()
        console.log('👥 Candidats chargés:', data.candidates.length, 'candidats')
        setCandidates(data.candidates)
        setRecentApplicationsTotal(data.pagination.total)
      } else {
        console.error('❌ Erreur chargement candidats:', response.status)
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des candidats:', error)
    }
  }, [])

  // Charger les bénévoles (nouveau système)
  const loadVolunteers = useCallback(async (page = 1, limit = 10) => {
    try {
      setVolunteersLoading(true)
      console.log('🎯 Chargement des bénévoles...')
      const response = await fetch(`/api/dashboard/volunteers?page=${page}&limit=${limit}`)
      if (response.ok) {
        const data = await response.json()
        setVolunteers(data.volunteers || [])
        setVolunteersTotal(data.pagination?.total || 0)
        console.log('✅ Bénévoles chargés:', data.volunteers?.length || 0)
      } else {
        console.error('❌ Erreur lors du chargement des bénévoles:', response.status)
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des bénévoles:', error)
    } finally {
      setVolunteersLoading(false)
    }
  }, [])

  // Fonction de chargement des données stabilisée avec useCallback
  const fetchDashboardData = useCallback(async () => {
    const isDevelopment = process.env.NODE_ENV === 'development'

    // En développement, permettre le chargement sans authentification
    if (!isDevelopment && (!isAuthenticated || authLoading)) {
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Récupérer les headers d'authentification directement
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      const response = await fetch('/api/dashboard/stats', { headers })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setDashboardData(data)
    } catch (err) {
      console.error('Erreur lors du chargement des données:', err)
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
    } finally {
      setLoading(false)
    }
  }, [])

  // Charger les données du dashboard
  useEffect(() => {
    fetchDashboardData()
    loadInterviews()
    loadCandidates()
    loadVolunteers()
  }, [fetchDashboardData, loadInterviews, loadCandidates, loadVolunteers])

  // Données par défaut si pas encore chargées
  const stats = dashboardData?.overview || {
    totalCandidates: 0,
    pendingApplications: 0,
    scheduledInterviews: 0,
    approvedMembers: 0
  }

  // Données des candidatures récentes depuis l'API (limitées à 10 pour la vue d'ensemble)
  const recentApplications = (dashboardData?.recentApplications || []).slice(0, recentApplicationsLimit)

  // Données des entretiens à venir depuis l'API (limitées à 5 pour la vue d'ensemble)
  const upcomingInterviews = interviews
    .filter(interview => new Date(interview.scheduledAt) > new Date()) // Seulement les futurs
    .sort((a, b) => new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime()) // Tri par date
    .slice(0, 5) // Limiter à 5 entretiens

  // Fonctions pour gérer les entretiens
  const handleCreateInterview = async (formData: InterviewFormData) => {
    try {
      console.log('📝 Création entretien avec données:', formData)

      const response = await fetch('/api/interviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      console.log('📡 Réponse API:', response.status, response.statusText)

      if (response.ok) {
        const newInterview = await response.json()
        console.log('✅ Entretien créé:', newInterview)

        // Fermer le formulaire et réinitialiser
        setShowInterviewForm(false)
        setInterviewFormData({
          title: '',
          description: '',
          candidateId: '',
          scheduledAt: '',
          type: 'DISCOVERY'
        })

        // Recharger les données dans le bon ordre
        console.log('🔄 Rechargement des données...')
        await Promise.all([
          loadInterviews(),
          fetchDashboardData(),
          loadVolunteers(),
          loadCandidates()
        ])

        // Afficher un message de succès
        setSuccessMessage(`Entretien "${newInterview.interview?.title || newInterview.title}" créé avec succès`)
        setTimeout(() => setSuccessMessage(null), 5000)

        console.log('✅ Données rechargées avec succès')
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ Erreur API:', errorData)
        throw new Error(errorData.error || 'Erreur lors de la création de l\'entretien')
      }
    } catch (error) {
      console.error('❌ Erreur création entretien:', error)
      setError(error instanceof Error ? error.message : 'Impossible de créer l\'entretien')
    }
  }

  const handleUpdateCandidateStatus = async (candidateId: string, newStatus: string) => {
    try {
      console.log(`🔄 Mise à jour statut candidat ${candidateId}: ${newStatus}`)

      const response = await fetch(`/api/candidates/${candidateId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        const updatedCandidate = await response.json()
        console.log('✅ Statut mis à jour:', updatedCandidate)

        // Mettre à jour l'état local immédiatement
        setCandidates(prev => prev.map(candidate =>
          candidate.id === candidateId
            ? { ...candidate, status: newStatus }
            : candidate
        ))

        // Recharger les données pour synchroniser
        await loadCandidates()
        await fetchDashboardData()

        // Afficher un message de succès
        setSuccessMessage(`Statut du candidat mis à jour: ${newStatus}`)
        setTimeout(() => setSuccessMessage(null), 3000)

      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ Erreur API:', errorData)
        throw new Error(errorData.error || 'Erreur lors de la mise à jour du statut')
      }
    } catch (error) {
      console.error('❌ Erreur mise à jour statut:', error)
      setError(error instanceof Error ? error.message : 'Impossible de mettre à jour le statut')
      setTimeout(() => setError(null), 5000)
    }
  }

  // Fonction pour mettre à jour le statut d'un bénévole
  const handleUpdateVolunteerStatus = async (volunteerId: string, newStatus: string) => {
    try {
      console.log(`🔄 Mise à jour statut bénévole ${volunteerId}: ${newStatus}`)

      const response = await fetch(`/api/dashboard/volunteers?id=${volunteerId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        const updatedVolunteer = await response.json()
        console.log('✅ Statut bénévole mis à jour:', updatedVolunteer)

        // Mettre à jour l'état local immédiatement
        setVolunteers(prev => prev.map(volunteer =>
          volunteer.id === volunteerId
            ? { ...volunteer, status: newStatus.toLowerCase() }
            : volunteer
        ))

        // Recharger les données pour synchroniser
        await loadVolunteers()
        await fetchDashboardData()

        // Afficher un message de succès
        setSuccessMessage(`Statut du bénévole mis à jour: ${newStatus}`)
        setTimeout(() => setSuccessMessage(null), 3000)

      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ Erreur API bénévole:', errorData)
        throw new Error(errorData.error || 'Erreur lors de la mise à jour du statut du bénévole')
      }
    } catch (error) {
      console.error('❌ Erreur mise à jour statut bénévole:', error)
      setError(error instanceof Error ? error.message : 'Impossible de mettre à jour le statut du bénévole')
      setTimeout(() => setError(null), 5000)
    }
  }

  // Fonction pour modifier un entretien
  const handleEditInterview = (interview: Interview) => {
    setEditingInterview(interview)
    setInterviewFormData({
      title: interview.title,
      description: interview.description || '',
      candidateId: interview.candidateId,
      scheduledAt: interview.scheduledAt.slice(0, 16), // Format pour datetime-local
      type: interview.type
    })
    setShowInterviewForm(true)
  }

  // Fonction pour démarrer un entretien
  const handleStartInterview = (interview: Interview) => {
    setStartingInterview(interview)
  }

  // Fonction pour mettre à jour le statut d'un entretien
  const handleUpdateInterviewStatus = async (interviewId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/interviews/${interviewId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        await loadInterviews()
        setSuccessMessage('Statut de l\'entretien mis à jour')
        setTimeout(() => setSuccessMessage(null), 3000)
      } else {
        throw new Error('Erreur lors de la mise à jour du statut')
      }
    } catch (error) {
      console.error('Erreur:', error)
      setError('Impossible de mettre à jour le statut de l\'entretien')
    }
  }

  // Accès direct au dashboard - pas de vérification d'authentification
  // Le dashboard est maintenant accessible directement sans login

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Logo size="md" showText={true} />
              </Link>
              <div className="hidden md:block">
                <h1 className="text-xl font-semibold text-gray-900">Dashboard RH</h1>
                {process.env.NODE_ENV === 'development' && (
                  <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                    Mode Développement
                  </span>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <Bell size={20} />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <Settings size={20} />
              </button>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">RH</span>
                </div>
                <span className="hidden md:block text-sm font-medium text-gray-700">Admin RH</span>
              </div>
              {/* Bouton de déconnexion supprimé - accès direct au dashboard */}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Indicateur de chargement */}
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Chargement des données...</p>
          </div>
        )}

        {/* Gestion d'erreur */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex">
              <XCircle className="text-red-400 mr-3" size={20} />
              <div>
                <h3 className="text-red-800 font-medium">Erreur de chargement</h3>
                <p className="text-red-600 text-sm mt-1">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="text-red-600 hover:text-red-800 text-sm underline mt-2"
                >
                  Réessayer
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Contenu principal */}
        {!error && (
          <>
        {/* Navigation tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Vue d\'ensemble', icon: TrendingUp },
              { id: 'applications', label: 'Candidatures', icon: Users },
              { id: 'interviews', label: 'Entretiens', icon: Calendar },
              { id: 'members', label: 'Membres', icon: CheckCircle }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'bg-primary-100 text-primary-700 border-b-2 border-primary-600'
                    : 'text-gray-600 hover:text-primary-600 hover:bg-gray-100'
                }`}
              >
                <tab.icon size={18} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Vue d'ensemble */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Statistiques */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Candidats</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {loading ? (
                        <span className="inline-block animate-pulse bg-gray-200 rounded w-16 h-8"></span>
                      ) : (
                        stats.totalCandidates
                      )}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Users className="text-primary-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">En attente</p>
                    <p className="text-3xl font-bold text-yellow-600">{stats.pendingApplications}</p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <Clock className="text-yellow-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Entretiens prévus</p>
                    <p className="text-3xl font-bold text-blue-600">{stats.scheduledInterviews}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Calendar className="text-blue-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Membres actifs</p>
                    <p className="text-3xl font-bold text-green-600">{stats.activeMembers}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <CheckCircle className="text-green-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Bénévoles</p>
                    <p className="text-3xl font-bold text-purple-600">
                      {loading ? (
                        <span className="inline-block animate-pulse bg-gray-200 rounded w-16 h-8"></span>
                      ) : (
                        stats.totalVolunteerApplications || 0
                      )}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {stats.pendingVolunteerApplications || 0} en attente
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Heart className="text-purple-600" size={24} />
                  </div>
                </div>
              </div>
            </div>

            {/* Candidatures récentes */}
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Candidatures récentes</h2>
                <Link
                  href="/dashboard/applications"
                  className="text-primary-600 hover:text-primary-700 font-medium text-sm"
                >
                  Voir tout
                </Link>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Candidat
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Organisation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentApplications.map((application: RecentApplication) => (
                      <tr key={application.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{application.name}</div>
                            <div className="text-sm text-gray-500">{application.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            {getTypeIcon(application.type)}
                            <span className="text-sm text-gray-900">{application.type}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {application.organization || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(application.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(application.date).toLocaleDateString('fr-FR')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination et actions */}
              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <div className="text-sm text-gray-500">
                  Affichage de {Math.min(recentApplicationsLimit, recentApplications.length)} sur {recentApplicationsTotal} candidatures
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setActiveTab('applications')}
                    className="karma-button-outline flex items-center space-x-2"
                  >
                    <Users size={16} />
                    <span>Voir tout</span>
                  </button>
                  {recentApplicationsTotal > recentApplicationsLimit && (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          if (recentApplicationsPage > 1) {
                            setRecentApplicationsPage(prev => prev - 1)
                            loadCandidates(recentApplicationsPage - 1, recentApplicationsLimit)
                          }
                        }}
                        disabled={recentApplicationsPage <= 1}
                        className="karma-button-outline text-sm px-3 py-1 disabled:opacity-50"
                      >
                        Précédent
                      </button>
                      <span className="text-sm text-gray-500">
                        Page {recentApplicationsPage} sur {Math.ceil(recentApplicationsTotal / recentApplicationsLimit)}
                      </span>
                      <button
                        onClick={() => {
                          if (recentApplicationsPage < Math.ceil(recentApplicationsTotal / recentApplicationsLimit)) {
                            setRecentApplicationsPage(prev => prev + 1)
                            loadCandidates(recentApplicationsPage + 1, recentApplicationsLimit)
                          }
                        }}
                        disabled={recentApplicationsPage >= Math.ceil(recentApplicationsTotal / recentApplicationsLimit)}
                        className="karma-button-outline text-sm px-3 py-1 disabled:opacity-50"
                      >
                        Suivant
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Entretiens à venir */}
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Entretiens à venir
                  <span className="ml-2 text-sm text-gray-500">({upcomingInterviews.length})</span>
                </h2>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setActiveTab('interviews')}
                    className="karma-button-outline flex items-center space-x-2"
                  >
                    <Calendar size={16} />
                    <span>Voir tout</span>
                  </button>
                  <button
                    onClick={() => {
                      setActiveTab('interviews')
                      setShowInterviewForm(true)
                    }}
                    className="karma-button-primary flex items-center space-x-2"
                  >
                    <Plus size={16} />
                    <span>Planifier</span>
                  </button>
                </div>
              </div>
              
              <div className="space-y-4">
                {upcomingInterviews.map((interview) => (
                  <div key={interview.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <Calendar className="text-primary-600" size={20} />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{interview.candidateName}</h3>
                        <p className="text-sm text-gray-500">
                          {getInterviewTypeLabel(interview.type)} • {new Date(interview.scheduledAt).toLocaleDateString('fr-FR')} à {new Date(interview.scheduledAt).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(interview.status)}
                      <button className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                        Détails
                      </button>
                    </div>
                  </div>
                ))}

                {upcomingInterviews.length === 0 && (
                  <div className="text-center py-8">
                    <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun entretien à venir</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Planifiez votre premier entretien pour commencer.
                    </p>
                    <div className="mt-4">
                      <button
                        onClick={() => {
                          setActiveTab('interviews')
                          setShowInterviewForm(true)
                        }}
                        className="karma-button-primary"
                      >
                        Planifier un entretien
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Onglet Candidatures */}
        {activeTab === 'applications' && (
          <div className="space-y-6">
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Gestion des Candidatures</h2>
                <div className="flex items-center space-x-3">
                  <select
                    value={selectedCandidate}
                    onChange={(e) => setSelectedCandidate(e.target.value)}
                    className="karma-input"
                  >
                    <option value="">Filtrer par statut</option>
                    <option value="pending">En attente</option>
                    <option value="approved">Approuvé</option>
                    <option value="rejected">Rejeté</option>
                  </select>
                  <button className="karma-button-primary flex items-center space-x-2">
                    <Download size={16} />
                    <span>Exporter</span>
                  </button>
                </div>
              </div>

              {/* Tableau des candidatures */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Candidat
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Organisation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentApplications.map((application: RecentApplication) => (
                      <tr key={application.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{application.name}</div>
                            <div className="text-sm text-gray-500">{application.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            {getTypeIcon(application.type)}
                            <span className="text-sm text-gray-900">{application.type}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {application.organization || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            value={application.status}
                            onChange={(e) => handleUpdateCandidateStatus(application.id, e.target.value)}
                            className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          >
                            <option value="pending">En attente</option>
                            <option value="approved">Approuvé</option>
                            <option value="active">Actif</option>
                            <option value="rejected">Rejeté</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(application.date).toLocaleDateString('fr-FR')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {
                              setInterviewFormData(prev => ({
                                ...prev,
                                candidateId: application.id,
                                title: `Entretien - ${application.name}`
                              }))
                              setShowInterviewForm(true)
                            }}
                            className="text-primary-600 hover:text-primary-900 mr-3"
                          >
                            Planifier entretien
                          </button>
                          <button className="text-gray-600 hover:text-gray-900">
                            Voir détails
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Section Bénévoles */}
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Candidatures Bénévoles
                  <span className="ml-2 text-sm text-gray-500">({volunteers.length})</span>
                </h2>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => loadVolunteers()}
                    disabled={volunteersLoading}
                    className="karma-button-outline flex items-center space-x-2"
                  >
                    <Users size={16} />
                    <span>Actualiser</span>
                  </button>
                  <button className="karma-button-primary flex items-center space-x-2">
                    <Download size={16} />
                    <span>Exporter</span>
                  </button>
                </div>
              </div>

              {/* Tableau des bénévoles */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Bénévole
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pôle
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Situation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Disponibilité
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {volunteersLoading ? (
                      <tr>
                        <td colSpan={7} className="px-6 py-4 text-center">
                          <div className="flex items-center justify-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                            <span className="ml-2">Chargement des bénévoles...</span>
                          </div>
                        </td>
                      </tr>
                    ) : volunteers.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                          Aucune candidature bénévole trouvée
                        </td>
                      </tr>
                    ) : (
                      volunteers.map((volunteer) => (
                        <tr key={volunteer.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                  <Heart className="h-5 w-5 text-purple-600" />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {volunteer.name}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {volunteer.email}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {(volunteer as any).contributionPole || 'Non spécifié'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {(volunteer as any).currentStatus || 'Non spécifié'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {(volunteer as any).weeklyHours || 'Non spécifié'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              volunteer.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              volunteer.status === 'approved' ? 'bg-green-100 text-green-800' :
                              volunteer.status === 'rejected' ? 'bg-red-100 text-red-800' :
                              volunteer.status === 'under_review' ? 'bg-blue-100 text-blue-800' :
                              volunteer.status === 'interview_scheduled' ? 'bg-purple-100 text-purple-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {volunteer.status === 'pending' ? 'En attente' :
                               volunteer.status === 'approved' ? 'Approuvé' :
                               volunteer.status === 'rejected' ? 'Rejeté' :
                               volunteer.status === 'under_review' ? 'En révision' :
                               volunteer.status === 'interview_scheduled' ? 'Entretien planifié' :
                               volunteer.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {volunteer.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              <select
                                value={volunteer.status}
                                onChange={(e) => handleUpdateVolunteerStatus(volunteer.id, e.target.value.toUpperCase())}
                                className="text-xs border border-gray-300 rounded px-2 py-1"
                              >
                                <option value="pending">En attente</option>
                                <option value="under_review">En révision</option>
                                <option value="approved">Approuvé</option>
                                <option value="rejected">Rejeté</option>
                                <option value="interview_scheduled">Entretien planifié</option>
                              </select>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Onglet Entretiens */}
        {activeTab === 'interviews' && (
          <div className="space-y-6">
            {/* Message de succès */}
            {successMessage && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex">
                  <CheckCircle className="text-green-400 mr-3" size={20} />
                  <div>
                    <h3 className="text-green-800 font-medium">Succès</h3>
                    <p className="text-green-600 text-sm mt-1">{successMessage}</p>
                  </div>
                </div>
              </div>
            )}
            {/* Formulaire de création d'entretien */}
            {showInterviewForm && (
              <div className="dashboard-card">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Planifier un Entretien</h3>
                  <button
                    onClick={() => setShowInterviewForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XCircle size={20} />
                  </button>
                </div>

                <form onSubmit={(e) => {
                  e.preventDefault()
                  handleCreateInterview(interviewFormData)
                }} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="karma-label">Titre de l'entretien</label>
                      <input
                        type="text"
                        value={interviewFormData.title}
                        onChange={(e) => setInterviewFormData(prev => ({
                          ...prev,
                          title: e.target.value
                        }))}
                        className="karma-input"
                        placeholder="Ex: Entretien découverte"
                        required
                      />
                    </div>

                    <div>
                      <label className="karma-label">Type d'entretien</label>
                      <select
                        value={interviewFormData.type}
                        onChange={(e) => setInterviewFormData(prev => ({
                          ...prev,
                          type: e.target.value as 'DISCOVERY' | 'FOLLOW_UP' | 'FINAL'
                        }))}
                        className="karma-input"
                        required
                      >
                        <option value="DISCOVERY">Découverte</option>
                        <option value="FOLLOW_UP">Suivi</option>
                        <option value="FINAL">Final</option>
                      </select>
                    </div>

                    <div>
                      <label className="karma-label">Candidat</label>
                      <select
                        value={interviewFormData.candidateId}
                        onChange={(e) => setInterviewFormData(prev => ({
                          ...prev,
                          candidateId: e.target.value
                        }))}
                        className="karma-input"
                        required
                      >
                        <option value="">Sélectionner un candidat</option>
                        <optgroup label="Candidatures classiques">
                          {recentApplications.map((candidate: RecentApplication) => (
                            <option key={candidate.id} value={candidate.id}>
                              {candidate.name} - {candidate.type}
                            </option>
                          ))}
                        </optgroup>
                        <optgroup label="Candidatures bénévoles">
                          {volunteers.map((volunteer: RecentApplication) => (
                            <option key={volunteer.id} value={volunteer.id}>
                              {volunteer.name} - Bénévole
                            </option>
                          ))}
                        </optgroup>
                      </select>
                    </div>

                    <div>
                      <label className="karma-label">Date et heure</label>
                      <input
                        type="datetime-local"
                        value={interviewFormData.scheduledAt}
                        onChange={(e) => setInterviewFormData(prev => ({
                          ...prev,
                          scheduledAt: e.target.value
                        }))}
                        className="karma-input"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="karma-label">Description</label>
                    <textarea
                      value={interviewFormData.description}
                      onChange={(e) => setInterviewFormData(prev => ({
                        ...prev,
                        description: e.target.value
                      }))}
                      className="karma-input"
                      rows={3}
                      placeholder="Objectifs et points à aborder lors de l'entretien..."
                    />
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowInterviewForm(false)}
                      className="karma-button-outline"
                    >
                      Annuler
                    </button>
                    <button
                      type="submit"
                      className="karma-button-primary"
                    >
                      Planifier l'entretien
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Liste des entretiens */}
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Entretiens Planifiés
                  <span className="ml-2 text-sm text-gray-500">({interviews.length})</span>
                </h2>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={loadInterviews}
                    disabled={interviewsLoading}
                    className="karma-button-outline flex items-center space-x-2"
                  >
                    <Clock size={16} />
                    <span>Actualiser</span>
                  </button>
                  <button
                    onClick={() => setShowInterviewForm(true)}
                    className="karma-button-primary flex items-center space-x-2"
                  >
                    <Plus size={16} />
                    <span>Nouvel entretien</span>
                  </button>
                </div>
              </div>

              {/* Affichage en tableau moderne */}
              <div className="overflow-x-auto">
                {interviewsLoading ? (
                  <div className="text-center py-12">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-karma-pink"></div>
                    <p className="mt-2 text-gray-600">Chargement des entretiens...</p>
                  </div>
                ) : interviews.length === 0 ? (
                  <div className="text-center py-12">
                    <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun entretien planifié</h3>
                    <p className="text-gray-500 mb-6">Commencez par planifier votre premier entretien</p>
                    <button
                      onClick={() => setShowInterviewForm(true)}
                      className="karma-button-primary flex items-center space-x-2 mx-auto"
                    >
                      <Plus size={16} />
                      <span>Planifier un entretien</span>
                    </button>
                  </div>
                ) : (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gradient-to-r from-karma-blue to-karma-pink">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Candidat
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Entretien
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Date & Heure
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Statut
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {interviews.map((interview: Interview) => (
                        <tr key={interview.id} className="hover:bg-gray-50 transition-colors">
                          {/* Candidat */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full bg-gradient-to-r from-karma-blue to-karma-pink flex items-center justify-center">
                                  <User className="h-5 w-5 text-white" />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {interview.candidateName}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {interview.candidateEmail}
                                </div>
                                {(interview as any).candidateType === 'volunteer' && (
                                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                    <Heart className="h-3 w-3 mr-1" />
                                    Bénévole
                                  </span>
                                )}
                              </div>
                            </div>
                          </td>

                          {/* Entretien */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{interview.title}</div>
                            {interview.description && (
                              <div className="text-sm text-gray-500 truncate max-w-xs">
                                {interview.description}
                              </div>
                            )}
                          </td>

                          {/* Date & Heure */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {new Date(interview.scheduledAt).toLocaleDateString('fr-FR', {
                                weekday: 'short',
                                day: 'numeric',
                                month: 'short',
                                year: 'numeric'
                              })}
                            </div>
                            <div className="text-sm text-gray-500">
                              {new Date(interview.scheduledAt).toLocaleTimeString('fr-FR', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                          </td>

                          {/* Type */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              interview.type === 'DISCOVERY' ? 'bg-blue-100 text-blue-800' :
                              interview.type === 'INTEGRATION' ? 'bg-green-100 text-green-800' :
                              interview.type === 'FOLLOW_UP' ? 'bg-yellow-100 text-yellow-800' :
                              interview.type === 'INTERVIEW' ? 'bg-purple-100 text-purple-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {getInterviewTypeLabel(interview.type)}
                            </span>
                          </td>

                          {/* Statut */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getStatusBadge(interview.status)}
                          </td>

                          {/* Actions */}
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleEditInterview(interview)}
                                className="text-karma-blue hover:text-karma-pink transition-colors"
                                title="Modifier"
                              >
                                <Edit size={16} />
                              </button>

                              {interview.status === 'SCHEDULED' && (
                                <button
                                  onClick={() => handleUpdateInterviewStatus(interview.id, 'CONFIRMED')}
                                  className="text-green-600 hover:text-green-800 transition-colors"
                                  title="Confirmer"
                                >
                                  <CheckCircle size={16} />
                                </button>
                              )}

                              <button
                                onClick={() => handleStartInterview(interview)}
                                className="text-karma-pink hover:text-karma-blue transition-colors"
                                title="Démarrer"
                              >
                                <Play size={16} />
                              </button>

                              <select
                                value={interview.status}
                                onChange={(e) => handleUpdateInterviewStatus(interview.id, e.target.value)}
                                className="text-xs border border-gray-300 rounded px-2 py-1 focus:ring-karma-pink focus:border-karma-pink"
                              >
                                <option value="SCHEDULED">Programmé</option>
                                <option value="CONFIRMED">Confirmé</option>
                                <option value="COMPLETED">Terminé</option>
                                <option value="CANCELLED">Annulé</option>
                              </select>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Onglet Membres */}
        {activeTab === 'members' && (
          <div className="space-y-6">
            <div className="dashboard-card">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Gestion des Membres</h2>
              <p className="text-gray-600">Fonctionnalité de gestion des membres à venir...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
