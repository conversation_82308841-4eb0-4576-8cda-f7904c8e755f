'use client'

import React, { useState, useEffect } from 'react'
import { AlertCircle, CheckCircle } from 'lucide-react'

// Composants
import DashboardHeader from './components/DashboardHeader'
import DashboardTabs from './components/DashboardTabs'
import StatsCards from './components/overview/StatsCards'
import RecentActivity from './components/overview/RecentActivity'

// Hooks personnalisés
import { useDashboardData } from './hooks/useDashboardData'
import { useInterviews } from './hooks/useInterviews'
import { useCandidates } from './hooks/useCandidates'
import { useVolunteers } from './hooks/useVolunteers'

// Utilitaires
import { getStatusBadge } from './utils/statusHelpers'
import { formatDateFR } from './utils/dateHelpers'

export const dynamic = 'force-dynamic'

export default function Dashboard() {
  // État local pour l'onglet actif
  const [activeTab, setActiveTab] = useState('overview')
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  // Hooks personnalisés pour les données
  const {
    stats,
    recentApplications,
    loading: dashboardLoading,
    error: dashboardError,
    fetchDashboardData
  } = useDashboardData()

  const {
    interviews,
    interviewsLoading,
    loadInterviews,
    handleCreateInterview,
    handleUpdateInterviewStatus
  } = useInterviews()

  const {
    candidates,
    candidatesLoading,
    loadCandidates,
    handleUpdateCandidateStatus
  } = useCandidates()

  const {
    volunteers,
    volunteersLoading,
    loadVolunteers,
    handleUpdateVolunteerStatus
  } = useVolunteers()

  // Charger les données au montage du composant
  useEffect(() => {
    const loadAllData = async () => {
      await Promise.all([
        fetchDashboardData(),
        loadInterviews(),
        loadCandidates(),
        loadVolunteers()
      ])
    }

    loadAllData()
  }, [fetchDashboardData, loadInterviews, loadCandidates, loadVolunteers])

  // Gestionnaires d'événements avec messages de succès/erreur
  const handleCandidateStatusUpdate = async (candidateId: string, newStatus: string) => {
    await handleUpdateCandidateStatus(
      candidateId,
      newStatus,
      () => {
        setSuccessMessage('Statut candidat mis à jour')
        setTimeout(() => setSuccessMessage(null), 3000)
      },
      (error) => {
        setErrorMessage(error)
        setTimeout(() => setErrorMessage(null), 5000)
      }
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />
      <DashboardTabs activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Messages de notification */}
      {successMessage && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{successMessage}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {errorMessage && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{errorMessage}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Contenu principal */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Onglet Vue d'ensemble */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <StatsCards stats={stats} />
            <RecentActivity
              applications={recentApplications}
              loading={dashboardLoading}
            />
          </div>
        )}

        {/* Onglet Candidatures */}
        {activeTab === 'candidates' && (
          <div className="space-y-6">
            <div className="dashboard-card">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Candidatures
                <span className="ml-2 text-sm text-gray-500">({candidates.length})</span>
              </h2>

              {candidatesLoading ? (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-karma-pink"></div>
                  <p className="mt-2 text-gray-600">Chargement des candidatures...</p>
                </div>
              ) : candidates.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gradient-to-r from-karma-blue to-karma-pink">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Candidat
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Statut
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {candidates.map((application) => (
                        <tr key={application.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full bg-gradient-to-r from-karma-blue to-karma-pink flex items-center justify-center">
                                  <span className="text-white text-sm font-medium">
                                    {application.name.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{application.name}</div>
                                <div className="text-sm text-gray-500">{application.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {application.type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDateFR(application.date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getStatusBadge(application.status)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <select
                              value={application.status}
                              onChange={(e) => handleCandidateStatusUpdate(application.id, e.target.value)}
                              className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-karma-pink focus:border-karma-pink"
                            >
                              <option value="pending">En attente</option>
                              <option value="approved">Approuvé</option>
                              <option value="active">Actif</option>
                              <option value="rejected">Rejeté</option>
                              <option value="inactive">Inactif</option>
                              <option value="suspended">Suspendu</option>
                            </select>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">Aucune candidature trouvée</p>
              )}
            </div>
          </div>
        )}

        {/* Les autres onglets */}
        {!['overview', 'candidates'].includes(activeTab) && (
          <div className="dashboard-card text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Onglet {activeTab} en cours de développement
            </h3>
            <p className="text-gray-500">
              Cette section sera bientôt disponible avec les composants refactorisés.
            </p>
          </div>
        )}
      </main>
    </div>
  )
}