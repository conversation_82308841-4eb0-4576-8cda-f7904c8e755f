import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { AppointmentType, AppointmentStatus } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const interviews = await prisma.appointment.findMany({
      include: {
        candidate: {
          include: {
            profile: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        scheduledAt: 'asc'
      }
    })

    const formattedInterviews = interviews.map(interview => ({
      id: interview.id,
      title: interview.title,
      description: interview.description,
      candidateId: interview.candidateId,
      candidateName: interview.candidate.name,
      candidateEmail: interview.candidate.email,
      scheduledAt: interview.scheduledAt.toISOString(),
      type: interview.type,
      status: interview.status,
      notes: interview.notes,
      createdAt: interview.createdAt.toISOString(),
      createdBy: interview.createdBy
    }))

    return NextResponse.json(formattedInterviews)
  } catch (error) {
    console.error('Erreur lors de la récupération des entretiens:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, description, candidateId, scheduledAt, type } = body

    // Validation des données
    if (!title || !candidateId || !scheduledAt || !type) {
      return NextResponse.json(
        { error: 'Données manquantes' },
        { status: 400 }
      )
    }

    // Vérifier que le candidat existe
    const candidate = await prisma.user.findUnique({
      where: { id: candidateId }
    })

    if (!candidate) {
      return NextResponse.json(
        { error: 'Candidat non trouvé' },
        { status: 404 }
      )
    }

    // Pour l'instant, on utilise un admin par défaut
    // En production, récupérer l'utilisateur connecté
    let adminUser = await prisma.user.findFirst({
      where: { userType: 'HR_ADMIN' }
    })

    // Créer un admin par défaut s'il n'existe pas
    if (!adminUser) {
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin Karma Com',
          password: 'admin123', // En production, utiliser un hash
          userType: 'HR_ADMIN',
          profile: {
            create: {
              firstName: 'Admin',
              lastName: 'Karma Com',
              membershipStatus: 'ACTIVE'
            }
          }
        }
      })
    }

    // Créer l'entretien
    const interview = await prisma.appointment.create({
      data: {
        title,
        description: description || '',
        candidateId,
        scheduledAt: new Date(scheduledAt),
        type: type as AppointmentType,
        status: AppointmentStatus.SCHEDULED,
        createdById: adminUser.id
      },
      include: {
        candidate: {
          include: {
            profile: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    const formattedInterview = {
      id: interview.id,
      title: interview.title,
      description: interview.description,
      candidateId: interview.candidateId,
      candidateName: interview.candidate.name,
      candidateEmail: interview.candidate.email,
      scheduledAt: interview.scheduledAt.toISOString(),
      type: interview.type,
      status: interview.status,
      notes: interview.notes,
      createdAt: interview.createdAt.toISOString(),
      createdBy: interview.createdBy
    }

    return NextResponse.json(formattedInterview, { status: 201 })
  } catch (error) {
    console.error('Erreur lors de la création de l\'entretien:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
