'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Logo from '@/components/ui/Logo'
import { User, Lock, Eye, EyeOff } from 'lucide-react'

export default function ConnexionPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.email) {
      newErrors.email = 'L\'email est requis'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide'
    }

    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        // Connexion réussie
        localStorage.setItem('auth-token', data.token)
        localStorage.setItem('user', JSON.stringify(data.user))
        
        // Rediriger selon le type d'utilisateur
        if (data.user.userType === 'HR_ADMIN') {
          router.push('/dashboard')
        } else {
          router.push('/profil')
        }
      } else {
        // Erreur de connexion
        setErrors({ general: data.error || 'Erreur de connexion' })
      }
    } catch (error) {
      console.error('Erreur lors de la connexion:', error)
      setErrors({ general: 'Une erreur est survenue. Veuillez réessayer.' })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container-karma py-12">
        <div className="max-w-md mx-auto">
          {/* En-tête */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <Logo size="lg" showText={true} />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Connexion
            </h1>
            <p className="text-gray-600">
              Accédez à votre espace personnel
            </p>
          </div>

          {/* Formulaire */}
          <div className="karma-card p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Erreur générale */}
              {errors.general && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-600 text-sm">{errors.general}</p>
                </div>
              )}

              {/* Email */}
              <div>
                <label className="karma-label">
                  <User className="inline mr-2" size={16} />
                  Adresse email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`karma-input ${errors.email ? 'border-red-500' : ''}`}
                  placeholder="<EMAIL>"
                  disabled={isSubmitting}
                />
                {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
              </div>

              {/* Mot de passe */}
              <div>
                <label className="karma-label">
                  <Lock className="inline mr-2" size={16} />
                  Mot de passe
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`karma-input pr-10 ${errors.password ? 'border-red-500' : ''}`}
                    placeholder="Votre mot de passe"
                    disabled={isSubmitting}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
                {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
              </div>

              {/* Options */}
              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-gray-600">Se souvenir de moi</span>
                </label>
                <Link
                  href="/mot-de-passe-oublie"
                  className="text-sm text-primary-600 hover:text-primary-700"
                >
                  Mot de passe oublié ?
                </Link>
              </div>

              {/* Bouton de connexion */}
              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full karma-button-primary text-lg py-3 ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? 'Connexion en cours...' : 'Se connecter'}
              </button>
            </form>

            {/* Liens d'inscription */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-center text-gray-600 mb-4">
                Vous n'avez pas encore de compte ?
              </p>
              <div className="space-y-3">
                <Link
                  href="/inscription/association"
                  className="block w-full text-center py-2 px-4 border border-primary-600 text-primary-600 rounded-lg hover:bg-primary-50 transition-colors duration-200"
                >
                  Inscription Association
                </Link>
                <Link
                  href="/inscription/organisation"
                  className="block w-full text-center py-2 px-4 border border-secondary-600 text-secondary-600 rounded-lg hover:bg-secondary-50 transition-colors duration-200"
                >
                  Inscription Organisation
                </Link>
                <Link
                  href="/inscription/benevole"
                  className="block w-full text-center py-2 px-4 border border-accent-600 text-accent-600 rounded-lg hover:bg-accent-50 transition-colors duration-200"
                >
                  Devenir Bénévole
                </Link>
              </div>
            </div>
          </div>

          {/* Aide */}
          <div className="text-center mt-8">
            <p className="text-gray-600 text-sm">
              Besoin d'aide ? {' '}
              <Link
                href="/contact"
                className="text-primary-600 hover:text-primary-700"
              >
                Contactez-nous
              </Link>
            </p>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
