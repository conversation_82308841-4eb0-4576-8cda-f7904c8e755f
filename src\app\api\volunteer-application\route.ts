import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validation des données requises
    if (!data.firstName || !data.lastName || !data.email) {
      return NextResponse.json(
        { error: 'Les champs prénom, nom et email sont requis' },
        { status: 400 }
      )
    }

    if (!data.phoneNumber) {
      return NextResponse.json(
        { error: 'Le numéro de téléphone est requis' },
        { status: 400 }
      )
    }

    if (!data.currentStatus || !data.contributionPole) {
      return NextResponse.json(
        { error: 'La situation professionnelle et le pôle de contribution sont requis' },
        { status: 400 }
      )
    }

    if (!data.weeklyHours || !data.availability || !data.participationRhythm) {
      return NextResponse.json(
        { error: 'Les informations de disponibilité sont requises' },
        { status: 400 }
      )
    }

    if (!data.howDidYouKnowUs) {
      return NextResponse.json(
        { error: 'Veuillez indiquer comment vous nous avez connus' },
        { status: 400 }
      )
    }

    // Validation de l'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(data.email)) {
      return NextResponse.json(
        { error: 'Format d\'email invalide' },
        { status: 400 }
      )
    }

    // Vérifier si l'email existe déjà
    const existingApplication = await prisma.volunteerApplication.findFirst({
      where: { email: data.email }
    })

    if (existingApplication) {
      return NextResponse.json(
        { error: 'Une candidature avec cet email existe déjà' },
        { status: 409 }
      )
    }

    // Préparer les données pour la base de données
    const applicationData = {
      // Écran 2 - Informations personnelles de base
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      email: data.email.trim().toLowerCase(),
      birthDate: data.birthDate ? new Date(data.birthDate) : null,
      birthPlace: data.birthPlace?.trim() || null,
      
      // Écran 3 - Coordonnées
      personalEmail: data.personalEmail?.trim() || null,
      phoneNumber: data.phoneNumber.trim(),
      address: data.address?.trim() || null,
      
      // Écran 4 - Situation professionnelle
      currentStatus: data.currentStatus,
      otherStatus: data.currentStatus === 'Autre' ? data.otherStatus?.trim() || null : null,
      
      // Écran 4 - Pôle de contribution
      contributionPole: data.contributionPole,
      otherPole: data.contributionPole === 'Autre' ? data.otherPole?.trim() || null : null,
      
      // Écran 5 - Compétences spécifiques
      specificSkills: data.specificSkills || null,
      otherSkills: data.specificSkills === 'Autre' ? data.otherSkills?.trim() || null : null,
      
      // Écran 6 - Expérience et motivations
      associationExperience: data.associationExperience?.trim() || null,
      motivationReason: data.motivationReason?.trim() || null,
      objectives: data.objectives?.trim() || null,
      weeklyHours: data.weeklyHours,
      otherWeeklyHours: data.weeklyHours === 'Autre' ? data.otherWeeklyHours?.trim() || null : null,
      availability: data.availability,
      otherAvailability: data.availability === 'Autre' ? data.otherAvailability?.trim() || null : null,
      participationRhythm: data.participationRhythm,
      otherRhythm: data.participationRhythm === 'Autre' ? data.otherRhythm?.trim() || null : null,
      cvFiles: data.cvFiles ? JSON.stringify(data.cvFiles) : null,
      onlineProfile: data.onlineProfile?.trim() || null,
      
      // Écran 7 - Comment nous avez-vous connus
      howDidYouKnowUs: data.howDidYouKnowUs,
      otherSource: data.howDidYouKnowUs === 'Autre' ? data.otherSource?.trim() || null : null,
    }

    // Créer la candidature dans la base de données
    const volunteerApplication = await prisma.volunteerApplication.create({
      data: applicationData
    })

    // Log de l'activité
    await prisma.activityLog.create({
      data: {
        action: 'VOLUNTEER_APPLICATION_CREATED',
        description: `Nouvelle candidature bénévole de ${data.firstName} ${data.lastName}`,
        metadata: {
          applicationId: volunteerApplication.id,
          email: data.email,
          contributionPole: data.contributionPole,
          currentStatus: data.currentStatus
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Candidature envoyée avec succès',
      applicationId: volunteerApplication.id
    })

  } catch (error) {
    console.error('Erreur lors de la création de la candidature bénévole:', error)
    
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // Construire les filtres
    const where: any = {}
    
    if (status) {
      where.status = status
    }
    
    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { contributionPole: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Récupérer les candidatures
    const [applications, total] = await Promise.all([
      prisma.volunteerApplication.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phoneNumber: true,
          currentStatus: true,
          contributionPole: true,
          weeklyHours: true,
          status: true,
          createdAt: true,
          updatedAt: true
        }
      }),
      prisma.volunteerApplication.count({ where })
    ])

    return NextResponse.json({
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Erreur lors de la récupération des candidatures:', error)
    
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
