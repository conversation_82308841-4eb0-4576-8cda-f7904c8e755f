import { useState, useEffect } from 'react'

interface Constant {
  id: string
  name: string
}

interface Skill extends Constant {
  category?: string
}

interface Constants {
  organizationTypes: Constant[]
  sectors: Constant[]
  partnershipTypes: Constant[]
  skills: Skill[]
  skillsByCategory: Record<string, Constant[]>
  departments: Constant[]
  availabilityOptions: string[]
  employeeCountOptions: string[]
  budgetOptions: string[]
}

interface UseConstantsReturn {
  constants: Constants | null
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useConstants(): UseConstantsReturn {
  const [constants, setConstants] = useState<Constants | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchConstants = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/constants')
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      setConstants(data)
    } catch (err) {
      console.error('Erreur lors du chargement des constantes:', err)
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchConstants()
  }, [])

  return {
    constants,
    loading,
    error,
    refetch: fetchConstants
  }
}

// Hook spécialisé pour les types d'organisations
export function useOrganizationTypes() {
  const [organizationTypes, setOrganizationTypes] = useState<Constant[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchOrganizationTypes = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/constants/organization-types')
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data = await response.json()
        setOrganizationTypes(data)
      } catch (err) {
        console.error('Erreur lors du chargement des types d\'organisation:', err)
        setError(err instanceof Error ? err.message : 'Erreur inconnue')
      } finally {
        setLoading(false)
      }
    }

    fetchOrganizationTypes()
  }, [])

  return { organizationTypes, loading, error }
}

// Hook spécialisé pour les secteurs
export function useSectors() {
  const [sectors, setSectors] = useState<Constant[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSectors = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/constants/sectors')
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data = await response.json()
        setSectors(data)
      } catch (err) {
        console.error('Erreur lors du chargement des secteurs:', err)
        setError(err instanceof Error ? err.message : 'Erreur inconnue')
      } finally {
        setLoading(false)
      }
    }

    fetchSectors()
  }, [])

  return { sectors, loading, error }
}

// Hook spécialisé pour les compétences
export function useSkills() {
  const [skills, setSkills] = useState<Skill[]>([])
  const [skillsByCategory, setSkillsByCategory] = useState<Record<string, Constant[]>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSkills = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/constants/skills')
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data = await response.json()
        setSkills(data.skills)
        setSkillsByCategory(data.skillsByCategory)
      } catch (err) {
        console.error('Erreur lors du chargement des compétences:', err)
        setError(err instanceof Error ? err.message : 'Erreur inconnue')
      } finally {
        setLoading(false)
      }
    }

    fetchSkills()
  }, [])

  return { skills, skillsByCategory, loading, error }
}

// Hook spécialisé pour les départements
export function useDepartments() {
  const [departments, setDepartments] = useState<Constant[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/constants/departments')
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data = await response.json()
        setDepartments(data)
      } catch (err) {
        console.error('Erreur lors du chargement des départements:', err)
        setError(err instanceof Error ? err.message : 'Erreur inconnue')
      } finally {
        setLoading(false)
      }
    }

    fetchDepartments()
  }, [])

  return { departments, loading, error }
}
