# Dockerfile simplifié pour test
FROM node:18-alpine

WORKDIR /app

# Installer les dépendances système
RUN apk add --no-cache libc6-compat

# Copier les fichiers de configuration
COPY package.json package-lock.json* ./
COPY tsconfig.json ./
COPY next.config.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY prisma ./prisma

# Installer les dépendances
RUN npm ci

# Copier le code source
COPY . .

# Générer Prisma client
RUN npx prisma generate

# Créer le dossier public s'il n'existe pas
RUN mkdir -p public

# Build de l'application (sans base de données)
ENV DATABASE_URL="***********************************/dummy"
RUN npm run build

# Exposer le port
EXPOSE 3000

# Commande de démarrage
CMD ["npm", "start"]
