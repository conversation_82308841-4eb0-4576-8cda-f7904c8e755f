import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Logo from '@/components/ui/Logo'
import {
  Users,
  Building2,
  Heart,
  Calendar,
  Shield,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

export default function HomePage() {
  const features = [
    {
      icon: Users,
      title: 'Gestion des Bénévoles',
      description: 'Recrutement et suivi optimisés pour votre équipe de bénévoles'
    },
    {
      icon: Building2,
      title: 'Partenariats',
      description: 'Gestion des associations et organisations partenaires'
    },
    {
      icon: Calendar,
      title: 'Rendez-vous',
      description: 'Planification automatisée des entretiens et suivis'
    },
    {
      icon: Shield,
      title: 'Conformité RGPD',
      description: 'Protection des données selon les réglementations européennes'
    }
  ]

  const userTypes = [
    {
      title: 'Associations',
      description: 'Rejoignez notre réseau d\'associations partenaires',
      icon: Heart,
      href: '/inscription/association',
      color: 'from-karma-blue to-primary-800'
    },
    {
      title: 'Organisations',
      description: 'Entreprises et institutions publiques',
      icon: Building2,
      href: '/inscription/organisation',
      color: 'from-karma-pink to-secondary-700'
    },
    {
      title: 'Bénévoles',
      description: 'Engagez-vous dans des projets solidaires',
      icon: Users,
      href: '/inscription/benevole',
      color: 'from-karma-blue to-karma-pink'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Hero Section */}
      <section className="relative karma-gradient text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative container-karma py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <div className="mb-8 flex justify-center lg:justify-start">
                <Logo size="xl" showText={true} className="text-white" />
              </div>
              <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                Ensemble pour la
                <span className="block text-pink-200">Solidarité</span>
              </h1>
              <p className="text-xl lg:text-2xl mb-8 text-primary-100 max-w-2xl">
                Plateforme de gestion d'adhésion pour associations, organisations et bénévoles. 
                Simplifiez vos processus, optimisez votre impact.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link
                  href="/inscription/association"
                  className="bg-white text-primary-600 font-semibold py-3 px-8 rounded-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 flex items-center justify-center"
                >
                  Rejoindre le réseau
                  <ArrowRight className="ml-2" size={20} />
                </Link>
                <Link
                  href="/dashboard"
                  className="border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-primary-600 transition-all duration-200"
                >
                  Dashboard RH
                </Link>
              </div>
            </div>
            
            <div className="hidden lg:block">
              <div className="relative">
                <div className="karma-card p-8 float-animation">
                  <div className="flex items-center mb-4">
                    <CheckCircle className="text-green-500 mr-3" size={24} />
                    <span className="text-gray-800 font-medium">Processus automatisé</span>
                  </div>
                  <div className="flex items-center mb-4">
                    <CheckCircle className="text-green-500 mr-3" size={24} />
                    <span className="text-gray-800 font-medium">Interface intuitive</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="text-green-500 mr-3" size={24} />
                    <span className="text-gray-800 font-medium">Conformité RGPD</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Types d'utilisateurs */}
      <section className="py-20 bg-white">
        <div className="container-karma">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Qui peut rejoindre Karma Com ?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Notre plateforme s'adresse à tous les acteurs de la solidarité
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {userTypes.map((type, index) => (
              <Link
                key={index}
                href={type.href}
                className="group karma-card p-8 text-center hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className={`w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-r ${type.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
                  <type.icon className="text-white" size={32} />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {type.title}
                </h3>
                <p className="text-gray-600 mb-6">
                  {type.description}
                </p>
                <div className="flex items-center justify-center text-primary-600 font-medium group-hover:text-primary-700">
                  S'inscrire
                  <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform duration-200" size={20} />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Fonctionnalités */}
      <section className="py-20 bg-gray-50">
        <div className="container-karma">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Fonctionnalités principales
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Des outils modernes pour optimiser la gestion de votre réseau
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="karma-card p-6 text-center group hover:shadow-xl transition-all duration-300"
              >
                <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                  <feature.icon className="text-white" size={24} />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-secondary-600 text-white">
        <div className="container-karma text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            Prêt à rejoindre notre communauté ?
          </h2>
          <p className="text-xl mb-8 text-primary-100 max-w-2xl mx-auto">
            Commencez dès aujourd'hui et découvrez comment nous pouvons vous aider 
            à optimiser votre impact solidaire.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/inscription/association"
              className="bg-white text-primary-600 font-semibold py-3 px-8 rounded-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-105"
            >
              Commencer maintenant
            </Link>
            <Link
              href="/contact"
              className="border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-primary-600 transition-all duration-200"
            >
              Nous contacter
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
