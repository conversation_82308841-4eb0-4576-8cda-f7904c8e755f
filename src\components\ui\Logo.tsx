import React from 'react'
import Image from 'next/image'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  showText?: boolean
  className?: string
}

const Logo: React.FC<LogoProps> = ({
  size = 'md',
  showText = true,
  className = ''
}) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
    xl: 'h-24 w-24'
  }

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Logo SVG - Nouveau logo Karma Com Solidarité */}
      <div className={`${sizeClasses[size]} relative`}>
        <Image
          src="/Logo.svg"
          alt="Karma Com Solidarité Logo"
          width={100}
          height={100}
          className="w-full h-full object-contain drop-shadow-sm"
          priority
        />
      </div>
      
      {showText && (
        <div className="flex flex-col">
          <span className={`font-bold text-karma-blue ${textSizeClasses[size]} leading-tight`}>
            Karma Com
          </span>
          <span className={`font-semibold text-karma-blue ${size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'} leading-tight`}>
            Solidarité
          </span>
        </div>
      )}
    </div>
  )
}

export default Logo
