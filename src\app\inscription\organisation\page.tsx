'use client'

import React, { useState, useEffect } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { useConstants } from '@/hooks/useConstants'
import { Building2, Building, Mail, Phone, MapPin, Globe, FileText, User } from 'lucide-react'

export default function OrganisationInscriptionPage() {
  const { constants, loading: constantsLoading } = useConstants()
  const [formData, setFormData] = useState({
    // Informations de contact
    email: '',
    password: '',
    confirmPassword: '',
    
    // Informations personnelles du représentant
    firstName: '',
    lastName: '',
    phone: '',
    
    // Informations de l'organisation
    organizationName: '',
    siret: '',
    website: '',
    description: '',
    organizationType: '',
    sector: '',
    employeeCount: '',
    
    // Adresse
    address: '',
    city: '',
    postalCode: '',
    country: 'France',
    
    // Partenariat
    partnershipType: '',
    motivation: '',
    budget: '',
    
    // Conditions
    acceptTerms: false,
    acceptRGPD: false
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Utiliser les constantes de la base de données ou des valeurs par défaut
  const organizationTypes = constants?.organizationTypes || []
  const sectors = constants?.sectors || []
  const partnershipTypes = constants?.partnershipTypes || []
  const employeeCountOptions = constants?.employeeCountOptions || []
  const budgetOptions = constants?.budgetOptions || []

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
    
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Validation email
    if (!formData.email) {
      newErrors.email = 'L\'email est requis'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide'
    }

    // Validation mot de passe
    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères'
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas'
    }

    // Validation champs requis
    const requiredFields = ['firstName', 'lastName', 'organizationName', 'organizationType', 'description']
    requiredFields.forEach(field => {
      if (!formData[field as keyof typeof formData]) {
        newErrors[field] = 'Ce champ est requis'
      }
    })

    // Validation SIRET
    if (formData.siret && !/^\d{14}$/.test(formData.siret)) {
      newErrors.siret = 'Le SIRET doit contenir 14 chiffres'
    }

    // Validation acceptation des conditions
    if (!formData.acceptTerms) {
      newErrors.acceptTerms = 'Vous devez accepter les conditions d\'utilisation'
    }

    if (!formData.acceptRGPD) {
      newErrors.acceptRGPD = 'Vous devez accepter la politique de confidentialité'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/auth/register/organization', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        alert('Inscription réussie ! Vous recevrez un email de confirmation.')
        // Réinitialiser le formulaire
        setFormData({
          email: '',
          password: '',
          confirmPassword: '',
          firstName: '',
          lastName: '',
          phone: '',
          organizationName: '',
          siret: '',
          website: '',
          description: '',
          organizationType: '',
          sector: '',
          employeeCount: '',
          address: '',
          city: '',
          postalCode: '',
          country: 'France',
          partnershipType: '',
          motivation: '',
          budget: '',
          acceptTerms: false,
          acceptRGPD: false
        })
      } else {
        // Afficher les erreurs de validation
        if (data.details) {
          const newErrors: Record<string, string> = {}
          data.details.forEach((error: any) => {
            if (error.path && error.path.length > 0) {
              newErrors[error.path[0]] = error.message
            }
          })
          setErrors(newErrors)
        } else {
          alert(data.error || 'Une erreur est survenue lors de l\'inscription.')
        }
      }
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error)
      alert('Une erreur est survenue. Veuillez réessayer.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container-karma py-12">
        {/* En-tête */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-full flex items-center justify-center">
              <Building2 className="text-white" size={32} />
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Inscription Organisation
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Entreprises et institutions, rejoignez notre réseau de partenaires engagés
          </p>
        </div>

        {/* Formulaire */}
        <div className="max-w-4xl mx-auto">
          {constantsLoading && (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-secondary-600"></div>
              <p className="mt-2 text-gray-600">Chargement des options...</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-8">
            
            {/* Section Compte */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <User className="mr-3 text-secondary-600" size={24} />
                Informations de connexion
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="karma-label">Email *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.email ? 'border-red-500' : ''}`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                </div>
                <div>
                  <label className="karma-label">Téléphone</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="karma-input"
                    placeholder="+33 1 23 45 67 89"
                  />
                </div>
                <div>
                  <label className="karma-label">Mot de passe *</label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.password ? 'border-red-500' : ''}`}
                    placeholder="Minimum 8 caractères"
                  />
                  {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
                </div>
                <div>
                  <label className="karma-label">Confirmer le mot de passe *</label>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.confirmPassword ? 'border-red-500' : ''}`}
                    placeholder="Répétez le mot de passe"
                  />
                  {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>}
                </div>
              </div>
            </div>

            {/* Section Représentant */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <User className="mr-3 text-secondary-600" size={24} />
                Représentant de l'organisation
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="karma-label">Prénom *</label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.firstName ? 'border-red-500' : ''}`}
                    placeholder="Votre prénom"
                  />
                  {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
                </div>
                <div>
                  <label className="karma-label">Nom *</label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.lastName ? 'border-red-500' : ''}`}
                    placeholder="Votre nom"
                  />
                  {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
                </div>
              </div>
            </div>

            {/* Section Organisation */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <Building className="mr-3 text-secondary-600" size={24} />
                Informations de l'organisation
              </h2>
              <div className="space-y-6">
                <div>
                  <label className="karma-label">Nom de l'organisation *</label>
                  <input
                    type="text"
                    name="organizationName"
                    value={formData.organizationName}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.organizationName ? 'border-red-500' : ''}`}
                    placeholder="Nom officiel de votre organisation"
                  />
                  {errors.organizationName && <p className="text-red-500 text-sm mt-1">{errors.organizationName}</p>}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="karma-label">Type d'organisation *</label>
                    <select
                      name="organizationType"
                      value={formData.organizationType}
                      onChange={handleInputChange}
                      className={`karma-input ${errors.organizationType ? 'border-red-500' : ''}`}
                    >
                      <option value="">Sélectionnez un type</option>
                      {organizationTypes.map(type => (
                        <option key={type.id} value={type.name}>{type.name}</option>
                      ))}
                    </select>
                    {errors.organizationType && <p className="text-red-500 text-sm mt-1">{errors.organizationType}</p>}
                  </div>
                  <div>
                    <label className="karma-label">Secteur d'activité</label>
                    <select
                      name="sector"
                      value={formData.sector}
                      onChange={handleInputChange}
                      className="karma-input"
                    >
                      <option value="">Sélectionnez un secteur</option>
                      {sectors.map(sector => (
                        <option key={sector.id} value={sector.name}>{sector.name}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="karma-label">SIRET</label>
                    <input
                      type="text"
                      name="siret"
                      value={formData.siret}
                      onChange={handleInputChange}
                      className={`karma-input ${errors.siret ? 'border-red-500' : ''}`}
                      placeholder="14 chiffres"
                    />
                    {errors.siret && <p className="text-red-500 text-sm mt-1">{errors.siret}</p>}
                  </div>
                  <div>
                    <label className="karma-label">Site web</label>
                    <input
                      type="url"
                      name="website"
                      value={formData.website}
                      onChange={handleInputChange}
                      className="karma-input"
                      placeholder="https://votre-site.com"
                    />
                  </div>
                  <div>
                    <label className="karma-label">Nombre d'employés</label>
                    <select
                      name="employeeCount"
                      value={formData.employeeCount}
                      onChange={handleInputChange}
                      className="karma-input"
                    >
                      <option value="">Sélectionnez</option>
                      {employeeCountOptions.map(option => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="karma-label">Description de l'organisation *</label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className={`karma-input ${errors.description ? 'border-red-500' : ''}`}
                    placeholder="Décrivez les activités et missions de votre organisation..."
                  />
                  {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
                </div>
              </div>
            </div>

            {/* Section Adresse */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <MapPin className="mr-3 text-secondary-600" size={24} />
                Adresse
              </h2>
              <div className="space-y-6">
                <div>
                  <label className="karma-label">Adresse</label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="karma-input"
                    placeholder="Numéro et nom de rue"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="karma-label">Ville</label>
                    <input
                      type="text"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      className="karma-input"
                      placeholder="Ville"
                    />
                  </div>
                  <div>
                    <label className="karma-label">Code postal</label>
                    <input
                      type="text"
                      name="postalCode"
                      value={formData.postalCode}
                      onChange={handleInputChange}
                      className="karma-input"
                      placeholder="75001"
                    />
                  </div>
                  <div>
                    <label className="karma-label">Pays</label>
                    <select
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      className="karma-input"
                    >
                      <option value="France">France</option>
                      <option value="Belgique">Belgique</option>
                      <option value="Suisse">Suisse</option>
                      <option value="Canada">Canada</option>
                      <option value="Autre">Autre</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Section Partenariat */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <FileText className="mr-3 text-secondary-600" size={24} />
                Partenariat souhaité
              </h2>
              <div className="space-y-6">
                <div>
                  <label className="karma-label">Type de partenariat</label>
                  <select
                    name="partnershipType"
                    value={formData.partnershipType}
                    onChange={handleInputChange}
                    className="karma-input"
                  >
                    <option value="">Sélectionnez un type</option>
                    {partnershipTypes.map(type => (
                      <option key={type.id} value={type.name}>{type.name}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="karma-label">Budget envisagé (optionnel)</label>
                  <select
                    name="budget"
                    value={formData.budget}
                    onChange={handleInputChange}
                    className="karma-input"
                  >
                    <option value="">Sélectionnez une fourchette</option>
                    {budgetOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="karma-label">Motivation et objectifs</label>
                  <textarea
                    name="motivation"
                    value={formData.motivation}
                    onChange={handleInputChange}
                    rows={4}
                    className="karma-input"
                    placeholder="Expliquez vos motivations et objectifs pour ce partenariat..."
                  />
                </div>
              </div>
            </div>

            {/* Conditions */}
            <div className="form-section">
              <div className="space-y-4">
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    name="acceptTerms"
                    checked={formData.acceptTerms}
                    onChange={handleInputChange}
                    className="mt-1 mr-3"
                  />
                  <label className="text-sm text-gray-700">
                    J'accepte les{' '}
                    <a href="/conditions" className="text-secondary-600 hover:underline">
                      conditions d'utilisation
                    </a>{' '}
                    de Karma Com Solidarité *
                  </label>
                </div>
                {errors.acceptTerms && <p className="text-red-500 text-sm">{errors.acceptTerms}</p>}
                
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    name="acceptRGPD"
                    checked={formData.acceptRGPD}
                    onChange={handleInputChange}
                    className="mt-1 mr-3"
                  />
                  <label className="text-sm text-gray-700">
                    J'accepte la{' '}
                    <a href="/politique-confidentialite" className="text-secondary-600 hover:underline">
                      politique de confidentialité
                    </a>{' '}
                    et le traitement de mes données personnelles selon le RGPD *
                  </label>
                </div>
                {errors.acceptRGPD && <p className="text-red-500 text-sm">{errors.acceptRGPD}</p>}
              </div>
            </div>

            {/* Bouton de soumission */}
            <div className="text-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`bg-gradient-to-r from-secondary-600 to-secondary-700 text-white font-medium py-3 px-12 rounded-lg hover:from-secondary-700 hover:to-secondary-800 transition-all duration-200 transform hover:scale-105 text-lg ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? 'Inscription en cours...' : 'Soumettre ma candidature'}
              </button>
            </div>
          </form>
        </div>
      </div>

      <Footer />
    </div>
  )
}
