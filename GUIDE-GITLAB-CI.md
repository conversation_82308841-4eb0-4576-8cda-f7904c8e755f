# 🚀 Guide GitLab CI/CD - Karma Com Solidarité

## 🎯 Vue d'ensemble

Ce guide explique comment configurer et utiliser le pipeline GitLab CI/CD pour déployer automatiquement l'application Karma Com Solidarité.

## 📋 Architecture du Pipeline

### Stages du Pipeline
1. **Test** - Vérification de la qualité du code et tests
2. **Build** - Construction de l'image Docker
3. **Deploy** - Déploiement en staging et production
4. **Cleanup** - Nettoyage des ressources

### Environnements
- **Staging** : `https://staging.karma-com-solidarite.fr`
- **Production** : `https://karma-com-solidarite.fr`

## 🔧 Configuration GitLab

### 1. Variables d'Environnement

Allez dans **Settings > CI/CD > Variables** et ajoutez :

#### Variables Staging
```bash
# Serveur staging
STAGING_HOST=staging.karma-com-solidarite.fr
STAGING_USER=deploy
STAGING_SSH_PRIVATE_KEY=[Clé SSH privée pour staging]

# Base de données staging
POSTGRES_PASSWORD_STAGING=staging_password_2024
NEXTAUTH_SECRET_STAGING=staging_secret_key_very_long_and_secure
PGADMIN_PASSWORD_STAGING=staging_admin_password
```

#### Variables Production
```bash
# Serveur production
PRODUCTION_HOST=karma-com-solidarite.fr
PRODUCTION_USER=deploy
PRODUCTION_SSH_PRIVATE_KEY=[Clé SSH privée pour production]

# Base de données production
POSTGRES_PASSWORD_PROD=super_secure_production_password_2024
NEXTAUTH_SECRET_PROD=production_secret_key_very_long_and_extremely_secure
PGADMIN_PASSWORD_PROD=super_secure_admin_password
```

#### Variables Générales
```bash
# Registry GitLab (automatique)
CI_REGISTRY_IMAGE=[Automatiquement défini par GitLab]
CI_REGISTRY_USER=gitlab-ci-token
CI_REGISTRY_PASSWORD=[Token automatique]
```

### 2. Configuration des Runners

#### Runner Docker
```yaml
# .gitlab-runner/config.toml
[[runners]]
  name = "docker-runner"
  url = "https://gitlab.com/"
  token = "YOUR_RUNNER_TOKEN"
  executor = "docker"
  [runners.docker]
    image = "docker:24.0.5"
    privileged = true
    volumes = ["/var/run/docker.sock:/var/run/docker.sock", "/cache"]
```

## 🏗️ Structure des Fichiers

```
.
├── .gitlab-ci.yml                 # Pipeline principal
├── docker-compose.yml             # Développement local
├── docker-compose.staging.yml     # Environnement staging
├── docker-compose.production.yml  # Environnement production
├── Dockerfile.simple              # Image Docker optimisée
├── nginx/
│   ├── staging.conf               # Config Nginx staging
│   └── production.conf            # Config Nginx production
└── scripts/
    ├── deploy-staging.sh          # Script déploiement staging
    └── deploy-production.sh       # Script déploiement production
```

## 🚀 Workflow de Déploiement

### 1. Développement
```bash
# Branche: feature/nouvelle-fonctionnalite
git push origin feature/nouvelle-fonctionnalite
```
**Actions :** Tests uniquement

### 2. Staging
```bash
# Branche: develop
git checkout develop
git merge feature/nouvelle-fonctionnalite
git push origin develop
```
**Actions :** Tests + Build + Déploiement staging (manuel)

### 3. Production
```bash
# Branche: main
git checkout main
git merge develop
git tag v1.0.0
git push origin main --tags
```
**Actions :** Tests + Build + Déploiement production (manuel)

## 📊 Jobs du Pipeline

### Stage: Test

#### `lint`
- **Image :** `node:18-alpine`
- **Action :** Vérification de la qualité du code
- **Déclenchement :** MR, main, develop

#### `test`
- **Image :** `node:18-alpine`
- **Services :** `postgres:15-alpine`
- **Action :** Tests unitaires et d'intégration
- **Déclenchement :** MR, main, develop

#### `build_test`
- **Image :** `node:18-alpine`
- **Action :** Test de compilation
- **Déclenchement :** MR, main, develop

### Stage: Build

#### `build_docker`
- **Image :** `docker:24.0.5`
- **Services :** `docker:24.0.5-dind`
- **Action :** Construction et push de l'image Docker
- **Déclenchement :** main, develop, tags

### Stage: Deploy

#### `deploy_staging`
- **Image :** `alpine:latest`
- **Action :** Déploiement automatisé en staging
- **Déclenchement :** develop (manuel)

#### `deploy_production`
- **Image :** `alpine:latest`
- **Action :** Déploiement automatisé en production
- **Déclenchement :** main, tags (manuel)

## 🔒 Sécurité

### Clés SSH
```bash
# Générer une paire de clés pour le déploiement
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Ajouter la clé publique sur les serveurs
ssh-copy-id <EMAIL>
ssh-copy-id <EMAIL>
```

### Variables Sensibles
- ✅ Toutes les variables sensibles sont **masquées**
- ✅ Clés SSH stockées de manière **sécurisée**
- ✅ Mots de passe **générés aléatoirement**

### Accès Réseau
- 🔒 Production : Ports exposés uniquement en local
- 🔒 Staging : Accès restreint par IP
- 🔒 Base de données : Accès interne uniquement

## 🖥️ Configuration des Serveurs

### Serveur Staging
```bash
# Installation Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Installation Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Création du répertoire de déploiement
sudo mkdir -p /opt/karma-com-staging
sudo chown deploy:deploy /opt/karma-com-staging

# Clone du repository
cd /opt/karma-com-staging
git clone https://gitlab.com/votre-username/karma-com-solidarite.git .
```

### Serveur Production
```bash
# Même configuration que staging
# + Configuration SSL/TLS
# + Configuration firewall
# + Configuration monitoring
```

## 📈 Monitoring et Logs

### Logs d'Application
```bash
# Voir les logs en temps réel
docker-compose logs -f app

# Logs spécifiques
docker-compose logs --tail=100 app
```

### Monitoring de Santé
```bash
# Vérifier l'état des services
docker-compose ps

# Test de santé
curl -f https://karma-com-solidarite.fr/api/health
```

## 🔧 Dépannage

### Pipeline Échoue

#### Erreur de Build Docker
```bash
# Vérifier les logs du job
# Nettoyer le cache
# Reconstruire sans cache
```

#### Erreur de Déploiement
```bash
# Vérifier la connectivité SSH
ssh <EMAIL>

# Vérifier l'espace disque
df -h

# Vérifier les logs Docker
docker-compose logs
```

### Application Non Accessible

#### Vérifier les Services
```bash
# État des conteneurs
docker-compose ps

# Logs de l'application
docker-compose logs app

# Test de connectivité
curl -I http://localhost:3000
```

## 📚 Commandes Utiles

### Déploiement Manuel
```bash
# Staging
gitlab-ci-multi-runner exec docker deploy_staging

# Production
gitlab-ci-multi-runner exec docker deploy_production
```

### Rollback
```bash
# Revenir à la version précédente
docker-compose pull
docker-compose up -d --no-deps app
```

### Backup
```bash
# Backup manuel de la base
docker exec karma-com-postgres-prod pg_dump -U karma_user karma_com_db > backup.sql
```

## 🎉 Résultat Final

Avec cette configuration GitLab CI/CD :

- ✅ **Déploiement automatisé** en staging et production
- ✅ **Tests automatiques** sur chaque commit
- ✅ **Images Docker** optimisées et sécurisées
- ✅ **Zero-downtime deployment** en production
- ✅ **Rollback facile** en cas de problème
- ✅ **Monitoring** et logs centralisés

**Votre application Karma Com Solidarité est prête pour un déploiement professionnel !** 🚀
