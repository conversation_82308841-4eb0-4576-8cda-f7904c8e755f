import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { AppointmentType, AppointmentStatus } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const interviews = await prisma.appointment.findMany({
      include: {
        candidate: {
          include: {
            profile: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        scheduledAt: 'asc'
      }
    })

    // Enrichir avec les données des bénévoles si nécessaire
    const formattedInterviews = await Promise.all(
      interviews.map(async (interview) => {
        let candidateInfo = {
          name: interview.candidate.name,
          email: interview.candidate.email,
          type: 'classic'
        }

        // Vérifier si c'est un bénévole en cherchant dans les logs d'activité
        const activityLog = await prisma.activityLog.findFirst({
          where: {
            action: 'INTERVIEW_SCHEDULED',
            metadata: {
              path: ['interviewId'],
              equals: interview.id
            }
          }
        })

        if (activityLog && activityLog.metadata && (activityLog.metadata as any).candidateType === 'volunteer') {
          candidateInfo.type = 'volunteer'
        }

        return {
          id: interview.id,
          title: interview.title,
          description: interview.description,
          candidateId: interview.candidateId,
          candidateName: candidateInfo.name,
          candidateEmail: candidateInfo.email,
          candidateType: candidateInfo.type,
          scheduledAt: interview.scheduledAt.toISOString(),
          type: interview.type,
          status: interview.status,
          notes: interview.notes,
          createdAt: interview.createdAt.toISOString(),
          createdBy: interview.createdBy
        }
      })
    )

    return NextResponse.json(formattedInterviews)
  } catch (error) {
    console.error('Erreur lors de la récupération des entretiens:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Début création entretien...')
    const body = await request.json()
    const { title, description, candidateId, scheduledAt, type } = body

    console.log('📥 Données reçues pour création entretien:', body)

    // Validation des données
    if (!title || !candidateId || !scheduledAt || !type) {
      return NextResponse.json(
        { error: 'Données manquantes' },
        { status: 400 }
      )
    }

    // Vérifier que le candidat existe (système classique ou bénévole)
    let candidate = null
    let candidateType = 'classic'
    let candidateName = ''
    let candidateEmail = ''

    // D'abord, chercher dans les candidats classiques (User)
    candidate = await prisma.user.findUnique({
      where: { id: candidateId },
      include: { profile: true }
    })

    if (candidate) {
      candidateType = 'classic'
      candidateName = candidate.name
      candidateEmail = candidate.email
      console.log('✅ Candidat classique trouvé:', candidateName)
    } else {
      // Si pas trouvé, chercher dans les bénévoles (VolunteerApplication)
      const volunteerApplication = await prisma.volunteerApplication.findUnique({
        where: { id: candidateId }
      })

      if (volunteerApplication) {
        candidateType = 'volunteer'
        candidateName = `${volunteerApplication.firstName} ${volunteerApplication.lastName}`
        candidateEmail = volunteerApplication.email
        console.log('✅ Candidat bénévole trouvé:', candidateName)

        // Vérifier d'abord si un utilisateur existe déjà avec cet email
        const existingUser = await prisma.user.findUnique({
          where: { email: volunteerApplication.email },
          include: { profile: true }
        })

        if (existingUser) {
          console.log('✅ Utilisateur existant trouvé pour bénévole:', existingUser.id)
          candidate = existingUser
        } else {
          // Créer un utilisateur temporaire pour le bénévole
          try {
            console.log('🔄 Création utilisateur temporaire pour bénévole...')
            candidate = await prisma.user.create({
              data: {
                email: volunteerApplication.email,
                name: candidateName,
                phone: volunteerApplication.phoneNumber, // Utiliser le champ phone du User
                password: 'temp_password', // Mot de passe temporaire
                userType: 'VOLUNTEER',
                profile: {
                  create: {
                    firstName: volunteerApplication.firstName,
                    lastName: volunteerApplication.lastName,
                    membershipStatus: 'PENDING' // Utiliser une valeur valide de MembershipStatus
                  }
                }
              }
            })
            console.log('✅ Utilisateur temporaire créé pour bénévole:', candidate.id)
          } catch (userCreationError) {
            console.error('❌ Erreur création utilisateur temporaire:', userCreationError)
            throw new Error(`Impossible de créer l'utilisateur pour le bénévole: ${userCreationError instanceof Error ? userCreationError.message : 'Erreur inconnue'}`)
          }
        }
      }
    }

    if (!candidate) {
      console.log('❌ Candidat non trouvé avec ID:', candidateId)
      return NextResponse.json(
        { error: 'Candidat non trouvé' },
        { status: 404 }
      )
    }

    // Pour l'instant, on utilise un admin par défaut
    // En production, récupérer l'utilisateur connecté
    let adminUser = await prisma.user.findFirst({
      where: { userType: 'HR_ADMIN' }
    })

    // Créer un admin par défaut s'il n'existe pas
    if (!adminUser) {
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin Karma Com',
          password: 'admin123', // En production, utiliser un hash
          userType: 'HR_ADMIN',
          profile: {
            create: {
              firstName: 'Admin',
              lastName: 'Karma Com',
              membershipStatus: 'ACTIVE'
            }
          }
        }
      })
    }

    // Créer l'entretien
    const interview = await prisma.appointment.create({
      data: {
        title,
        description: description || '',
        candidateId: candidate.id, // Utiliser l'ID du candidat (classique ou temporaire pour bénévole)
        scheduledAt: new Date(scheduledAt),
        type: type as AppointmentType,
        status: AppointmentStatus.SCHEDULED,
        createdById: adminUser.id
      },
      include: {
        candidate: {
          include: {
            profile: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // Mettre à jour le statut du bénévole si c'est un candidat bénévole
    if (candidateType === 'volunteer') {
      await prisma.volunteerApplication.update({
        where: { id: candidateId }, // ID original du bénévole
        data: { status: 'INTERVIEW_SCHEDULED' }
      })
      console.log('✅ Statut bénévole mis à jour: INTERVIEW_SCHEDULED')
    }

    // Log de l'activité
    await prisma.activityLog.create({
      data: {
        action: 'INTERVIEW_SCHEDULED',
        description: `Entretien planifié pour ${candidateName} le ${new Date(scheduledAt).toLocaleDateString('fr-FR')}`,
        metadata: {
          interviewId: interview.id,
          originalCandidateId: candidateId, // ID original (User ou VolunteerApplication)
          finalCandidateId: candidate.id, // ID utilisé dans l'entretien (toujours User)
          candidateType,
          candidateName,
          interviewType: type,
          scheduledAt: scheduledAt
        }
      }
    })

    console.log('✅ Entretien créé avec succès:', interview.id)

    const formattedInterview = {
      id: interview.id,
      title: interview.title,
      description: interview.description,
      candidateId: interview.candidateId,
      candidateName: interview.candidate.name,
      candidateEmail: interview.candidate.email,
      scheduledAt: interview.scheduledAt.toISOString(),
      type: interview.type,
      status: interview.status,
      notes: interview.notes,
      createdAt: interview.createdAt.toISOString(),
      createdBy: interview.createdBy
    }

    return NextResponse.json(formattedInterview, { status: 201 })
  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'entretien:', error)
    console.error('❌ Stack trace:', error instanceof Error ? error.stack : 'Pas de stack trace')

    // Retourner une erreur plus détaillée en développement
    const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue'
    const isDevelopment = process.env.NODE_ENV === 'development'

    return NextResponse.json(
      {
        error: isDevelopment ? `Erreur détaillée: ${errorMessage}` : 'Erreur interne du serveur',
        details: isDevelopment ? error : undefined
      },
      { status: 500 }
    )
  }
}
