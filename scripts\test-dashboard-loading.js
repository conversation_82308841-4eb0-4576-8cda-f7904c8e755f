// Script de test pour vérifier que le chargement infini du dashboard est corrigé
const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function testDashboardLoading() {
  console.log('🔄 Test du Chargement Dashboard - Karma Com Solidarité')
  console.log('====================================================')
  
  try {
    // Test 1: Vérifier que l'API dashboard répond correctement
    console.log('📊 Test 1: API Dashboard Stats...')
    const apiResponse = await fetch(`${BASE_URL}/api/dashboard/stats`)
    
    if (apiResponse.ok) {
      const data = await apiResponse.json()
      console.log('✅ API Dashboard répond correctement')
      console.log(`   Total candidats: ${data.overview?.totalCandidates || 0}`)
      console.log(`   En attente: ${data.overview?.pendingApplications || 0}`)
    } else {
      console.log(`❌ API Dashboard erreur: HTTP ${apiResponse.status}`)
      const errorText = await apiResponse.text()
      console.log(`   Détails: ${errorText}`)
      return false
    }
    
    // Test 2: Vérifier que la page dashboard se charge
    console.log('\n🌐 Test 2: Page Dashboard...')
    const pageResponse = await fetch(`${BASE_URL}/dashboard`)
    
    if (pageResponse.ok) {
      const html = await pageResponse.text()
      
      // Vérifications de base
      const checks = [
        { name: 'Page contient Dashboard', test: html.includes('Dashboard') || html.includes('dashboard') },
        { name: 'Pas d\'erreur JS visible', test: !html.includes('Error:') && !html.includes('error') },
        { name: 'Composants React chargés', test: html.includes('__NEXT_DATA__') || html.includes('next') },
        { name: 'Styles CSS présents', test: html.includes('karma-') || html.includes('bg-') || html.includes('text-') }
      ]
      
      console.log('✅ Page Dashboard accessible')
      
      let checksPassed = 0
      checks.forEach(check => {
        if (check.test) {
          console.log(`   ✅ ${check.name}`)
          checksPassed++
        } else {
          console.log(`   ❌ ${check.name}`)
        }
      })
      
      console.log(`   📊 Vérifications: ${checksPassed}/${checks.length}`)
      
      if (checksPassed >= checks.length * 0.75) {
        console.log('✅ Page Dashboard semble fonctionnelle')
        return true
      } else {
        console.log('⚠️ Page Dashboard a des problèmes')
        return false
      }
      
    } else {
      console.log(`❌ Page Dashboard inaccessible: HTTP ${pageResponse.status}`)
      return false
    }
    
  } catch (error) {
    console.log('❌ Erreur lors du test:', error.message)
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 L\'application n\'est pas démarrée. Lancez: npm run dev')
    }
    
    return false
  }
}

async function testAPIPerformance() {
  console.log('\n⚡ Test 3: Performance API...')
  
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${BASE_URL}/api/dashboard/stats`)
    const endTime = Date.now()
    const responseTime = endTime - startTime
    
    if (response.ok) {
      console.log(`✅ API répond en ${responseTime}ms`)
      
      if (responseTime < 1000) {
        console.log('   🚀 Performance excellente (< 1s)')
      } else if (responseTime < 3000) {
        console.log('   ⚡ Performance correcte (< 3s)')
      } else {
        console.log('   🐌 Performance lente (> 3s)')
      }
      
      return responseTime < 5000 // Acceptable si < 5s
    } else {
      console.log(`❌ API erreur: HTTP ${response.status}`)
      return false
    }
  } catch (error) {
    console.log('❌ Erreur performance:', error.message)
    return false
  }
}

async function testMultipleRequests() {
  console.log('\n🔄 Test 4: Requêtes Multiples (test anti-boucle infinie)...')
  
  try {
    // Faire plusieurs requêtes rapidement pour simuler un rechargement
    const promises = []
    for (let i = 0; i < 5; i++) {
      promises.push(fetch(`${BASE_URL}/api/dashboard/stats`))
    }
    
    const startTime = Date.now()
    const responses = await Promise.all(promises)
    const endTime = Date.now()
    
    const successCount = responses.filter(r => r.ok).length
    const totalTime = endTime - startTime
    
    console.log(`✅ ${successCount}/5 requêtes réussies en ${totalTime}ms`)
    
    if (successCount === 5 && totalTime < 10000) {
      console.log('   🎯 Pas de boucle infinie détectée')
      return true
    } else {
      console.log('   ⚠️ Possible problème de performance')
      return false
    }
    
  } catch (error) {
    console.log('❌ Erreur test multiple:', error.message)
    return false
  }
}

async function main() {
  console.log('🧪 Tests de Chargement Dashboard')
  console.log('=================================')
  
  // Vérifier que l'application est accessible
  try {
    const response = await fetch(`${BASE_URL}`)
    if (!response.ok) {
      throw new Error(`Application non accessible: HTTP ${response.status}`)
    }
    console.log('✅ Application accessible\n')
  } catch (error) {
    console.log('❌ Application non accessible:', error.message)
    console.log('💡 Assurez-vous que l\'application est démarrée avec "npm run dev"')
    process.exit(1)
  }
  
  // Exécuter les tests
  const test1 = await testDashboardLoading()
  const test2 = await testAPIPerformance()
  const test3 = await testMultipleRequests()
  
  // Résumé
  console.log('\n📋 Résumé des Tests de Chargement')
  console.log('==================================')
  
  const tests = [
    { name: 'Dashboard Loading', passed: test1 },
    { name: 'API Performance', passed: test2 },
    { name: 'Anti-Boucle Infinie', passed: test3 }
  ]
  
  const passedCount = tests.filter(t => t.passed).length
  
  tests.forEach(test => {
    console.log(`${test.passed ? '✅' : '❌'} ${test.name}`)
  })
  
  console.log(`\n📊 Score: ${passedCount}/${tests.length}`)
  
  if (passedCount === tests.length) {
    console.log('\n🎉 Tous les tests de chargement sont passés!')
    console.log('\n✅ Le problème de chargement infini est corrigé:')
    console.log('   • useCallback utilisé pour stabiliser les fonctions')
    console.log('   • Dépendances useEffect optimisées')
    console.log('   • API répond correctement')
    console.log('   • Pas de boucle infinie détectée')
    console.log('\n🌐 Dashboard accessible: http://localhost:3000/dashboard')
  } else {
    console.log('\n⚠️ Certains tests ont échoué.')
    console.log('💡 Vérifiez:')
    console.log('   • Les logs de la console navigateur')
    console.log('   • Les erreurs dans le terminal de dev')
    console.log('   • La base de données est-elle accessible?')
    process.exit(1)
  }
}

// Vérifier si node-fetch est disponible
try {
  require('node-fetch')
} catch (error) {
  console.log('❌ node-fetch n\'est pas installé')
  console.log('💡 Il devrait être installé avec les devDependencies')
  process.exit(1)
}

main()
