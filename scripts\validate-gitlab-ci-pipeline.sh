#!/bin/bash

# Script de validation du pipeline GitLab CI/CD
echo "🔍 Validation Pipeline GitLab CI/CD - Karma Com Solidarité"
echo "=========================================================="

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
log "Vérification des prérequis..."

if [ ! -f ".gitlab-ci.yml" ]; then
    error "Fichier .gitlab-ci.yml non trouvé"
    exit 1
fi

if [ ! -f "package.json" ]; then
    error "Fichier package.json non trouvé"
    exit 1
fi

success "Prérequis validés"

# Validation de la syntaxe YAML
log "Validation de la syntaxe YAML..."

if command -v yamllint &> /dev/null; then
    if yamllint .gitlab-ci.yml; then
        success "Syntaxe YAML valide"
    else
        warning "Erreurs de syntaxe YAML détectées"
    fi
else
    warning "yamllint non installé, validation YAML ignorée"
fi

# Vérification des stages définis
log "Vérification des stages..."

expected_stages=("validate" "test" "build" "deploy")
for stage in "${expected_stages[@]}"; do
    if grep -q "stage: $stage" .gitlab-ci.yml; then
        success "Stage '$stage' défini"
    else
        error "Stage '$stage' manquant"
    fi
done

# Vérification des jobs critiques
log "Vérification des jobs critiques..."

critical_jobs=(
    "validate"
    "test:unit"
    "test:design"
    "test:integration"
    "build:application"
    "build:docker"
    "deploy:production"
)

for job in "${critical_jobs[@]}"; do
    if grep -q "^$job:" .gitlab-ci.yml; then
        success "Job '$job' défini"
    else
        error "Job '$job' manquant"
    fi
done

# Vérification des variables d'environnement
log "Vérification des variables d'environnement..."

required_vars=(
    "VPS_IP"
    "VPS_USER"
    "DOMAIN"
    "NODE_VERSION"
    "DATABASE_URL"
)

for var in "${required_vars[@]}"; do
    if grep -q "$var:" .gitlab-ci.yml; then
        success "Variable '$var' définie"
    else
        warning "Variable '$var' manquante"
    fi
done

# Vérification des scripts référencés
log "Vérification des scripts référencés..."

referenced_scripts=(
    "scripts/validate-gitlab-ci.sh"
    "scripts/verify-config.sh"
    "scripts/prepare-vps-deploy.sh"
    "deploy-to-vps-no-sudo.sh"
)

for script in "${referenced_scripts[@]}"; do
    if [ -f "$script" ]; then
        success "Script '$script' présent"
        if [ -x "$script" ]; then
            success "Script '$script' exécutable"
        else
            warning "Script '$script' non exécutable"
        fi
    else
        error "Script '$script' manquant"
    fi
done

# Vérification des tests dans package.json
log "Vérification des tests dans package.json..."

test_scripts=(
    "test:dashboard-direct"
    "test:inscriptions"
    "test:dashboard"
    "test:auth"
    "test:design"
    "test:docker"
    "test:docker-compose"
)

for test in "${test_scripts[@]}"; do
    if grep -q "\"$test\":" package.json; then
        success "Test '$test' défini dans package.json"
    else
        warning "Test '$test' manquant dans package.json"
    fi
done

# Vérification des Dockerfiles
log "Vérification des Dockerfiles..."

dockerfiles=("Dockerfile.simple" "Dockerfile.runtime")
for dockerfile in "${dockerfiles[@]}"; do
    if [ -f "$dockerfile" ]; then
        success "Dockerfile '$dockerfile' présent"
    else
        warning "Dockerfile '$dockerfile' manquant"
    fi
done

# Vérification de la configuration Docker Compose
log "Vérification de Docker Compose..."

if [ -f "docker-compose.yml" ]; then
    success "docker-compose.yml présent"
else
    error "docker-compose.yml manquant"
fi

if [ -f "docker-compose.production.yml" ]; then
    success "docker-compose.production.yml présent"
else
    warning "docker-compose.production.yml manquant"
fi

# Test de validation GitLab CI (si gitlab-ci-local est disponible)
log "Test de validation GitLab CI..."

if command -v gitlab-ci-local &> /dev/null; then
    if gitlab-ci-local --list; then
        success "Pipeline GitLab CI valide"
    else
        warning "Erreurs dans le pipeline GitLab CI"
    fi
else
    warning "gitlab-ci-local non installé, validation ignorée"
fi

# Résumé de la validation
echo ""
log "Résumé de la Validation Pipeline"
echo "================================="

echo ""
echo "📋 Pipeline GitLab CI/CD configuré avec:"
echo "   - 4 stages: validate, test, build, deploy"
echo "   - 12+ jobs incluant tests et déploiement"
echo "   - Intégration complète des scripts existants"
echo "   - Déploiement automatisé sur VPS"
echo ""
echo "🧪 Tests intégrés:"
echo "   - Tests unitaires et fonctionnels"
echo "   - Tests de design et interface"
echo "   - Tests d'intégration"
echo "   - Tests Docker et Docker Compose"
echo ""
echo "🚀 Déploiement:"
echo "   - Staging (manuel) pour les branches"
echo "   - Production (manuel) pour main/master"
echo "   - Monitoring post-déploiement"
echo "   - Rollback en cas de problème"
echo ""
echo "🔧 Variables GitLab CI requises:"
echo "   - SSH_PRIVATE_KEY: Clé SSH pour accès VPS"
echo "   - CI_REGISTRY_USER: Utilisateur registry Docker"
echo "   - CI_REGISTRY_PASSWORD: Mot de passe registry"
echo ""
echo "📊 URLs après déploiement:"
echo "   - Application: https://kcs.zidani.org"
echo "   - Dashboard: https://kcs.zidani.org/dashboard"
echo ""

success "Validation du pipeline GitLab CI/CD terminée"
