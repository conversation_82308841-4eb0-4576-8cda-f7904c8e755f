#!/bin/bash

# === CONFIGURATION ===
NEW_USER="hamza"
SSH_PORT=22
PUBLIC_KEY="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDkeEoftZB8xmgKECziOOH+GD+e02oUdkvpKykOF2SHOkC6/Lh+Qm227oQbzfBclGwEDxGW4rZBU88fTFnPJNr8ms0rZO0ue5eC84e44tZ1IhILqGYmsoq5inKpU6VKLx5CI+o8eKC9H0GhPboN6JecvPjI67mBkV6lKbrQ2sBaxoXM+ufhNdNZy8eMmDD/O0c89UrouJV2Q4cdZJolfcClColmFKeN3o9NhL6a2HbIZgHvTGTuOqu/rN9m2PLkgsOsfGS+3igqY0DA6qUfZyfG1PwVwk5K0q3pPu3u3hu10VRFLA7gTb667VEmgbjjP584bRIYKSsVCCzkRUJa//zt gcatrans\hamza.bedoui@GCA22368
"  # Remplace par ta vraie clé publique

# === Créer un utilisateur avec dossier home ===
echo "[+] Création de l'utilisateur $NEW_USER"
adduser --disabled-password --gecos "" $NEW_USER
usermod -aG sudo $NEW_USER

# === Ajouter la clé SSH ===
echo "[+] Configuration de l'accès SSH par clé"
mkdir -p /home/<USER>/.ssh
echo "$PUBLIC_KEY" > /home/<USER>/.ssh/authorized_keys
chmod 600 /home/<USER>/.ssh/authorized_keys
chmod 700 /home/<USER>/.ssh
chown -R $NEW_USER:$NEW_USER /home/<USER>/.ssh

# === Config SSH ===
SSHD_CONFIG="/etc/ssh/sshd_config"
echo "[+] Sauvegarde de sshd_config"
cp $SSHD_CONFIG ${SSHD_CONFIG}.bak

echo "[+] Configuration SSH : port $SSH_PORT, pas de root, pas de mot de passe"
sed -i "s/^#*Port .*/Port $SSH_PORT/" $SSHD_CONFIG
sed -i "s/^#*PermitRootLogin .*/PermitRootLogin no/" $SSHD_CONFIG
sed -i "s/^#*PasswordAuthentication .*/PasswordAuthentication no/" $SSHD_CONFIG
sed -i "s/^#*ChallengeResponseAuthentication .*/ChallengeResponseAuthentication no/" $SSHD_CONFIG
sed -i "s/^#*UsePAM .*/UsePAM no/" $SSHD_CONFIG

# === Pare-feu UFW ===
echo "[+] Configuration du pare-feu UFW"
apt update -y && apt install -y ufw
ufw allow $SSH_PORT/tcp
ufw allow http
ufw allow https
ufw --force enable

# === Installation de fail2ban ===
echo "[+] Installation de fail2ban"
apt install -y fail2ban
systemctl enable fail2ban
systemctl start fail2ban

# === Redémarrage SSH ===
echo "[+] Redémarrage du service SSH"
systemctl restart ssh

echo "✅ VPS sécurisé."
echo "➡️ Connecte-toi maintenant avec :"
echo "   ssh -p $SSH_PORT $NEW_USER@$(hostname -I | awk '{print $1}')"
