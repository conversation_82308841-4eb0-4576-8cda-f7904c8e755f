# 🧪 Test Manuel - Entretiens Dashboard

## 🎯 Objectif
Vérifier que la création et l'affichage des entretiens fonctionnent correctement dans le dashboard.

## 📋 Prérequis
- ✅ Application démarrée : `npm run dev`
- ✅ Base de données accessible
- ✅ Candidats présents dans la base

## 🔍 Tests à Effectuer

### 1. 🌐 Accès au Dashboard
1. **Ouvrir** : http://localhost:3000/dashboard
2. **Vérifier** : Page se charge sans erreur
3. **Vérifier** : 3 onglets visibles (Vue d'ensemble, Candidatures, Entretiens)

### 2. 📅 Onglet Entretiens
1. **Cliquer** sur l'onglet "Entretiens"
2. **Vérifier** : Section "Entretiens Planifiés" visible
3. **Vérifier** : Bouton "Nouvel entretien" présent
4. **Vérifier** : Bouton "Actualiser" présent
5. **Vérifier** : Compteur d'entretiens affiché (ex: "Entretiens Planifiés (4)")

### 3. 📝 Création d'Entretien
1. **Cliquer** sur "Nouvel entretien"
2. **Vérifier** : Formulaire s'affiche
3. **Remplir** le formulaire :
   - **Titre** : "Test Entretien Manuel"
   - **Type** : "Découverte"
   - **Candidat** : Sélectionner un candidat dans la liste
   - **Date et heure** : Choisir une date future
   - **Description** : "Test de création manuelle"
4. **Cliquer** : "Planifier l'entretien"

### 4. ✅ Vérifications Post-Création
1. **Vérifier** : Message de succès vert s'affiche
2. **Vérifier** : Formulaire se ferme automatiquement
3. **Vérifier** : Nouvel entretien apparaît dans la liste
4. **Vérifier** : Compteur d'entretiens augmente

### 5. 🔄 Test d'Actualisation
1. **Cliquer** : Bouton "Actualiser"
2. **Vérifier** : Indicateur de chargement s'affiche
3. **Vérifier** : Liste se recharge
4. **Vérifier** : Entretiens toujours présents

### 6. 🗄️ Vérification Base de Données
1. **Ouvrir** un terminal
2. **Exécuter** : `curl http://localhost:3000/api/interviews`
3. **Vérifier** : Nouvel entretien présent dans la réponse JSON

## 📊 Résultats Attendus

### Interface Utilisateur
- ✅ **Formulaire responsive** et bien stylé
- ✅ **Messages de feedback** clairs
- ✅ **Chargement fluide** sans erreurs
- ✅ **Compteurs mis à jour** en temps réel

### Données
- ✅ **Entretien enregistré** en base PostgreSQL
- ✅ **Informations complètes** : titre, candidat, date, type
- ✅ **Statut initial** : "SCHEDULED"
- ✅ **Relations correctes** : candidat lié, admin créateur

### API
- ✅ **POST /api/interviews** : Création réussie
- ✅ **GET /api/interviews** : Liste mise à jour
- ✅ **Réponses JSON** bien formatées

## 🐛 Problèmes Possibles

### Formulaire ne se soumet pas
**Causes possibles :**
- Champs obligatoires non remplis
- Date invalide (passée)
- Candidat non sélectionné

**Solutions :**
- Vérifier la validation des champs
- Choisir une date future
- Sélectionner un candidat valide

### Entretien non affiché
**Causes possibles :**
- Erreur de chargement API
- Problème de state React
- Erreur base de données

**Solutions :**
- Cliquer sur "Actualiser"
- Vérifier la console navigateur
- Tester l'API directement

### Message d'erreur
**Causes possibles :**
- Base de données inaccessible
- Candidat inexistant
- Erreur serveur

**Solutions :**
- Vérifier PostgreSQL : `docker ps`
- Vérifier les logs serveur
- Tester avec un autre candidat

## 🔧 Commandes de Debug

### Vérifier l'API
```bash
# Lister les entretiens
curl http://localhost:3000/api/interviews

# Lister les candidats
curl http://localhost:3000/api/candidates

# Créer un entretien via API
curl -X POST http://localhost:3000/api/interviews \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test API",
    "candidateId": "CANDIDATE_ID",
    "scheduledAt": "2024-12-25T14:00:00Z",
    "type": "DISCOVERY"
  }'
```

### Vérifier la Base de Données
```bash
# Accéder à PostgreSQL
docker exec -it karma-postgres psql -U karma_user -d karma_com_db

# Lister les entretiens
SELECT * FROM "Appointment";

# Lister les candidats
SELECT id, name, email FROM "User" WHERE "userType" != 'HR_ADMIN';
```

### Tests Automatiques
```bash
# Test complet des entretiens
npm run test:interviews

# Test des onglets dashboard
npm run test:tabs

# Test général du dashboard
npm run test:dashboard
```

## 📈 Métriques de Succès

### Performance
- ⏱️ **Création** : < 2 secondes
- ⏱️ **Chargement** : < 1 seconde
- ⏱️ **Actualisation** : < 500ms

### Fonctionnalité
- 🎯 **Taux de succès** : 100% des créations
- 🎯 **Persistance** : Données conservées après rechargement
- 🎯 **Cohérence** : Compteurs et listes synchronisés

### Expérience Utilisateur
- 😊 **Feedback immédiat** : Messages de succès/erreur
- 😊 **Interface intuitive** : Formulaire clair
- 😊 **Navigation fluide** : Pas de rechargement de page

## ✅ Checklist de Validation

- [ ] Dashboard accessible
- [ ] Onglet Entretiens fonctionnel
- [ ] Formulaire de création complet
- [ ] Validation des champs
- [ ] Soumission réussie
- [ ] Message de succès affiché
- [ ] Entretien dans la liste
- [ ] Compteur mis à jour
- [ ] Bouton actualiser fonctionnel
- [ ] Données persistantes
- [ ] API accessible
- [ ] Base de données cohérente

## 🎉 Résultat Final

Si tous les tests passent :
- ✅ **Création d'entretiens** : Fonctionnelle
- ✅ **Affichage en temps réel** : Opérationnel
- ✅ **Persistance des données** : Garantie
- ✅ **Interface utilisateur** : Complète et intuitive

**Le système de gestion des entretiens est prêt pour la production !** 🚀
