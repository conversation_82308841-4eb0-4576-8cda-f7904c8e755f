#!/bin/bash

# Script de réparation rapide pour le dashboard
# Résout les problèmes courants après mise à jour des dépendances

set -e

echo "🔧 Réparation du Dashboard Karma Com"
echo "===================================="

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "package.json" ]; then
    echo "❌ Erreur: package.json non trouvé"
    echo "💡 Assurez-vous d'être dans le répertoire racine du projet"
    exit 1
fi

echo "📦 Nettoyage et réinstallation des dépendances..."

# Nettoyer node_modules et package-lock
rm -rf node_modules package-lock.json

# Réinstaller les dépendances
npm install

echo "🗄️ Régénération du client Prisma..."

# Régénérer le client Prisma
npx prisma generate

echo "🐳 Vérification de PostgreSQL..."

# Vérifier si PostgreSQL est démarré
if ! docker ps | grep -q postgres; then
    echo "🚀 Démarrage de PostgreSQL..."
    docker-compose up -d postgres pgadmin
    
    echo "⏳ Attente du démarrage de PostgreSQL..."
    sleep 10
else
    echo "✅ PostgreSQL déjà démarré"
fi

echo "📊 Application des migrations..."

# Appliquer les migrations
npx prisma migrate dev --name "fix-after-update"

echo "🧪 Test de la connexion à la base..."

# Tester la connexion
npm run diagnose

echo "🌱 Vérification des données..."

# Vérifier si les données existent, sinon les créer
echo "  📋 Vérification des constantes..."
npm run db:seed-constants

echo "  🎭 Vérification des fake data..."
npm run db:seed-fake

echo "  👥 Vérification des comptes de base..."
npm run db:init

echo ""
echo "🎉 Réparation terminée!"
echo ""
echo "🚀 Pour démarrer l'application:"
echo "  npm run dev"
echo ""
echo "🧪 Pour tester le dashboard:"
echo "  npm run test:dashboard"
echo ""
echo "🔍 En cas de problème:"
echo "  npm run diagnose"
