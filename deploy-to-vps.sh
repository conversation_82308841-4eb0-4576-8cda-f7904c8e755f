#!/bin/bash

# Script de déploiement Karma Com Solidarité sur VPS
# Usage: ./deploy-to-vps.sh

set -e

# Configuration VPS
VPS_IP="*************"
VPS_USER="vpsadmin"
DOMAIN="kcz.zidani.org"
APP_NAME="karma-com-solidarite"
DEPLOY_DIR="/opt/$APP_NAME"

# Configuration de l'application
DB_NAME="karma_com_db"
DB_USER="karma_user"
DB_PASSWORD="karma_secure_password_2024"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Vérification des prérequis locaux
log "Vérification des prérequis locaux..."

if [ ! -f "package.json" ]; then
    error "Ce script doit être exécuté depuis la racine du projet Karma Com"
fi

if [ ! -f "Dockerfile.simple" ]; then
    error "Dockerfile.simple non trouvé. Assurez-vous qu'il existe."
fi

if ! command -v ssh &> /dev/null; then
    error "SSH n'est pas installé"
fi

success "Prérequis locaux validés"

# Test de connexion SSH
log "Test de connexion SSH au VPS..."
if ssh -o ConnectTimeout=10 -o BatchMode=yes $VPS_USER@$VPS_IP exit 2>/dev/null; then
    success "Connexion SSH au VPS réussie"
else
    error "Impossible de se connecter au VPS. Vérifiez vos clés SSH et la configuration."
fi

# Création du répertoire de déploiement sur le VPS
log "Préparation du VPS pour le déploiement..."
ssh $VPS_USER@$VPS_IP << 'EOF'
    # Créer le répertoire de déploiement
    sudo mkdir -p /opt/karma-com-solidarite
    sudo chown vpsadmin:vpsadmin /opt/karma-com-solidarite
    
    # Créer les répertoires nécessaires
    mkdir -p /opt/karma-com-solidarite/{backups,logs,ssl}
    
    echo "Répertoires créés sur le VPS"
EOF

success "VPS préparé"

# Copie des fichiers nécessaires
log "Copie des fichiers vers le VPS..."

# Créer un archive temporaire avec seulement les fichiers nécessaires
TEMP_DIR=$(mktemp -d)
ARCHIVE_NAME="karma-com-deploy.tar.gz"

log "Création de l'archive de déploiement..."

# Copier les fichiers essentiels
cp -r src $TEMP_DIR/
cp -r prisma $TEMP_DIR/
cp -r public $TEMP_DIR/ 2>/dev/null || mkdir -p $TEMP_DIR/public
cp package.json $TEMP_DIR/
cp package-lock.json $TEMP_DIR/ 2>/dev/null || true
cp next.config.js $TEMP_DIR/
cp tailwind.config.js $TEMP_DIR/
cp postcss.config.js $TEMP_DIR/
cp tsconfig.json $TEMP_DIR/
cp Dockerfile.simple $TEMP_DIR/
cp docker-compose.production.yml $TEMP_DCréation de l'archive deIR/ 2>/dev/null || cp docker-compose.yml $TEMP_DIR/docker-compose.production.yml
cp -r nginx $TEMP_DIR/ 2>/dev/null || mkdir -p $TEMP_DIR/nginx

# Créer l'archive
cd $TEMP_DIR
tar -czf $ARCHIVE_NAME .
cd - > /dev/null

# Copier l'archive vers le VPS
scp $TEMP_DIR/$ARCHIVE_NAME $VPS_USER@$VPS_IP:/kcs/

# Nettoyer le répertoire temporaire
rm -rf $TEMP_DIR

success "Fichiers copiés vers le VPS"

# Déploiement sur le VPS
log "Déploiement de l'application sur le VPS..."

ssh $VPS_USER@$VPS_IP << EOF
    set -e
    
    echo "📦 Extraction de l'archive..."
    cd /kcs
    tar -xzf /kcs/karma-com-deploy.tar.gz
    
    
    echo "🔧 Configuration de l'environnement..."
    
    # Créer le fichier .env.production
    cat > .env.production << 'ENVEOF'
NODE_ENV=production
DATABASE_URL=************************************************/$DB_NAME
NEXTAUTH_SECRET=karma_com_production_secret_key_very_secure_2024
NEXTAUTH_URL=https://$DOMAIN
ENVEOF
    
    echo "🐳 Construction de l'image Docker..."
   # docker build -f Dockerfile.simple -t karma-com-app:latest .
    
    echo "📋 Configuration Docker Compose..."
    
    # Créer le docker-compose.yml pour production
    cat > docker-compose.yml << 'COMPOSEEOF'
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: karma-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: $DB_NAME
      POSTGRES_USER: $DB_USER
      POSTGRES_PASSWORD: $DB_PASSWORD
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "127.0.0.1:5432:5432"
    networks:
      - karma-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $DB_USER -d $DB_NAME"]
      interval: 30s
      timeout: 10s
      retries: 3

  app:
    image: karma-com-app:latest
    container_name: karma-app
    restart: unless-stopped
    ports:
      - "127.0.0.1:3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=************************************************/$DB_NAME
      - NEXTAUTH_SECRET=karma_com_production_secret_key_very_secure_2024
      - NEXTAUTH_URL=https://$DOMAIN
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - karma-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  karma-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
COMPOSEEOF
    
    echo "🚀 Démarrage des services..."
    docker-compose down 2>/dev/null || true
    docker-compose up -d
    
    echo "⏳ Attente du démarrage des services..."
    sleep 30
    
    echo "✅ Services démarrés"
EOF

success "Application déployée sur le VPS"

# Configuration Nginx
log "Configuration de Nginx pour le domaine $DOMAIN..."

ssh $VPS_USER@$VPS_IP << EOF
    set -e
    
    echo "🌐 Configuration Nginx..."
    
    # Créer la configuration Nginx
    sudo tee /etc/nginx/sites-available/$DOMAIN > /dev/null << 'NGINXEOF'
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    # Redirection vers HTTPS (sera configuré avec Certbot)
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # Configuration SSL (sera ajoutée par Certbot)
    
    # Headers de sécurité
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Logs
    access_log /var/log/nginx/karma-com-access.log;
    error_log /var/log/nginx/karma-com-error.log;
    
    # Taille maximale des uploads
    client_max_body_size 10M;
    
    # Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Proxy vers l'application
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health check
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host \$host;
    }
    
    # Assets statiques avec cache
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host \$host;
    }
}
NGINXEOF
    
    # Activer le site
    sudo ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/$DOMAIN
    
    # Supprimer le site par défaut s'il existe
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Tester la configuration
    sudo nginx -t
    
    # Redémarrer Nginx
    sudo systemctl restart nginx
    
    echo "✅ Nginx configuré"
EOF

success "Nginx configuré"

# Configuration SSL avec Certbot
log "Configuration SSL avec Let's Encrypt..."

ssh $VPS_USER@$VPS_IP << EOF
    set -e
    
    echo "🔒 Configuration SSL..."
    
    # Obtenir le certificat SSL
    sudo certbot --nginx --agree-tos --redirect --hsts --staple-ocsp --email <EMAIL> -d $DOMAIN -d www.$DOMAIN --non-interactive
    
    echo "✅ SSL configuré"
EOF

success "SSL configuré"

# Vérification finale
log "Vérification du déploiement..."

ssh $VPS_USER@$VPS_IP << 'EOF'
    echo "🔍 Vérification des services..."
    
    # Vérifier Docker
    docker-compose ps
    
    # Vérifier l'application
    sleep 10
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Application accessible localement"
    else
        echo "❌ Application non accessible"
    fi
    
    # Vérifier Nginx
    if sudo nginx -t; then
        echo "✅ Configuration Nginx valide"
    else
        echo "❌ Erreur configuration Nginx"
    fi
EOF

# Test final depuis l'extérieur
log "Test final de l'application..."
sleep 30

if curl -f https://$DOMAIN > /dev/null 2>&1; then
    success "Application accessible sur https://$DOMAIN"
else
    warning "Application pas encore accessible depuis l'extérieur (DNS en cours de propagation?)"
fi

# Résumé final
echo ""
echo "🎉 Déploiement terminé !"
echo "========================"
echo ""
echo "📊 Informations de déploiement :"
echo "   🌐 URL : https://$DOMAIN"
echo "   🖥️  VPS : $VPS_USER@$VPS_IP"
echo "   📁 Répertoire : $DEPLOY_DIR"
echo ""
echo "🔧 Commandes utiles sur le VPS :"
echo "   📋 Voir les logs : docker-compose logs -f"
echo "   🔄 Redémarrer : docker-compose restart"
echo "   🛑 Arrêter : docker-compose down"
echo "   📊 État : docker-compose ps"
echo ""
echo "🗄️ Base de données :"
echo "   🔗 Host : localhost (depuis le VPS)"
echo "   🔗 Port : 5432"
echo "   🔗 Database : $DB_NAME"
echo "   🔗 User : $DB_USER"
echo ""
echo "📚 Logs importants :"
echo "   📄 Nginx : /var/log/nginx/karma-com-*.log"
echo "   📄 Application : docker-compose logs app"
echo "   📄 Base : docker-compose logs postgres"

success "Déploiement Karma Com Solidarité terminé avec succès !"
