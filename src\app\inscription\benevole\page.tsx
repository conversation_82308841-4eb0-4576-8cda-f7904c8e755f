'use client'

import React, { useState } from 'react'
import { ChevronLeft, ChevronRight, Upload, X } from 'lucide-react'

// Types pour les données du formulaire
interface VolunteerFormData {
  // Écran 2
  firstName: string
  lastName: string
  email: string
  birthDate: string
  birthPlace: string
  
  // Écran 3
  personalEmail: string
  phoneNumber: string
  address: string
  
  // Écran 4
  currentStatus: string
  otherStatus: string
  contributionPole: string
  otherPole: string
  
  // Écran 5
  specificSkills: string
  otherSkills: string
  
  // Écran 6
  associationExperience: string
  motivationReason: string
  objectives: string
  weeklyHours: string
  otherWeeklyHours: string
  availability: string
  otherAvailability: string
  participationRhythm: string
  otherRhythm: string
  cvFiles: File[]
  onlineProfile: string
  
  // Écran 7
  howDidYouKnowUs: string
  otherSource: string
}

const initialFormData: VolunteerFormData = {
  firstName: '',
  lastName: '',
  email: '',
  birthDate: '',
  birthPlace: '',
  personalEmail: '',
  phoneNumber: '',
  address: '',
  currentStatus: '',
  otherStatus: '',
  contributionPole: '',
  otherPole: '',
  specificSkills: '',
  otherSkills: '',
  associationExperience: '',
  motivationReason: '',
  objectives: '',
  weeklyHours: '',
  otherWeeklyHours: '',
  availability: '',
  otherAvailability: '',
  participationRhythm: '',
  otherRhythm: '',
  cvFiles: [],
  onlineProfile: '',
  howDidYouKnowUs: '',
  otherSource: ''
}

// Options pour les listes déroulantes
const statusOptions = [
  { value: 'En poste', label: 'En poste' },
  { value: 'En études/formations', label: 'En études/formations' },
  { value: 'En reconversion', label: 'En reconversion' },
  { value: 'En recherche active', label: 'En recherche active' },
  { value: 'Freelance', label: 'Freelance' },
  { value: 'Autre', label: 'Autre' }
]

const poleOptions = [
  { value: 'Communication et Stratégie Digitale', label: 'Communication et Stratégie Digitale' },
  { value: 'IT et Transformation Numérique', label: 'IT et Transformation Numérique' },
  { value: 'IA et Automatisation', label: 'IA et Automatisation' },
  { value: 'Design Graphique et Création Visuelle', label: 'Design Graphique et Création Visuelle' },
  { value: 'Production Audiovisuelle', label: 'Production Audiovisuelle' },
  { value: 'Recherche et Éthique', label: 'Recherche et Éthique' },
  { value: 'Partenariats et Mécénat', label: 'Partenariats et Mécénat' },
  { value: 'RSE-RSO (Responsabilité Sociétale des Organisations)', label: 'RSE-RSO (Responsabilité Sociétale des Organisations)' },
  { value: 'Ressources Humaines', label: 'Ressources Humaines' },
  { value: 'Administration et Secrétariat', label: 'Administration et Secrétariat' },
  { value: 'Juridique', label: 'Juridique' },
  { value: 'Comptabilité et Gestion Financière', label: 'Comptabilité et Gestion Financière' },
  { value: 'Autre', label: 'Autre' }
]

const skillsOptions = [
  { value: 'Développement web (front-end, back-end, full-stack)', label: 'Développement web (front-end, back-end, full-stack)' },
  { value: 'Administration de sites web (WordPress, Wix)', label: 'Administration de sites web (WordPress, Wix)' },
  { value: 'Analyse de données', label: 'Analyse de données' },
  { value: 'Automatisation des processus (RPA, Zapier, API)', label: 'Automatisation des processus (RPA, Zapier, API)' },
  { value: 'Sécurité informatique et protection des données', label: 'Sécurité informatique et protection des données' },
  { value: 'Développement d\'applications mobiles (iOS, Android)', label: 'Développement d\'applications mobiles (iOS, Android)' },
  { value: 'Développement et intégration de modèles d\'IA', label: 'Développement et intégration de modèles d\'IA' },
  { value: 'Autre', label: 'Autre' }
]

const weeklyHoursOptions = [
  { value: 'Moins de 5 heures par semaine', label: 'Moins de 5 heures par semaine' },
  { value: 'Entre 5 et 10 heures par semaine', label: 'Entre 5 et 10 heures par semaine' },
  { value: 'Entre 10 et 15 heures par semaine', label: 'Entre 10 et 15 heures par semaine' },
  { value: 'Plus de 15 heures par semaine', label: 'Plus de 15 heures par semaine' },
  { value: 'Autre', label: 'Autre' }
]

const availabilityOptions = [
  { value: 'Matinée (8h à 12h)', label: 'Matinée (8h à 12h)' },
  { value: 'Après-midi (12h à 18h)', label: 'Après-midi (12h à 18h)' },
  { value: 'Soirée (18h à 22h)', label: 'Soirée (18h à 22h)' },
  { value: 'Week-end (samedi et/ou dimanche)', label: 'Week-end (samedi et/ou dimanche)' },
  { value: 'Autre', label: 'Autre' }
]

const rhythmOptions = [
  { value: 'Régulier (chaque semaine)', label: 'Régulier (chaque semaine)' },
  { value: 'Ponctuel (missions spécifiques ou événements)', label: 'Ponctuel (missions spécifiques ou événements)' },
  { value: 'Autre', label: 'Autre' }
]

const sourceOptions = [
  { value: 'Site internet de Karma Com Solidarité', label: 'Site internet de Karma Com Solidarité' },
  { value: 'Plateforme (ex. : Tous Bénévoles, JeVeuxAider.gouv...préciser dans "autre")', label: 'Plateforme (ex. : Tous Bénévoles, JeVeuxAider.gouv...préciser dans "autre")' },
  { value: 'Réseaux sociaux (préciser dans "autre")', label: 'Réseaux sociaux (préciser dans "autre")' },
  { value: 'Recommandation par une connaissance', label: 'Recommandation par une connaissance' },
  { value: 'Autre', label: 'Autre' }
]

export default function VolunteerApplicationPage() {
  const [currentScreen, setCurrentScreen] = useState(1)
  const [formData, setFormData] = useState<VolunteerFormData>(initialFormData)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Navigation entre les écrans
  const nextScreen = () => {
    if (validateCurrentScreen()) {
      setCurrentScreen(prev => Math.min(prev + 1, 8))
    }
  }

  const prevScreen = () => {
    setCurrentScreen(prev => Math.max(prev - 1, 1))
  }

  // Validation des champs requis pour chaque écran
  const validateCurrentScreen = (): boolean => {
    const newErrors: Record<string, string> = {}

    switch (currentScreen) {
      case 2:
        if (!formData.firstName.trim()) newErrors.firstName = 'Le prénom est requis'
        if (!formData.lastName.trim()) newErrors.lastName = 'Le nom est requis'
        if (!formData.email.trim()) newErrors.email = 'L\'email est requis'
        if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
          newErrors.email = 'Format d\'email invalide'
        }
        break
      case 3:
        if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Le numéro de téléphone est requis'
        break
      case 4:
        if (!formData.currentStatus) newErrors.currentStatus = 'Veuillez sélectionner votre situation'
        if (!formData.contributionPole) newErrors.contributionPole = 'Veuillez sélectionner un pôle'
        break
      case 6:
        if (!formData.weeklyHours) newErrors.weeklyHours = 'Veuillez indiquer votre disponibilité horaire'
        if (!formData.availability) newErrors.availability = 'Veuillez indiquer vos créneaux de disponibilité'
        if (!formData.participationRhythm) newErrors.participationRhythm = 'Veuillez indiquer votre rythme de participation'
        break
      case 7:
        if (!formData.howDidYouKnowUs) newErrors.howDidYouKnowUs = 'Veuillez indiquer comment vous nous avez connus'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Mise à jour des données du formulaire
  const updateFormData = (field: keyof VolunteerFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Supprimer l'erreur si le champ est maintenant valide
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Gestion des fichiers
  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const newFiles = Array.from(files).slice(0, 5) // Max 5 fichiers
      updateFormData('cvFiles', [...formData.cvFiles, ...newFiles].slice(0, 5))
    }
  }

  const removeFile = (index: number) => {
    const newFiles = formData.cvFiles.filter((_, i) => i !== index)
    updateFormData('cvFiles', newFiles)
  }

  // Soumission du formulaire
  const handleSubmit = async () => {
    if (!validateCurrentScreen()) return

    setIsSubmitting(true)
    try {
      // Préparer les données pour l'envoi
      const submitData = {
        ...formData,
        cvFiles: formData.cvFiles.map(file => ({
          name: file.name,
          size: file.size,
          type: file.type
        }))
      }

      const response = await fetch('/api/volunteer-application', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (response.ok) {
        setCurrentScreen(8) // Aller à l'écran de remerciement
      } else {
        throw new Error('Erreur lors de la soumission')
      }
    } catch (error) {
      console.error('Erreur:', error)
      alert('Une erreur est survenue lors de la soumission. Veuillez réessayer.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-pink-50">
      <div className="container-karma py-8">
        {/* En-tête avec progression */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-8">
            {/* Barre de progression */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-600">
                  Étape {currentScreen} sur 8
                </span>
                <span className="text-sm text-gray-500">
                  {Math.round((currentScreen / 8) * 100)}% complété
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-karma-blue to-karma-pink h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(currentScreen / 8) * 100}%` }}
                />
              </div>
            </div>

            {/* Contenu des écrans */}
            <div className="min-h-[500px]">
              {/* Écran 1 - Bienvenue */}
              {currentScreen === 1 && (
                <div className="text-center py-12">
                  <h1 className="text-3xl font-bold text-gray-900 mb-6">
                    Candidature Bénévolat KCS
                  </h1>
                  <div className="max-w-2xl mx-auto space-y-4 text-gray-600 leading-relaxed">
                    <p>
                      Bienvenue chez <strong>Karma Com Solidarité (KCS)</strong> et merci pour votre intérêt à rejoindre notre association et équipe de bénévoles !
                    </p>
                    <p>
                      Votre engagement est essentiel pour renforcer nos capacités et atteindre nos objectifs. Ce formulaire nous permet de mieux comprendre vos compétences et disponibilités pour vous proposer une mission adaptée.
                    </p>
                    <p>
                      Une fois le formulaire complété et validé, vous recevrez un lien pour choisir un créneau de RDV découverte. Merci pour votre implication et au plaisir de collaborer avec vous !
                    </p>
                  </div>
                </div>
              )}

              {/* Écran 2 - Informations personnelles de base */}
              {currentScreen === 2 && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    Informations personnelles
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="karma-label">Prénom *</label>
                      <input
                        type="text"
                        value={formData.firstName}
                        onChange={(e) => updateFormData('firstName', e.target.value)}
                        className={`karma-input ${errors.firstName ? 'border-red-500' : ''}`}
                        placeholder="Votre prénom"
                      />
                      {errors.firstName && (
                        <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
                      )}
                    </div>

                    <div>
                      <label className="karma-label">Nom *</label>
                      <input
                        type="text"
                        value={formData.lastName}
                        onChange={(e) => updateFormData('lastName', e.target.value)}
                        className={`karma-input ${errors.lastName ? 'border-red-500' : ''}`}
                        placeholder="Votre nom"
                      />
                      {errors.lastName && (
                        <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
                      )}
                    </div>

                    <div>
                      <label className="karma-label">Email *</label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => updateFormData('email', e.target.value)}
                        className={`karma-input ${errors.email ? 'border-red-500' : ''}`}
                        placeholder="<EMAIL>"
                      />
                      {errors.email && (
                        <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                      )}
                    </div>

                    <div>
                      <label className="karma-label">Date de naissance</label>
                      <input
                        type="date"
                        value={formData.birthDate}
                        onChange={(e) => updateFormData('birthDate', e.target.value)}
                        className="karma-input"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="karma-label">Lieu de naissance</label>
                      <input
                        type="text"
                        value={formData.birthPlace}
                        onChange={(e) => updateFormData('birthPlace', e.target.value)}
                        className="karma-input"
                        placeholder="Ville, Pays"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Écran 3 - Coordonnées */}
              {currentScreen === 3 && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    Coordonnées
                  </h2>

                  <div className="space-y-6">
                    <div>
                      <label className="karma-label">Email personnel</label>
                      <input
                        type="email"
                        value={formData.personalEmail}
                        onChange={(e) => updateFormData('personalEmail', e.target.value)}
                        className="karma-input"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="karma-label">Numéro de téléphone *</label>
                      <input
                        type="tel"
                        value={formData.phoneNumber}
                        onChange={(e) => updateFormData('phoneNumber', e.target.value)}
                        className={`karma-input ${errors.phoneNumber ? 'border-red-500' : ''}`}
                        placeholder="06 12 34 56 78"
                      />
                      {errors.phoneNumber && (
                        <p className="text-red-500 text-sm mt-1">{errors.phoneNumber}</p>
                      )}
                    </div>

                    <div>
                      <label className="karma-label">Adresse postale</label>
                      <textarea
                        value={formData.address}
                        onChange={(e) => updateFormData('address', e.target.value)}
                        className="karma-input"
                        rows={3}
                        placeholder="Votre adresse complète"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Écran 4 - Situation professionnelle et pôle de contribution */}
              {currentScreen === 4 && (
                <div className="space-y-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    Situation professionnelle et pôle de contribution
                  </h2>

                  {/* Section 1: Situation actuelle */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-800">
                      Actuellement, vous êtes : *
                    </h3>
                    <select
                      value={formData.currentStatus}
                      onChange={(e) => updateFormData('currentStatus', e.target.value)}
                      className={`karma-input ${errors.currentStatus ? 'border-red-500' : ''}`}
                    >
                      <option value="">Sélectionnez votre situation</option>
                      {statusOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.currentStatus && (
                      <p className="text-red-500 text-sm mt-1">{errors.currentStatus}</p>
                    )}

                    {formData.currentStatus === 'Autre' && (
                      <div className="mt-4">
                        <label className="karma-label">Précisez :</label>
                        <input
                          type="text"
                          value={formData.otherStatus}
                          onChange={(e) => updateFormData('otherStatus', e.target.value)}
                          className="karma-input"
                          placeholder="Précisez votre situation"
                        />
                      </div>
                    )}
                  </div>

                  {/* Section 2: Pôle de contribution */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-800">
                      Dans quel pôle souhaitez-vous contribuer ? *
                    </h3>
                    <select
                      value={formData.contributionPole}
                      onChange={(e) => updateFormData('contributionPole', e.target.value)}
                      className={`karma-input ${errors.contributionPole ? 'border-red-500' : ''}`}
                    >
                      <option value="">Sélectionnez un pôle</option>
                      {poleOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.contributionPole && (
                      <p className="text-red-500 text-sm mt-1">{errors.contributionPole}</p>
                    )}

                    {formData.contributionPole === 'Autre' && (
                      <div className="mt-4">
                        <label className="karma-label">Précisez :</label>
                        <input
                          type="text"
                          value={formData.otherPole}
                          onChange={(e) => updateFormData('otherPole', e.target.value)}
                          className="karma-input"
                          placeholder="Précisez le pôle"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Écran 5 - Compétences spécifiques */}
              {currentScreen === 5 && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    Compétences spécifiques
                  </h2>

                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-800">
                      Quelles compétences souhaitez-vous mettre à profit dans le domaine : IT et Transformation Numérique ?
                    </h3>
                    <select
                      value={formData.specificSkills}
                      onChange={(e) => updateFormData('specificSkills', e.target.value)}
                      className="karma-input"
                    >
                      <option value="">Sélectionnez vos compétences</option>
                      {skillsOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>

                    {formData.specificSkills === 'Autre' && (
                      <div className="mt-4">
                        <label className="karma-label">Précisez :</label>
                        <textarea
                          value={formData.otherSkills}
                          onChange={(e) => updateFormData('otherSkills', e.target.value)}
                          className="karma-input"
                          rows={3}
                          placeholder="Décrivez vos autres compétences"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Écran 6 - Expérience, motivations et disponibilités */}
              {currentScreen === 6 && (
                <div className="space-y-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    Expérience, motivations et disponibilités
                  </h2>

                  {/* Questions texte */}
                  <div className="space-y-6">
                    <div>
                      <label className="karma-label">
                        6.1 Avez-vous une expérience dans une association ou un domaine pertinent ?
                      </label>
                      <textarea
                        value={formData.associationExperience}
                        onChange={(e) => updateFormData('associationExperience', e.target.value)}
                        className="karma-input"
                        rows={4}
                        placeholder="Décrivez votre expérience..."
                      />
                    </div>

                    <div>
                      <label className="karma-label">
                        6.2 Pourquoi souhaitez-vous contribuer à Karma Com Solidarité ?
                      </label>
                      <textarea
                        value={formData.motivationReason}
                        onChange={(e) => updateFormData('motivationReason', e.target.value)}
                        className="karma-input"
                        rows={4}
                        placeholder="Expliquez vos motivations..."
                      />
                    </div>

                    <div>
                      <label className="karma-label">
                        6.3 Quels sont vos objectifs en rejoignant notre association ?
                      </label>
                      <textarea
                        value={formData.objectives}
                        onChange={(e) => updateFormData('objectives', e.target.value)}
                        className="karma-input"
                        rows={4}
                        placeholder="Décrivez vos objectifs..."
                      />
                    </div>
                  </div>

                  {/* Disponibilités */}
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-800">
                      6.4 Quel est le volume horaire hebdomadaire que vous pourriez consacrer à nos missions ?
                    </h3>

                    {/* 6.4.1 Volume horaire */}
                    <div>
                      <label className="karma-label">6.4.1 Volume horaire *</label>
                      <select
                        value={formData.weeklyHours}
                        onChange={(e) => updateFormData('weeklyHours', e.target.value)}
                        className={`karma-input ${errors.weeklyHours ? 'border-red-500' : ''}`}
                      >
                        <option value="">Sélectionnez votre disponibilité</option>
                        {weeklyHoursOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                      {errors.weeklyHours && (
                        <p className="text-red-500 text-sm mt-1">{errors.weeklyHours}</p>
                      )}

                      {formData.weeklyHours === 'Autre' && (
                        <div className="mt-4">
                          <label className="karma-label">Précisez :</label>
                          <input
                            type="text"
                            value={formData.otherWeeklyHours}
                            onChange={(e) => updateFormData('otherWeeklyHours', e.target.value)}
                            className="karma-input"
                            placeholder="Précisez votre disponibilité horaire"
                          />
                        </div>
                      )}
                    </div>

                    {/* 6.4.2 Créneaux de disponibilité */}
                    <div>
                      <label className="karma-label">6.4.2 Vos disponibilités horaires selon les jours *</label>
                      <select
                        value={formData.availability}
                        onChange={(e) => updateFormData('availability', e.target.value)}
                        className={`karma-input ${errors.availability ? 'border-red-500' : ''}`}
                      >
                        <option value="">Sélectionnez vos créneaux</option>
                        {availabilityOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                      {errors.availability && (
                        <p className="text-red-500 text-sm mt-1">{errors.availability}</p>
                      )}

                      {formData.availability === 'Autre' && (
                        <div className="mt-4">
                          <label className="karma-label">Précisez :</label>
                          <input
                            type="text"
                            value={formData.otherAvailability}
                            onChange={(e) => updateFormData('otherAvailability', e.target.value)}
                            className="karma-input"
                            placeholder="Précisez vos créneaux de disponibilité"
                          />
                        </div>
                      )}
                    </div>

                    {/* 6.4.3 Rythme de participation */}
                    <div>
                      <label className="karma-label">6.4.3 Trouver le rythme de participation *</label>
                      <select
                        value={formData.participationRhythm}
                        onChange={(e) => updateFormData('participationRhythm', e.target.value)}
                        className={`karma-input ${errors.participationRhythm ? 'border-red-500' : ''}`}
                      >
                        <option value="">Sélectionnez votre rythme</option>
                        {rhythmOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                      {errors.participationRhythm && (
                        <p className="text-red-500 text-sm mt-1">{errors.participationRhythm}</p>
                      )}

                      {formData.participationRhythm === 'Autre' && (
                        <div className="mt-4">
                          <label className="karma-label">Précisez :</label>
                          <input
                            type="text"
                            value={formData.otherRhythm}
                            onChange={(e) => updateFormData('otherRhythm', e.target.value)}
                            className="karma-input"
                            placeholder="Précisez votre rythme de participation"
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Upload de fichiers et profil en ligne */}
                  <div className="space-y-6">
                    {/* 6.4.4 CV et documents */}
                    <div>
                      <label className="karma-label">6.4.4 Votre CV</label>
                      <p className="text-sm text-gray-600 mb-3">
                        Vous pouvez joindre votre CV et autres documents comme un portfolio (5 max)
                        <br />
                        Importez jusqu'à 5 fichiers compatibles. 100 MB max. par fichier.
                      </p>

                      <input
                        type="file"
                        multiple
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        onChange={(e) => handleFileUpload(e.target.files)}
                        className="karma-input"
                        disabled={formData.cvFiles.length >= 5}
                      />

                      {/* Liste des fichiers uploadés */}
                      {formData.cvFiles.length > 0 && (
                        <div className="mt-4 space-y-2">
                          <p className="text-sm font-medium text-gray-700">
                            Fichiers sélectionnés ({formData.cvFiles.length}/5) :
                          </p>
                          {formData.cvFiles.map((file, index) => (
                            <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                              <div className="flex items-center space-x-3">
                                <Upload className="h-4 w-4 text-gray-500" />
                                <span className="text-sm text-gray-700">{file.name}</span>
                                <span className="text-xs text-gray-500">
                                  ({(file.size / 1024 / 1024).toFixed(2)} MB)
                                </span>
                              </div>
                              <button
                                type="button"
                                onClick={() => removeFile(index)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* 6.4.5 Profil en ligne */}
                    <div>
                      <label className="karma-label">6.4.5 Votre Profil en ligne</label>
                      <p className="text-sm text-gray-600 mb-3">
                        Vous pouvez joindre votre CV en lien et/ou profil LinkedIn ou un lien vers votre site
                      </p>
                      <input
                        type="url"
                        value={formData.onlineProfile}
                        onChange={(e) => updateFormData('onlineProfile', e.target.value)}
                        className="karma-input"
                        placeholder="https://linkedin.com/in/votre-profil ou https://votre-site.com"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Écran 7 - Comment nous avez-vous connus */}
              {currentScreen === 7 && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    Comment nous avez-vous connus ?
                  </h2>

                  <div className="space-y-4">
                    <select
                      value={formData.howDidYouKnowUs}
                      onChange={(e) => updateFormData('howDidYouKnowUs', e.target.value)}
                      className={`karma-input ${errors.howDidYouKnowUs ? 'border-red-500' : ''}`}
                    >
                      <option value="">Sélectionnez une option</option>
                      {sourceOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.howDidYouKnowUs && (
                      <p className="text-red-500 text-sm mt-1">{errors.howDidYouKnowUs}</p>
                    )}

                    {formData.howDidYouKnowUs === 'Autre' && (
                      <div className="mt-4">
                        <label className="karma-label">Précisez :</label>
                        <textarea
                          value={formData.otherSource}
                          onChange={(e) => updateFormData('otherSource', e.target.value)}
                          className="karma-input"
                          rows={3}
                          placeholder="Précisez comment vous nous avez connus"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Écran 8 - Message de remerciement final */}
              {currentScreen === 8 && (
                <div className="text-center py-12">
                  <div className="max-w-2xl mx-auto">
                    <div className="mb-8">
                      <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6">
                        <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <h1 className="text-3xl font-bold text-gray-900 mb-6">
                        Merci pour votre candidature !
                      </h1>
                    </div>

                    <div className="space-y-4 text-gray-600 leading-relaxed">
                      <p>
                        Merci d'avoir pris le temps de compléter ce formulaire !
                      </p>
                      <p>
                        Vos réponses nous permettront de mieux comprendre vos compétences et disponibilités. Une fois validée, vous recevrez un lien pour réserver votre RDV découverte avec notre équipe.
                      </p>
                      <p className="font-semibold text-karma-blue">
                        À bientôt chez Karma Com Solidarité !
                      </p>
                    </div>

                    <div className="mt-8">
                      <button
                        onClick={() => window.location.href = '/'}
                        className="karma-button-primary"
                      >
                        Retour à l'accueil
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Boutons de navigation */}
            <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
              <button
                onClick={prevScreen}
                disabled={currentScreen === 1}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                  currentScreen === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <ChevronLeft className="h-5 w-5" />
                <span>Retour</span>
              </button>

              {currentScreen < 7 ? (
                <button
                  onClick={nextScreen}
                  className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-karma-blue to-karma-pink text-white rounded-lg font-medium hover:shadow-lg transition-all duration-200"
                >
                  <span>Suivant</span>
                  <ChevronRight className="h-5 w-5" />
                </button>
              ) : currentScreen === 7 ? (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50"
                >
                  {isSubmitting ? 'Envoi en cours...' : 'Envoyer ma candidature'}
                </button>
              ) : (
                <div></div> // Écran 8 - pas de bouton
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
