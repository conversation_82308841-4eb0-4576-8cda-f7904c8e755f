// Test rapide pour vérifier que tout fonctionne
const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function quickTest() {
  console.log('⚡ Test Rapide - Karma Com Dashboard')
  console.log('===================================')
  
  let allGood = true
  
  // Test 1: Application accessible
  try {
    console.log('🌐 Test: Application accessible...')
    const response = await fetch(BASE_URL)
    if (response.ok) {
      console.log('✅ Application accessible')
    } else {
      console.log(`❌ Application inaccessible: HTTP ${response.status}`)
      allGood = false
    }
  } catch (error) {
    console.log('❌ Application non accessible:', error.message)
    console.log('💡 Lancez: npm run dev')
    allGood = false
  }
  
  // Test 2: API de santé
  try {
    console.log('🏥 Test: API de santé...')
    const response = await fetch(`${BASE_URL}/api/health`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ API de santé OK')
      console.log(`   Base de données: ${data.database.connected ? '✅ Connectée' : '❌ Déconnectée'}`)
      console.log(`   Utilisateurs: ${data.database.userCount || 0}`)
    } else {
      console.log(`❌ API de santé erreur: HTTP ${response.status}`)
      allGood = false
    }
  } catch (error) {
    console.log('❌ API de santé inaccessible:', error.message)
    allGood = false
  }
  
  // Test 3: API Dashboard
  try {
    console.log('📊 Test: API Dashboard...')
    const response = await fetch(`${BASE_URL}/api/dashboard/stats`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ API Dashboard OK')
      console.log(`   Total candidats: ${data.overview?.totalCandidates || 0}`)
    } else {
      const errorData = await response.json().catch(() => ({}))
      console.log(`❌ API Dashboard erreur: HTTP ${response.status}`)
      if (errorData.details) {
        console.log(`   Détails: ${errorData.details.message}`)
      }
      allGood = false
    }
  } catch (error) {
    console.log('❌ API Dashboard inaccessible:', error.message)
    allGood = false
  }
  
  // Test 4: Page Dashboard
  try {
    console.log('🖥️ Test: Page Dashboard...')
    const response = await fetch(`${BASE_URL}/dashboard`)
    
    if (response.ok) {
      console.log('✅ Page Dashboard accessible')
    } else {
      console.log(`❌ Page Dashboard erreur: HTTP ${response.status}`)
      allGood = false
    }
  } catch (error) {
    console.log('❌ Page Dashboard inaccessible:', error.message)
    allGood = false
  }
  
  // Résumé
  console.log('\n📋 Résumé du Test Rapide')
  console.log('========================')
  
  if (allGood) {
    console.log('🎉 Tous les tests sont passés!')
    console.log('\n🚀 Vous pouvez utiliser l\'application:')
    console.log('   • Application: http://localhost:3000')
    console.log('   • Dashboard: http://localhost:3000/dashboard')
    console.log('   • Connexion: <EMAIL> / admin123')
  } else {
    console.log('⚠️ Certains tests ont échoué')
    console.log('\n🔧 Solutions:')
    console.log('   • Diagnostic complet: npm run diagnose')
    console.log('   • Réparation auto: npm run fix:dashboard')
    console.log('   • Guide détaillé: voir DEPANNAGE.md')
  }
}

// Vérifier si node-fetch est disponible
try {
  require('node-fetch')
} catch (error) {
  console.log('❌ node-fetch n\'est pas installé')
  console.log('💡 Installez-le avec: npm install')
  process.exit(1)
}

quickTest().catch(console.error)
