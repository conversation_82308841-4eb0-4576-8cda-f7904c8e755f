# 🚨 Guide de Dépannage - Karma Com Dashboard

## Erreur 500 (Internal Server Error) sur le Dashboard

### 🔍 Diagnostic Rapide

```bash
# 1. Diagnostiquer le problème
npm run diagnose

# 2. Tester l'API de santé
curl http://localhost:3000/api/health

# 3. Réparation automatique
npm run fix:dashboard
```

### 🛠️ Solutions par Type d'Erreur

#### Erreur Prisma / Base de Données

**Symptômes :**
- Erreur 500 sur `/api/dashboard/stats`
- Messages d'erreur contenant "Prisma" ou "database"

**Solutions :**
```bash
# Régénérer le client Prisma
npx prisma generate

# Appliquer les migrations
npx prisma migrate dev

# Réinitialiser complètement
npm run db:reset
```

#### PostgreSQL Non Accessible

**Symptômes :**
- "Can't reach database server"
- "Connection refused"

**Solutions :**
```bash
# Démarrer PostgreSQL
docker-compose up -d postgres

# Vérifier le statut
docker ps | grep postgres

# Vérifier les logs
docker-compose logs postgres
```

#### Variables d'Environnement

**Symptômes :**
- "Environment variable not found"
- "DATABASE_URL"

**Solutions :**
```bash
# Vérifier le fichier .env
cat .env

# Recréer depuis l'exemple
cp .env.example .env
```

#### Dépendances Manquantes

**Symptômes :**
- "Module not found"
- "@prisma/client"

**Solutions :**
```bash
# Nettoyer et réinstaller
rm -rf node_modules package-lock.json
npm install
npx prisma generate
```

### 🔧 Commandes de Réparation

#### Réparation Complète (Recommandée)
```bash
npm run fix:dashboard
```

#### Réparation Étape par Étape
```bash
# 1. Nettoyer les dépendances
rm -rf node_modules package-lock.json
npm install

# 2. Régénérer Prisma
npx prisma generate

# 3. Démarrer la base
docker-compose up -d postgres

# 4. Appliquer les migrations
npx prisma migrate dev

# 5. Initialiser les données
npm run db:init
```

### 🧪 Tests de Validation

```bash
# Tester la connexion base de données
npm run diagnose

# Tester l'API de santé
curl http://localhost:3000/api/health

# Tester le dashboard complet
npm run test:dashboard

# Tester l'authentification
npm run test:auth
```

### 📋 Checklist de Vérification

- [ ] **PostgreSQL démarré** : `docker ps | grep postgres`
- [ ] **Variables d'environnement** : `cat .env`
- [ ] **Client Prisma généré** : `ls node_modules/@prisma/client`
- [ ] **Migrations appliquées** : `npx prisma migrate status`
- [ ] **Données présentes** : `npm run diagnose`
- [ ] **Application démarrée** : `npm run dev`

### 🆘 Cas Désespérés

Si rien ne fonctionne, réinitialisation complète :

```bash
# 1. Arrêter tout
docker-compose down
pkill -f "npm run dev"

# 2. Nettoyer complètement
rm -rf node_modules package-lock.json
docker volume prune -f

# 3. Recommencer depuis le début
npm install
docker-compose up -d postgres pgadmin
npx prisma generate
npx prisma migrate dev --name "fresh-start"
npm run db:init
npm run dev
```

### 📞 Support

Si le problème persiste :

1. **Copier les logs d'erreur** complets
2. **Exécuter** `npm run diagnose` et copier le résultat
3. **Vérifier** les logs Docker : `docker-compose logs`
4. **Tester** l'API de santé : `curl http://localhost:3000/api/health`

### 🔗 Liens Utiles

- **Application** : http://localhost:3000
- **Dashboard** : http://localhost:3000/dashboard
- **API Santé** : http://localhost:3000/api/health
- **pgAdmin** : http://localhost:5050
- **Prisma Studio** : `npm run db:studio`
