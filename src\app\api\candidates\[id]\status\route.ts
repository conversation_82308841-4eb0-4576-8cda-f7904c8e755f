import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { MembershipStatus } from '@prisma/client'

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { status } = body

    // Validation des données
    if (!status) {
      return NextResponse.json(
        { error: 'Statut manquant' },
        { status: 400 }
      )
    }

    // Mapper les statuts du frontend vers les valeurs de la base
    const statusMapping: Record<string, MembershipStatus> = {
      'pending': MembershipStatus.PENDING,
      'approved': MembershipStatus.ACTIVE,
      'rejected': MembershipStatus.REJECTED
    }

    const membershipStatus = statusMapping[status]
    if (!membershipStatus) {
      return NextResponse.json(
        { error: 'Statut invalide' },
        { status: 400 }
      )
    }

    // Vérifier que l'utilisateur existe
    const user = await prisma.user.findUnique({
      where: { id },
      include: { profile: true }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'Utilisateur non trouvé' },
        { status: 404 }
      )
    }

    // Mettre à jour le statut dans le profil
    const updatedProfile = await prisma.profile.update({
      where: { userId: id },
      data: { membershipStatus }
    })

    // Récupérer l'utilisateur mis à jour
    const updatedUser = await prisma.user.findUnique({
      where: { id },
      include: { profile: true }
    })

    return NextResponse.json({
      id: updatedUser!.id,
      name: updatedUser!.name,
      email: updatedUser!.email,
      userType: updatedUser!.userType,
      membershipStatus: updatedUser!.profile?.membershipStatus,
      updatedAt: new Date().toISOString()
    })
  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
