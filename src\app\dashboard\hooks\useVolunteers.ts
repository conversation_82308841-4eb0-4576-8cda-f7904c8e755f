import { useState, useCallback } from 'react'
import { RecentApplication } from '../utils/constants'

export const useVolunteers = () => {
  const [volunteers, setVolunteers] = useState<RecentApplication[]>([])
  const [volunteersLoading, setVolunteersLoading] = useState(false)
  const [volunteersPage, setVolunteersPage] = useState(1)
  const [volunteersTotal, setVolunteersTotal] = useState(0)
  const volunteersLimit = 10

  // Charger les bénévoles (nouveau système)
  const loadVolunteers = useCallback(async (page = 1, limit = 10) => {
    try {
      setVolunteersLoading(true)
      console.log('🎯 Chargement des bénévoles...')
      const response = await fetch(`/api/dashboard/volunteers?page=${page}&limit=${limit}`)
      if (response.ok) {
        const data = await response.json()
        setVolunteers(data.volunteers || [])
        setVolunteersTotal(data.pagination?.total || 0)
        console.log('🎯 Bénévoles chargés:', data.volunteers?.length || 0, 'bénévoles')
      } else {
        console.error('❌ Erreur chargement bénévoles:', response.status)
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des bénévoles:', error)
    } finally {
      setVolunteersLoading(false)
    }
  }, [])

  // Mettre à jour le statut d'un bénévole
  const handleUpdateVolunteerStatus = async (volunteerId: string, newStatus: string, onSuccess?: () => void, onError?: (error: string) => void) => {
    try {
      console.log(`🔄 Mise à jour statut bénévole ${volunteerId}: ${newStatus}`)

      const response = await fetch(`/api/dashboard/volunteers?id=${volunteerId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        const updatedVolunteer = await response.json()
        console.log('✅ Statut bénévole mis à jour:', updatedVolunteer)

        // Recharger les bénévoles
        await loadVolunteers(volunteersPage, volunteersLimit)

        if (onSuccess) {
          onSuccess()
        }

      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ Erreur API bénévole:', errorData)
        throw new Error(errorData.error || 'Erreur lors de la mise à jour du statut du bénévole')
      }
    } catch (error) {
      console.error('❌ Erreur mise à jour statut bénévole:', error)
      if (onError) {
        onError(error instanceof Error ? error.message : 'Impossible de mettre à jour le statut du bénévole')
      }
    }
  }

  return {
    volunteers,
    volunteersLoading,
    volunteersPage,
    setVolunteersPage,
    volunteersTotal,
    volunteersLimit,
    loadVolunteers,
    handleUpdateVolunteerStatus
  }
}
