import React from 'react'
import { getStatusBadge, getRegistrationTypeBadge } from '../../utils/statusHelpers'
import { formatDateFR } from '../../utils/dateHelpers'
import { RecentApplication } from '../../utils/constants'

interface RecentActivityProps {
  applications: RecentApplication[]
  loading: boolean
}

const RecentActivity: React.FC<RecentActivityProps> = ({ applications, loading }) => {
  if (loading) {
    return (
      <div className="dashboard-card">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Activité Récente</h2>
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-karma-pink"></div>
          <p className="mt-2 text-gray-600">Chargement...</p>
        </div>
      </div>
    )
  }

  if (applications.length === 0) {
    return (
      <div className="dashboard-card">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Activité Récente</h2>
        <p className="text-gray-500 text-center py-8">Aucune activité récente</p>
      </div>
    )
  }

  return (
    <div className="dashboard-card">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        Activité Récente
        <span className="ml-2 text-sm text-gray-500">({applications.length})</span>
      </h2>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Candidat
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type d'enregistrement
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Organisation
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Statut
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {applications.map((application) => (
              <tr key={application.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-r from-karma-blue to-karma-pink flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {application.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{application.name}</div>
                      <div className="text-sm text-gray-500">{application.email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getRegistrationTypeBadge(application.type)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {application.organization || '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDateFR(application.date)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(application.status)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {applications.length >= 10 && (
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-500">
            Affichage des 10 dernières activités. 
            <span className="text-karma-pink cursor-pointer hover:underline ml-1">
              Voir toutes les candidatures →
            </span>
          </p>
        </div>
      )}
    </div>
  )
}

export default RecentActivity
