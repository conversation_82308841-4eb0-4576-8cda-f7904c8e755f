# Docker Compose pour l'environnement de staging
version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: karma-com-postgres-staging
    restart: unless-stopped
    environment:
      POSTGRES_DB: karma_com_staging
      POSTGRES_USER: karma_user_staging
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD_STAGING}
    volumes:
      - postgres_staging_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    networks:
      - karma-network-staging
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U karma_user_staging -d karma_com_staging"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Application Next.js
  app:
    image: ${REGISTRY_IMAGE:-karma-com-app}:${IMAGE_TAG:-latest}
    container_name: karma-com-app-staging
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=staging
      - DATABASE_URL=postgresql://karma_user_staging:${POSTGRES_PASSWORD_STAGING}@postgres:5432/karma_com_staging
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET_STAGING}
      - NEXTAUTH_URL=https://staging.karma-com-solidarite.fr
      - LOG_LEVEL=debug
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - karma-network-staging
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # pgAdmin pour la gestion de la base de données
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: karma-com-pgadmin-staging
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD_STAGING}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_staging_data:/var/lib/pgadmin
    ports:
      - "5051:80"
    networks:
      - karma-network-staging
    depends_on:
      - postgres

  # Nginx reverse proxy (optionnel)
  nginx:
    image: nginx:alpine
    container_name: karma-com-nginx-staging
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/staging.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - karma-network-staging

networks:
  karma-network-staging:
    driver: bridge

volumes:
  postgres_staging_data:
    driver: local
  pgadmin_staging_data:
    driver: local
