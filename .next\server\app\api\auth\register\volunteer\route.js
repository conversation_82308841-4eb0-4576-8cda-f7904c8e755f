"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register/volunteer/route";
exports.ids = ["app/api/auth/register/volunteer/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute&page=%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute&page=%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_hamza_bedoui_Documents_mesDocs_AI_KCS_augment_kcs_src_app_api_auth_register_volunteer_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/register/volunteer/route.ts */ \"(rsc)/./src/app/api/auth/register/volunteer/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register/volunteer/route\",\n        pathname: \"/api/auth/register/volunteer\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register/volunteer/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\api\\\\auth\\\\register\\\\volunteer\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_hamza_bedoui_Documents_mesDocs_AI_KCS_augment_kcs_src_app_api_auth_register_volunteer_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/register/volunteer/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute&page=%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/register/volunteer/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/auth/register/volunteer/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/validations */ \"(rsc)/./src/lib/validations.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validation des données\n        const validationResult = _lib_validations__WEBPACK_IMPORTED_MODULE_3__.volunteerRegistrationSchema.safeParse(body);\n        if (!validationResult.success) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Donn\\xe9es invalides\",\n                details: validationResult.error.errors\n            }, {\n                status: 400\n            });\n        }\n        const data = validationResult.data;\n        // Vérifier si l'email existe déjà\n        const existingUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                email: data.email\n            }\n        });\n        if (existingUser) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Un compte avec cet email existe d\\xe9j\\xe0\"\n            }, {\n                status: 409\n            });\n        }\n        // Hasher le mot de passe\n        const hashedPassword = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.hashPassword)(data.password);\n        // Préparer les compétences\n        const allSkills = [\n            ...data.skills\n        ];\n        if (data.customSkills) {\n            allSkills.push(data.customSkills);\n        }\n        // Créer l'utilisateur et son profil\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.create({\n            data: {\n                email: data.email,\n                password: hashedPassword,\n                name: `${data.firstName} ${data.lastName}`,\n                phone: data.phone,\n                userType: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserType.VOLUNTEER,\n                profile: {\n                    create: {\n                        firstName: data.firstName,\n                        lastName: data.lastName,\n                        dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : null,\n                        address: data.address,\n                        city: data.city,\n                        postalCode: data.postalCode,\n                        country: data.country,\n                        skills: allSkills,\n                        availability: data.availability,\n                        motivation: data.motivation,\n                        membershipStatus: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.MembershipStatus.PENDING\n                    }\n                }\n            },\n            include: {\n                profile: true\n            }\n        });\n        // Log de l'activité\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.activityLog.create({\n            data: {\n                action: \"USER_REGISTRATION\",\n                description: `Nouvelle inscription bénévole: ${data.firstName} ${data.lastName}`,\n                userId: user.id,\n                metadata: {\n                    userType: \"VOLUNTEER\",\n                    skills: allSkills,\n                    preferredDepartments: data.preferredDepartments\n                }\n            }\n        });\n        // Retourner la réponse (sans le mot de passe)\n        const { password: _, ...userWithoutPassword } = user;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Inscription r\\xe9ussie\",\n            user: userWithoutPassword\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Erreur lors de l'inscription b\\xe9n\\xe9vole:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Erreur interne du serveur\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/register/volunteer/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getTokenFromRequest: () => (/* binding */ getTokenFromRequest),\n/* harmony export */   getUserFromRequest: () => (/* binding */ getUserFromRequest),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.NEXTAUTH_SECRET || \"fallback-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hashedPassword);\n}\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        return null;\n    }\n}\nfunction getTokenFromRequest(request) {\n    const authHeader = request.headers.get(\"authorization\");\n    if (authHeader && authHeader.startsWith(\"Bearer \")) {\n        return authHeader.substring(7);\n    }\n    // Fallback to cookie\n    const token = request.cookies.get(\"auth-token\")?.value;\n    return token || null;\n}\nfunction getUserFromRequest(request) {\n    const token = getTokenFromRequest(request);\n    if (!token) return null;\n    return verifyToken(token);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rYXJtYS1jb20tZGFzaGJvYXJkLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/validations.ts":
/*!********************************!*\
  !*** ./src/lib/validations.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appointmentSchema: () => (/* binding */ appointmentSchema),\n/* harmony export */   associationRegistrationSchema: () => (/* binding */ associationRegistrationSchema),\n/* harmony export */   loginSchema: () => (/* binding */ loginSchema),\n/* harmony export */   organizationRegistrationSchema: () => (/* binding */ organizationRegistrationSchema),\n/* harmony export */   volunteerRegistrationSchema: () => (/* binding */ volunteerRegistrationSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n// Schéma de validation pour l'inscription d'association\nconst associationRegistrationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    // Informations de connexion\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(\"Format d'email invalide\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(8, \"Le mot de passe doit contenir au moins 8 caract\\xe8res\"),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    // Informations personnelles du représentant\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le pr\\xe9nom est requis\"),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le nom est requis\"),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    // Informations de l'association\n    organizationName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le nom de l'association est requis\"),\n    siret: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(/^\\d{14}$/, \"Le SIRET doit contenir 14 chiffres\").optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"\")),\n    website: zod__WEBPACK_IMPORTED_MODULE_0__.string().url(\"URL invalide\").optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"\")),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(10, \"La description doit contenir au moins 10 caract\\xe8res\"),\n    // Adresse\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    postalCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    country: zod__WEBPACK_IMPORTED_MODULE_0__.string().default(\"France\"),\n    // Autres\n    motivation: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    acceptTerms: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, \"Vous devez accepter les conditions\"),\n    acceptRGPD: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, \"Vous devez accepter la politique de confidentialit\\xe9\")\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: \"Les mots de passe ne correspondent pas\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\n// Schéma de validation pour l'inscription d'organisation\nconst organizationRegistrationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    // Informations de connexion\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(\"Format d'email invalide\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(8, \"Le mot de passe doit contenir au moins 8 caract\\xe8res\"),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    // Informations personnelles du représentant\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le pr\\xe9nom est requis\"),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le nom est requis\"),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    // Informations de l'organisation\n    organizationName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le nom de l'organisation est requis\"),\n    organizationType: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le type d'organisation est requis\"),\n    sector: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    siret: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(/^\\d{14}$/, \"Le SIRET doit contenir 14 chiffres\").optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"\")),\n    website: zod__WEBPACK_IMPORTED_MODULE_0__.string().url(\"URL invalide\").optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"\")),\n    employeeCount: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(10, \"La description doit contenir au moins 10 caract\\xe8res\"),\n    // Adresse\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    postalCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    country: zod__WEBPACK_IMPORTED_MODULE_0__.string().default(\"France\"),\n    // Partenariat\n    partnershipType: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    budget: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    motivation: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    // Conditions\n    acceptTerms: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, \"Vous devez accepter les conditions\"),\n    acceptRGPD: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, \"Vous devez accepter la politique de confidentialit\\xe9\")\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: \"Les mots de passe ne correspondent pas\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\n// Schéma de validation pour l'inscription de bénévole\nconst volunteerRegistrationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    // Informations de connexion\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(\"Format d'email invalide\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(8, \"Le mot de passe doit contenir au moins 8 caract\\xe8res\"),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    // Informations personnelles\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le pr\\xe9nom est requis\"),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le nom est requis\"),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    // Adresse\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    postalCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    country: zod__WEBPACK_IMPORTED_MODULE_0__.string().default(\"France\"),\n    // Compétences et disponibilités\n    skills: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string()).default([]),\n    customSkills: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    availability: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    experience: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    motivation: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(10, \"La motivation doit contenir au moins 10 caract\\xe8res\"),\n    // Préférences\n    preferredDepartments: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string()).default([]),\n    remoteWork: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    // Conditions\n    acceptTerms: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, \"Vous devez accepter les conditions\"),\n    acceptRGPD: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, \"Vous devez accepter la politique de confidentialit\\xe9\")\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: \"Les mots de passe ne correspondent pas\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\n// Schéma de validation pour la connexion\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(\"Format d'email invalide\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le mot de passe est requis\")\n});\n// Schéma de validation pour la création de rendez-vous\nconst appointmentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le titre est requis\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    scheduledAt: zod__WEBPACK_IMPORTED_MODULE_0__.string().datetime(\"Date et heure invalides\"),\n    duration: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(15).max(480).default(60),\n    type: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        \"DISCOVERY\",\n        \"INTEGRATION\",\n        \"FOLLOW_UP\",\n        \"INTERVIEW\"\n    ]),\n    candidateId: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Le candidat est requis\"),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/validations.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/zod","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute&page=%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Fvolunteer%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();