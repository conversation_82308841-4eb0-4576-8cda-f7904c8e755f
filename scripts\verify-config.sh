#!/bin/bash

# Script de vérification de la configuration
echo "🔍 Vérification Configuration - Karma Com Solidarité"
echo "=================================================="

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification du fichier docker-compose.yml
print_status "Vérification du fichier docker-compose.yml..."

if [ ! -f "docker-compose.yml" ]; then
    print_error "Fichier docker-compose.yml non trouvé"
    exit 1
fi

# Extraire les paramètres de la base de données
DB_NAME=$(grep "POSTGRES_DB:" docker-compose.yml | awk '{print $2}')
DB_USER=$(grep "POSTGRES_USER:" docker-compose.yml | awk '{print $2}')
DB_PASSWORD=$(grep "POSTGRES_PASSWORD:" docker-compose.yml | awk '{print $2}')

print_success "Paramètres extraits du docker-compose.yml:"
echo "   - Database: $DB_NAME"
echo "   - User: $DB_USER"
echo "   - Password: $DB_PASSWORD"

# Vérification du script de déploiement
print_status "Vérification du script deploy-to-vps.sh..."

if [ ! -f "deploy-to-vps.sh" ]; then
    print_error "Script deploy-to-vps.sh non trouvé"
    exit 1
fi

# Vérifier que les paramètres correspondent
if grep -q "karma_com_db" deploy-to-vps.sh; then
    print_success "Database name correspond dans deploy-to-vps.sh"
else
    print_error "Database name ne correspond pas dans deploy-to-vps.sh"
fi

if grep -q "karma_user" deploy-to-vps.sh; then
    print_success "Database user correspond dans deploy-to-vps.sh"
else
    print_error "Database user ne correspond pas dans deploy-to-vps.sh"
fi

if grep -q "karma_password_2024" deploy-to-vps.sh; then
    print_success "Database password correspond dans deploy-to-vps.sh"
else
    print_error "Database password ne correspond pas dans deploy-to-vps.sh"
fi

# Vérification des ports
print_status "Vérification des ports..."

POSTGRES_PORT=$(grep -A 2 "ports:" docker-compose.yml | grep "5432" | head -1 | awk -F: '{print $2}' | awk -F'"' '{print $1}')
APP_PORT=$(grep -A 2 "ports:" docker-compose.yml | grep "3000" | head -1 | awk -F: '{print $2}' | awk -F'"' '{print $1}')
PGADMIN_PORT=$(grep -A 2 "ports:" docker-compose.yml | grep "5050" | head -1 | awk -F: '{print $2}' | awk -F'"' '{print $1}')

echo "Ports configurés:"
echo "   - PostgreSQL: $POSTGRES_PORT"
echo "   - Application: $APP_PORT"
echo "   - pgAdmin: $PGADMIN_PORT"

# Vérification des services
print_status "Vérification des services..."

services=$(grep -E "^  [a-z].*:$" docker-compose.yml | sed 's/://g' | awk '{print $1}')
echo "Services configurés:"
for service in $services; do
    echo "   - $service"
done

# Vérification de la configuration Nginx dans le script
print_status "Vérification de la configuration Nginx..."

if grep -q "proxy_pass.*3000" deploy-to-vps.sh; then
    print_success "Nginx configuré pour proxifier vers le port 3000"
else
    print_warning "Configuration Nginx à vérifier"
fi

# Vérification des variables d'environnement
print_status "Vérification des variables d'environnement..."

if grep -q "DATABASE_URL=*********************************************************/karma_com_db" deploy-to-vps.sh; then
    print_success "DATABASE_URL correctement configurée"
else
    print_error "DATABASE_URL mal configurée"
fi

if grep -q "NEXTAUTH_SECRET=karma_com_secret_key_2024" deploy-to-vps.sh; then
    print_success "NEXTAUTH_SECRET configuré"
else
    print_warning "NEXTAUTH_SECRET à vérifier"
fi

# Résumé
echo ""
print_status "Résumé de la configuration"
echo "=========================="

echo ""
echo "📊 Configuration Base de Données:"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo "   Password: $DB_PASSWORD"
echo "   Port: 5432"
echo ""
echo "🌐 Configuration Application:"
echo "   Port: 3000"
echo "   Environment: production"
echo "   Domain: kcs.zidani.org"
echo ""
echo "🔧 Services Déployés:"
echo "   - PostgreSQL (port 5432)"
echo "   - Application Next.js (port 3000)"
echo "   - pgAdmin (port 5050)"
echo "   - Nginx (ports 80/443)"
echo ""
echo "📁 Répertoire de déploiement:"
echo "   /home/<USER>/kcs"
echo ""
echo "🚀 Commande de déploiement:"
echo "   npm run deploy:vps"

print_success "Vérification terminée"
