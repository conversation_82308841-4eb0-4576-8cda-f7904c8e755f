import React, { useState } from 'react'
import { Plus, Calendar, Clock, User, Edit, Play, Trash2, MoreHorizontal } from 'lucide-react'
import { getStatusBadge, getInterviewTypeLabel } from '../../utils/statusHelpers'
import { formatDateFR, formatTimeFR, formatDateForInput } from '../../utils/dateHelpers'
import { Interview, InterviewFormData } from '../../utils/constants'

interface InterviewPlanningProps {
  interviews: Interview[]
  loading: boolean
  onCreateInterview: (formData: any) => Promise<void>
  onUpdateInterviewStatus: (interviewId: string, newStatus: string) => Promise<void>
  onEditInterview: (interview: Interview) => void
  onStartInterview: (interview: Interview) => void
  candidates: any[]
}

const InterviewPlanning: React.FC<InterviewPlanningProps> = ({
  interviews,
  loading,
  onCreateInterview,
  onUpdateInterviewStatus,
  onEditInterview,
  onStartInterview,
  candidates
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [formData, setFormData] = useState<InterviewFormData>({
    title: '',
    description: '',
    candidateId: '',
    scheduledAt: '',
    type: 'DISCOVERY'
  })
  const [submitting, setSubmitting] = useState(false)

  // Entretiens à venir (prochains 7 jours)
  const upcomingInterviews = interviews.filter(interview => {
    const interviewDate = new Date(interview.scheduledAt)
    const now = new Date()
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    return interviewDate >= now && interviewDate <= nextWeek && 
           ['SCHEDULED', 'CONFIRMED'].includes(interview.status)
  }).sort((a, b) => new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime())

  // Entretiens du jour
  const todayInterviews = interviews.filter(interview => {
    const interviewDate = new Date(interview.scheduledAt)
    const today = new Date()
    return interviewDate.toDateString() === today.toDateString() &&
           ['SCHEDULED', 'CONFIRMED'].includes(interview.status)
  }).sort((a, b) => new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime())

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.title || !formData.candidateId || !formData.scheduledAt) return

    setSubmitting(true)
    try {
      await onCreateInterview(formData)
      setFormData({
        title: '',
        description: '',
        candidateId: '',
        scheduledAt: '',
        type: 'DISCOVERY'
      })
      setShowCreateForm(false)
    } catch (error) {
      console.error('Erreur création entretien:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const handleQuickAction = async (interview: Interview, action: string) => {
    switch (action) {
      case 'confirm':
        await onUpdateInterviewStatus(interview.id, 'CONFIRMED')
        break
      case 'complete':
        await onUpdateInterviewStatus(interview.id, 'COMPLETED')
        break
      case 'cancel':
        await onUpdateInterviewStatus(interview.id, 'CANCELLED')
        break
      case 'start':
        onStartInterview(interview)
        break
      case 'edit':
        onEditInterview(interview)
        break
    }
  }

  return (
    <div className="space-y-6">
      {/* Section: Entretiens du jour */}
      <div className="dashboard-card">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Calendar className="mr-2 text-karma-pink" size={20} />
            Entretiens d'aujourd'hui
            <span className="ml-2 text-sm text-gray-500">({todayInterviews.length})</span>
          </h3>
        </div>

        {todayInterviews.length > 0 ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {todayInterviews.map((interview) => (
              <div key={interview.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">{interview.title}</h4>
                    <p className="text-sm text-gray-600">{interview.candidateName}</p>
                  </div>
                  {getStatusBadge(interview.status)}
                </div>
                
                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <div className="flex items-center">
                    <Clock size={14} className="mr-2" />
                    {formatTimeFR(interview.scheduledAt)}
                  </div>
                  <div className="flex items-center">
                    <User size={14} className="mr-2" />
                    {getInterviewTypeLabel(interview.type)}
                  </div>
                </div>

                <div className="flex space-x-2">
                  {interview.status === 'SCHEDULED' && (
                    <button
                      onClick={() => handleQuickAction(interview, 'confirm')}
                      className="flex-1 px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                    >
                      Confirmer
                    </button>
                  )}
                  {interview.status === 'CONFIRMED' && (
                    <button
                      onClick={() => handleQuickAction(interview, 'start')}
                      className="flex-1 px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 flex items-center justify-center"
                    >
                      <Play size={12} className="mr-1" />
                      Démarrer
                    </button>
                  )}
                  <button
                    onClick={() => handleQuickAction(interview, 'edit')}
                    className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                  >
                    <Edit size={12} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Calendar size={48} className="mx-auto mb-4 text-gray-300" />
            <p>Aucun entretien prévu aujourd'hui</p>
          </div>
        )}
      </div>

      {/* Section: Planification et création */}
      <div className="dashboard-card">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Planification des rendez-vous</h3>
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus size={16} />
            <span>Nouveau rendez-vous</span>
          </button>
        </div>

        {/* Formulaire de création */}
        {showCreateForm && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Titre de l'entretien *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-karma-pink focus:border-karma-pink"
                    placeholder="Ex: Entretien de découverte"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Candidat *
                  </label>
                  <select
                    value={formData.candidateId}
                    onChange={(e) => setFormData({ ...formData, candidateId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-karma-pink focus:border-karma-pink"
                    required
                  >
                    <option value="">Sélectionner un candidat</option>
                    {candidates.map((candidate) => (
                      <option key={candidate.id} value={candidate.id}>
                        {candidate.name} ({candidate.email})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date et heure *
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.scheduledAt}
                    onChange={(e) => setFormData({ ...formData, scheduledAt: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-karma-pink focus:border-karma-pink"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type d'entretien
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-karma-pink focus:border-karma-pink"
                  >
                    <option value="DISCOVERY">Découverte</option>
                    <option value="INTEGRATION">Intégration</option>
                    <option value="FOLLOW_UP">Suivi</option>
                    <option value="INTERVIEW">Entretien</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-karma-pink focus:border-karma-pink"
                  placeholder="Détails sur l'entretien..."
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  disabled={submitting}
                  className="btn-primary disabled:opacity-50"
                >
                  {submitting ? 'Création...' : 'Créer l\'entretien'}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Entretiens à venir */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">
            Prochains entretiens (7 jours)
            <span className="ml-2 text-sm text-gray-500">({upcomingInterviews.length})</span>
          </h4>

          {upcomingInterviews.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Entretien
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Candidat
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Date & Heure
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {upcomingInterviews.map((interview) => (
                    <tr key={interview.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{interview.title}</div>
                        {interview.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {interview.description}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{interview.candidateName}</div>
                        <div className="text-sm text-gray-500">{interview.candidateEmail}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{formatDateFR(interview.scheduledAt)}</div>
                        <div className="text-gray-500">{formatTimeFR(interview.scheduledAt)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {getInterviewTypeLabel(interview.type)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(interview.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {interview.status === 'SCHEDULED' && (
                            <button
                              onClick={() => handleQuickAction(interview, 'confirm')}
                              className="text-green-600 hover:text-green-900"
                              title="Confirmer"
                            >
                              Confirmer
                            </button>
                          )}
                          <button
                            onClick={() => handleQuickAction(interview, 'edit')}
                            className="text-karma-pink hover:text-karma-pink-dark"
                            title="Modifier"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleQuickAction(interview, 'cancel')}
                            className="text-red-600 hover:text-red-900"
                            title="Annuler"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Clock size={48} className="mx-auto mb-4 text-gray-300" />
              <p>Aucun entretien programmé dans les 7 prochains jours</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default InterviewPlanning
