# 🚀 Guide de Déploiement VPS - Karma Com Solidarité

## 🎯 Objectif
Déployer l'application Karma Com Solidarité sur votre VPS `*************` avec le domaine `kcz.zidani.org`.

## 📋 Informations VPS
- **IP** : `*************`
- **Utilisateur** : `vpsadmin`
- **Domaine** : `kcz.zidani.org`
- **SSL** : Let's Encrypt (automatique)

## 🔧 Prérequis

### 1. Configuration DNS
Configurez les enregistrements DNS chez votre registraire :
```
Type: A
Name: kcz
Value: *************
TTL: 300

Type: A  
Name: www.kcz
Value: *************
TTL: 300
```

### 2. Accès SSH
Assurez-vous d'avoir accès SSH au VPS :
```bash
ssh vpsadmin@*************
```

### 3. VPS Préparé
Votre VPS doit avoir été configuré avec le script `setup_vps.sh` incluant :
- ✅ Docker et Docker Compose
- ✅ Nginx
- ✅ Certbot (Let's Encrypt)
- ✅ Utilisateur `vpsadmin`

## 📁 Fichiers à Déployer

### Fichiers Essentiels
```
karma-com-solidarite/
├── src/                    # Code source Next.js
├── prisma/                 # Schéma base de données
├── public/                 # Fichiers statiques
├── package.json            # Dépendances
├── package-lock.json       # Lock des versions
├── next.config.js          # Configuration Next.js
├── tailwind.config.js      # Configuration Tailwind
├── postcss.config.js       # Configuration PostCSS
├── tsconfig.json           # Configuration TypeScript
├── Dockerfile.simple       # Image Docker
└── docker-compose.yml      # Services Docker
```

### Fichiers NON Nécessaires
```
❌ node_modules/           # Reconstruit dans Docker
❌ .next/                  # Reconstruit pendant le build
❌ .git/                   # Historique Git
❌ .env.local              # Variables locales
❌ scripts/                # Scripts de développement
❌ README.md               # Documentation
```

## 🚀 Déploiement Automatique

### 1. Exécution du Script
```bash
# Depuis la racine de votre projet
chmod +x deploy-to-vps.sh
./deploy-to-vps.sh
```

### 2. Étapes Automatisées
Le script effectue automatiquement :
1. ✅ **Vérification** des prérequis locaux
2. ✅ **Test** de connexion SSH au VPS
3. ✅ **Création** des répertoires sur le VPS
4. ✅ **Copie** des fichiers nécessaires
5. ✅ **Construction** de l'image Docker
6. ✅ **Configuration** Docker Compose
7. ✅ **Démarrage** des services
8. ✅ **Configuration** Nginx
9. ✅ **Installation** SSL Let's Encrypt
10. ✅ **Vérification** finale

## 🐳 Architecture Docker

### Services Déployés
```yaml
services:
  postgres:          # Base de données PostgreSQL
    - Port: 5432 (local seulement)
    - Volume: postgres_data
    - Health check: Activé
    
  app:              # Application Next.js
    - Port: 3000 (local seulement)
    - Image: karma-com-app:latest
    - Health check: Activé
```

### Configuration Réseau
```
Internet → Nginx (443/80) → App (3000) → PostgreSQL (5432)
```

## 🌐 Configuration Nginx

### Fonctionnalités
- ✅ **Reverse Proxy** vers l'application
- ✅ **SSL/TLS** automatique
- ✅ **Compression Gzip**
- ✅ **Headers de sécurité**
- ✅ **Cache des assets statiques**
- ✅ **Support WebSocket**

### Fichier de Configuration
```nginx
# /etc/nginx/sites-available/kcz.zidani.org
server {
    listen 443 ssl http2;
    server_name kcz.zidani.org www.kcz.zidani.org;
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        # Headers et configuration proxy...
    }
}
```

## 🔒 Sécurité

### SSL/TLS
- ✅ **Certificat** Let's Encrypt automatique
- ✅ **Redirection** HTTP → HTTPS
- ✅ **HSTS** activé
- ✅ **OCSP Stapling** activé

### Headers de Sécurité
```nginx
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```

### Accès Base de Données
- 🔒 **Port 5432** accessible uniquement en local
- 🔒 **Utilisateur** dédié avec mot de passe fort
- 🔒 **Réseau Docker** isolé

## 📊 Monitoring et Maintenance

### Commandes Utiles sur le VPS
```bash
# Se connecter au VPS
ssh vpsadmin@*************

# Aller dans le répertoire de l'application
cd /opt/karma-com-solidarite

# Voir l'état des services
docker-compose ps

# Voir les logs
docker-compose logs -f app
docker-compose logs -f postgres

# Redémarrer l'application
docker-compose restart app

# Redémarrer tous les services
docker-compose restart

# Arrêter les services
docker-compose down

# Démarrer les services
docker-compose up -d
```

### Logs Importants
```bash
# Logs Nginx
sudo tail -f /var/log/nginx/karma-com-access.log
sudo tail -f /var/log/nginx/karma-com-error.log

# Logs Application
docker-compose logs -f app

# Logs Base de données
docker-compose logs -f postgres

# Logs système
sudo journalctl -u nginx -f
sudo journalctl -u docker -f
```

### Backup Base de Données
```bash
# Backup manuel
docker exec karma-postgres pg_dump -U karma_user karma_com_db > backup_$(date +%Y%m%d).sql

# Restauration
docker exec -i karma-postgres psql -U karma_user karma_com_db < backup_20241212.sql
```

## 🔧 Dépannage

### Application Non Accessible
```bash
# Vérifier les services Docker
docker-compose ps

# Vérifier les logs
docker-compose logs app

# Vérifier Nginx
sudo nginx -t
sudo systemctl status nginx

# Vérifier les ports
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :443
```

### Erreurs SSL
```bash
# Renouveler le certificat
sudo certbot renew

# Vérifier le certificat
sudo certbot certificates

# Tester la configuration SSL
curl -I https://kcz.zidani.org
```

### Problèmes de Base de Données
```bash
# Vérifier PostgreSQL
docker-compose exec postgres pg_isready -U karma_user

# Se connecter à la base
docker-compose exec postgres psql -U karma_user karma_com_db

# Voir les tables
docker-compose exec postgres psql -U karma_user karma_com_db -c "\dt"
```

## 🔄 Mise à Jour de l'Application

### Déploiement d'une Nouvelle Version
```bash
# 1. Sur votre machine locale
git pull origin main
./deploy-to-vps.sh

# 2. Le script met automatiquement à jour :
#    - Le code source
#    - L'image Docker
#    - Les services
```

### Rollback en Cas de Problème
```bash
# Sur le VPS
cd /opt/karma-com-solidarite

# Arrêter les services
docker-compose down

# Revenir à l'image précédente (si sauvegardée)
docker tag karma-com-app:latest karma-com-app:backup
docker tag karma-com-app:previous karma-com-app:latest

# Redémarrer
docker-compose up -d
```

## 📈 Performance

### Optimisations Appliquées
- ✅ **Compression Gzip** pour les assets
- ✅ **Cache des fichiers statiques** (1 an)
- ✅ **Image Docker** multi-stage optimisée
- ✅ **Health checks** pour la disponibilité
- ✅ **Restart policy** automatique

### Monitoring des Ressources
```bash
# Utilisation CPU/RAM
docker stats

# Espace disque
df -h

# Logs de taille
du -sh /var/log/nginx/
docker system df
```

## 🎉 Résultat Final

Après déploiement réussi :

### URLs d'Accès
- 🌐 **Application** : https://kcz.zidani.org
- 🌐 **Dashboard** : https://kcz.zidani.org/dashboard
- 🌐 **API** : https://kcz.zidani.org/api/health

### Services Actifs
- ✅ **Application Next.js** sur port 3000 (interne)
- ✅ **PostgreSQL** sur port 5432 (interne)
- ✅ **Nginx** sur ports 80/443 (public)
- ✅ **SSL/TLS** Let's Encrypt

### Fonctionnalités Disponibles
- ✅ **Inscription** associations/organisations/bénévoles
- ✅ **Connexion** sécurisée
- ✅ **Dashboard** administrateur
- ✅ **Gestion** des candidatures
- ✅ **API** complète

**Votre application Karma Com Solidarité est maintenant déployée et accessible sur https://kcz.zidani.org !** 🚀
