// ./karma-com-dashboard/src/app/layout.tsx
import type { Metadata } from "next";
import LocalFont from "next/font/local";
import "./globals.css";

const geistSans = LocalFont({
  src: [
    {
      path: "../../public/fonts/Geist/Geist-Regular.woff", // Adjust path as needed
      weight: "400",
      style: "normal",
    },
    // ... add other weights/styles if available
  ],
  variable: "--font-geist-sans", // Define a CSS variable for the font
});

const geistMono = LocalFont({
  src: [
    {
      path: "../../public/fonts/Geist/Geist-Mono-Regular.woff", // Adjust path as needed
      weight: "400",
      style: "normal",
    },
    // ... add other weights/styles if available
  ],
  variable: "--font-geist-mono",
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${geistSans.variable} ${geistMono.variable}`}>
      <body>{children}</body>
    </html>
  );
}