# Dockerfile pour runtime sans build (plus rapide)
FROM node:18-alpine

WORKDIR /app

# Installer les dépendances système
RUN apk add --no-cache libc6-compat curl

# Copier les fichiers de configuration
COPY package.json package-lock.json* ./
COPY tsconfig.json ./
COPY next.config.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY prisma ./prisma

# Installer seulement les dépendances de production
RUN npm ci --only=production --no-optional

# Copier le code source
COPY . .

# Générer Prisma client
RUN npx prisma generate

# Créer le dossier public s'il n'existe pas
RUN mkdir -p public

# Variables d'environnement
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Exposer le port
EXPOSE 3000

# Commande de démarrage en mode développement (plus rapide)
CMD ["npm", "run", "dev", "--", "--hostname", "0.0.0.0"]
