import React, { useState, useEffect } from 'react'
import { Bell, Mail, Clock, Setting<PERSON>, Send, CheckCircle, AlertCircle, Users } from 'lucide-react'
import { getStatusBadge, getInterviewTypeLabel } from '../../utils/statusHelpers'
import { formatDateFR, formatTimeFR, getRelativeTime } from '../../utils/dateHelpers'
import { Interview } from '../../utils/constants'

interface InterviewNotificationsProps {
  interviews: Interview[]
  loading: boolean
}

interface NotificationSettings {
  emailReminders: boolean
  reminderHours: number
  autoConfirmation: boolean
  reminderTemplate: string
}

const InterviewNotifications: React.FC<InterviewNotificationsProps> = ({
  interviews,
  loading
}) => {
  const [settings, setSettings] = useState<NotificationSettings>({
    emailReminders: true,
    reminderHours: 24,
    autoConfirmation: false,
    reminderTemplate: 'default'
  })
  const [sendingReminders, setSendingReminders] = useState(false)
  const [lastReminderSent, setLastReminderSent] = useState<string | null>(null)

  // Entretiens nécessitant des rappels (dans les prochaines 48h)
  const interviewsNeedingReminders = interviews.filter(interview => {
    const interviewDate = new Date(interview.scheduledAt)
    const now = new Date()
    const reminderTime = new Date(interviewDate.getTime() - settings.reminderHours * 60 * 60 * 1000)
    const in48Hours = new Date(now.getTime() + 48 * 60 * 60 * 1000)
    
    return interviewDate >= now && 
           interviewDate <= in48Hours && 
           ['SCHEDULED', 'CONFIRMED'].includes(interview.status)
  }).sort((a, b) => new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime())

  // Entretiens avec rappels déjà envoyés (simulation)
  const interviewsWithReminders = interviews.filter(interview => {
    const interviewDate = new Date(interview.scheduledAt)
    const now = new Date()
    const reminderTime = new Date(interviewDate.getTime() - settings.reminderHours * 60 * 60 * 1000)
    
    return reminderTime <= now && 
           interviewDate >= now && 
           ['SCHEDULED', 'CONFIRMED'].includes(interview.status)
  })

  const handleSendReminders = async () => {
    setSendingReminders(true)
    try {
      // Simulation d'envoi de rappels
      await new Promise(resolve => setTimeout(resolve, 2000))
      setLastReminderSent(new Date().toISOString())
      console.log(`Rappels envoyés pour ${interviewsNeedingReminders.length} entretiens`)
    } catch (error) {
      console.error('Erreur envoi rappels:', error)
    } finally {
      setSendingReminders(false)
    }
  }

  const handleSettingsChange = (key: keyof NotificationSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const getNotificationStatus = (interview: Interview) => {
    const interviewDate = new Date(interview.scheduledAt)
    const now = new Date()
    const reminderTime = new Date(interviewDate.getTime() - settings.reminderHours * 60 * 60 * 1000)
    
    if (reminderTime <= now && interviewDate >= now) {
      return { status: 'sent', label: 'Rappel envoyé', color: 'text-green-600' }
    } else if (interviewDate >= now) {
      return { status: 'pending', label: 'En attente', color: 'text-yellow-600' }
    } else {
      return { status: 'expired', label: 'Expiré', color: 'text-gray-500' }
    }
  }

  return (
    <div className="space-y-6">
      {/* Configuration des notifications */}
      <div className="dashboard-card">
        <div className="flex items-center mb-6">
          <Settings className="mr-2 text-karma-pink" size={20} />
          <h3 className="text-lg font-semibold text-gray-900">Configuration des notifications</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Rappels par email</label>
                <p className="text-xs text-gray-500">Envoyer des rappels automatiques aux membres</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.emailReminders}
                  onChange={(e) => handleSettingsChange('emailReminders', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-karma-pink/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-karma-pink"></div>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Délai de rappel (heures avant l'entretien)
              </label>
              <select
                value={settings.reminderHours}
                onChange={(e) => handleSettingsChange('reminderHours', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-karma-pink focus:border-karma-pink"
              >
                <option value={1}>1 heure</option>
                <option value={2}>2 heures</option>
                <option value={6}>6 heures</option>
                <option value={12}>12 heures</option>
                <option value={24}>24 heures</option>
                <option value={48}>48 heures</option>
              </select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Confirmation automatique</label>
                <p className="text-xs text-gray-500">Confirmer automatiquement après rappel</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.autoConfirmation}
                  onChange={(e) => handleSettingsChange('autoConfirmation', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-karma-pink/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-karma-pink"></div>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Modèle de rappel
              </label>
              <select
                value={settings.reminderTemplate}
                onChange={(e) => handleSettingsChange('reminderTemplate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-karma-pink focus:border-karma-pink"
              >
                <option value="default">Modèle par défaut</option>
                <option value="formal">Modèle formel</option>
                <option value="friendly">Modèle amical</option>
                <option value="brief">Modèle bref</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Rappels à envoyer */}
      <div className="dashboard-card">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Bell className="mr-2 text-karma-pink" size={20} />
            <h3 className="text-lg font-semibold text-gray-900">
              Rappels à envoyer
              <span className="ml-2 text-sm text-gray-500">({interviewsNeedingReminders.length})</span>
            </h3>
          </div>
          
          {interviewsNeedingReminders.length > 0 && (
            <button
              onClick={handleSendReminders}
              disabled={sendingReminders || !settings.emailReminders}
              className="btn-primary flex items-center space-x-2 disabled:opacity-50"
            >
              <Send size={16} />
              <span>{sendingReminders ? 'Envoi...' : 'Envoyer les rappels'}</span>
            </button>
          )}
        </div>

        {interviewsNeedingReminders.length > 0 ? (
          <div className="space-y-3">
            {interviewsNeedingReminders.map((interview) => (
              <div key={interview.id} className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                    <Clock className="text-yellow-600" size={20} />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{interview.title}</h4>
                    <p className="text-sm text-gray-600">
                      {interview.candidateName} • {formatDateFR(interview.scheduledAt)} à {formatTimeFR(interview.scheduledAt)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-yellow-700">
                    {getRelativeTime(interview.scheduledAt)}
                  </span>
                  {getStatusBadge(interview.status)}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Bell size={48} className="mx-auto mb-4 text-gray-300" />
            <p>Aucun rappel à envoyer pour le moment</p>
          </div>
        )}
      </div>

      {/* Historique des notifications */}
      <div className="dashboard-card">
        <div className="flex items-center mb-6">
          <Mail className="mr-2 text-karma-pink" size={20} />
          <h3 className="text-lg font-semibold text-gray-900">
            Historique des notifications
            <span className="ml-2 text-sm text-gray-500">({interviewsWithReminders.length})</span>
          </h3>
        </div>

        {lastReminderSent && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="text-green-600 mr-2" size={16} />
              <span className="text-sm text-green-800">
                Derniers rappels envoyés le {formatDateFR(lastReminderSent)} à {formatTimeFR(lastReminderSent)}
              </span>
            </div>
          </div>
        )}

        {interviewsWithReminders.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Entretien
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Candidat
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Date & Heure
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Statut notification
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Statut entretien
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {interviewsWithReminders.map((interview) => {
                  const notificationStatus = getNotificationStatus(interview)
                  return (
                    <tr key={interview.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{interview.title}</div>
                        <div className="text-sm text-gray-500">{getInterviewTypeLabel(interview.type)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{interview.candidateName}</div>
                        <div className="text-sm text-gray-500">{interview.candidateEmail}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{formatDateFR(interview.scheduledAt)}</div>
                        <div className="text-gray-500">{formatTimeFR(interview.scheduledAt)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-sm ${notificationStatus.color}`}>
                          {notificationStatus.label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(interview.status)}
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Mail size={48} className="mx-auto mb-4 text-gray-300" />
            <p>Aucune notification envoyée récemment</p>
          </div>
        )}
      </div>

      {/* Statistiques des notifications */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="dashboard-card text-center">
          <div className="text-2xl font-bold text-blue-600">{interviewsNeedingReminders.length}</div>
          <div className="text-sm text-gray-600">Rappels en attente</div>
        </div>
        <div className="dashboard-card text-center">
          <div className="text-2xl font-bold text-green-600">{interviewsWithReminders.length}</div>
          <div className="text-sm text-gray-600">Rappels envoyés</div>
        </div>
        <div className="dashboard-card text-center">
          <div className="text-2xl font-bold text-karma-pink">{settings.reminderHours}h</div>
          <div className="text-sm text-gray-600">Délai de rappel</div>
        </div>
      </div>
    </div>
  )
}

export default InterviewNotifications
