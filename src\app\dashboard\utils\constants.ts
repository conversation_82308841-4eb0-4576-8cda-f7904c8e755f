// Types et interfaces
export interface RecentApplication {
  id: string
  name: string
  email: string
  date: string
  status: string
  organization?: string
  type?: string
}

export interface Interview {
  id: string
  title: string
  description?: string
  candidateId: string
  candidateName: string
  candidateEmail: string
  scheduledAt: string
  type: 'DISCOVERY' | 'INTEGRATION' | 'FOLLOW_UP' | 'INTERVIEW'
  status: 'SCHEDULED' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED'
  createdAt: string
  notes?: string
}

export interface InterviewFormData {
  title: string
  description: string
  candidateId: string
  scheduledAt: string
  type: 'DISCOVERY' | 'FOLLOW_UP' | 'FINAL'
}

// Configuration des statuts
export const STATUS_CONFIG = {
  // Statuts candidatures
  pending: { label: 'En attente', color: 'bg-yellow-100 text-yellow-800 border border-yellow-200' },
  approved: { label: 'Approuvé', color: 'bg-green-100 text-green-800 border border-green-200' },
  active: { label: 'Actif', color: 'bg-blue-100 text-blue-800 border border-blue-200' },
  rejected: { label: 'Rejeté', color: 'bg-red-100 text-red-800 border border-red-200' },
  inactive: { label: 'Inactif', color: 'bg-gray-100 text-gray-800 border border-gray-200' },
  suspended: { label: 'Suspendu', color: 'bg-orange-100 text-orange-800 border border-orange-200' },

  // Statuts entretiens
  SCHEDULED: { label: 'Programmé', color: 'bg-blue-100 text-blue-800 border border-blue-200' },
  CONFIRMED: { label: 'Confirmé', color: 'bg-green-100 text-green-800 border border-green-200' },
  COMPLETED: { label: 'Terminé', color: 'bg-gray-100 text-gray-800 border border-gray-200' },
  CANCELLED: { label: 'Annulé', color: 'bg-red-100 text-red-800 border border-red-200' },

  // Alias pour compatibilité
  confirmed: { label: 'Confirmé', color: 'bg-green-100 text-green-800 border border-green-200' },
  completed: { label: 'Terminé', color: 'bg-gray-100 text-gray-800 border border-gray-200' },
  cancelled: { label: 'Annulé', color: 'bg-red-100 text-red-800 border border-red-200' },
  scheduled: { label: 'Programmé', color: 'bg-blue-100 text-blue-800 border border-blue-200' }
}

// Configuration des types d'entretien
export const INTERVIEW_TYPE_LABELS = {
  DISCOVERY: 'Découverte',
  INTEGRATION: 'Intégration', 
  FOLLOW_UP: 'Suivi',
  INTERVIEW: 'Entretien'
}

// Configuration des icônes par type
export const TYPE_ICONS = {
  association: 'Building2',
  organization: 'Building2',
  volunteer: 'Heart',
  individual: 'User'
}

// Configuration des types d'enregistrement
export const REGISTRATION_TYPES = {
  'Association': { label: 'Association', color: 'bg-blue-100 text-blue-800', icon: 'Building2' },
  'Organisation': { label: 'Organisation', color: 'bg-purple-100 text-purple-800', icon: 'Building2' },
  'Bénévole': { label: 'Bénévole', color: 'bg-green-100 text-green-800', icon: 'Heart' }
}

// Constantes de pagination
export const PAGINATION_LIMITS = {
  recentApplications: 10,
  volunteers: 10,
  candidates: 50
}

// Messages par défaut
export const DEFAULT_MESSAGES = {
  loading: 'Chargement...',
  noData: 'Aucune donnée disponible',
  error: 'Une erreur est survenue',
  success: 'Opération réussie'
}

// Configuration des onglets
export const DASHBOARD_TABS = [
  { id: 'overview', label: 'Vue d\'ensemble', icon: 'TrendingUp' },
  { id: 'candidates', label: 'Candidatures', icon: 'Users' },
  { id: 'interviews', label: 'Entretiens', icon: 'Calendar' },
  { id: 'volunteers', label: 'Bénévoles', icon: 'Heart' },
  { id: 'members', label: 'Membres', icon: 'Building2' }
]
