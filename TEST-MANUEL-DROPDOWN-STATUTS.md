# 🧪 Test Manuel - Liste Déroulante Statuts Candidatures

## 🎯 Objectif
Vérifier que la liste déroulante des statuts dans l'onglet Candidatures fonctionne correctement et met à jour les statuts en temps réel.

## 📋 Prérequis
- ✅ Application démarrée : `npm run dev`
- ✅ Base de données avec candidats
- ✅ Dashboard accessible : http://localhost:3000/dashboard

## 🔍 Tests à Effectuer

### 1. 🏠 Accès à l'Onglet Candidatures
1. **Ouvrir** : http://localhost:3000/dashboard
2. **Cliquer** : Onglet "Candidatures"
3. **Vérifier** : Section "Gestion des Candidatures" visible
4. **Vérifier** : Tableau avec colonnes (Candidat, Type, Organisation, Statut, Date, Actions)

### 2. 📋 Vérification de la Colonne Statut

#### Affichage Initial
1. **Localiser** : Colonne "Statut" dans le tableau
2. **Vérifier** : Chaque ligne a une liste déroulante
3. **Vérifier** : Statuts actuels affichés avec badges colorés :
   - 🟡 **En attente** (pending) - jaune
   - 🟢 **Approuvé** (approved) - vert  
   - 🔵 **Actif** (active) - bleu
   - 🔴 **Rejeté** (rejected) - rouge

#### Options de la Liste Déroulante
1. **Cliquer** : Sur une liste déroulante de statut
2. **Vérifier** : 4 options disponibles :
   - "En attente"
   - "Approuvé"
   - "Actif"
   - "Rejeté"
3. **Vérifier** : Option actuelle sélectionnée
4. **Vérifier** : Liste se ferme en cliquant ailleurs

### 3. 🔄 Test de Changement de Statut

#### Test 1 : En attente → Approuvé
1. **Localiser** : Un candidat avec statut "En attente"
2. **Cliquer** : Sur la liste déroulante
3. **Sélectionner** : "Approuvé"
4. **Vérifier** : Badge devient vert immédiatement
5. **Vérifier** : Texte change pour "Approuvé"

#### Test 2 : Approuvé → Actif
1. **Localiser** : Un candidat avec statut "Approuvé"
2. **Cliquer** : Sur la liste déroulante
3. **Sélectionner** : "Actif"
4. **Vérifier** : Badge devient bleu immédiatement
5. **Vérifier** : Texte change pour "Actif"

#### Test 3 : Actif → Rejeté
1. **Localiser** : Un candidat avec statut "Actif"
2. **Cliquer** : Sur la liste déroulante
3. **Sélectionner** : "Rejeté"
4. **Vérifier** : Badge devient rouge immédiatement
5. **Vérifier** : Texte change pour "Rejeté"

#### Test 4 : Rejeté → En attente
1. **Localiser** : Un candidat avec statut "Rejeté"
2. **Cliquer** : Sur la liste déroulante
3. **Sélectionner** : "En attente"
4. **Vérifier** : Badge devient jaune immédiatement
5. **Vérifier** : Texte change pour "En attente"

### 4. ✅ Vérification de la Persistance

#### Test de Rechargement
1. **Changer** : Le statut d'un candidat
2. **Recharger** : La page (F5)
3. **Vérifier** : Nouveau statut conservé
4. **Vérifier** : Badge coloré correct

#### Test de Navigation
1. **Changer** : Le statut d'un candidat
2. **Naviguer** : Vers un autre onglet (Vue d'ensemble)
3. **Revenir** : À l'onglet Candidatures
4. **Vérifier** : Statut conservé

### 5. 📊 Test des Messages de Feedback

#### Message de Succès
1. **Changer** : Un statut
2. **Vérifier** : Message de succès vert s'affiche
3. **Vérifier** : Message disparaît après 3 secondes
4. **Vérifier** : Contenu du message approprié

#### Gestion d'Erreur
1. **Déconnecter** : Internet (si possible)
2. **Tenter** : Changement de statut
3. **Vérifier** : Message d'erreur rouge s'affiche
4. **Reconnecter** : Internet
5. **Vérifier** : Fonctionnement normal restauré

### 6. 🎨 Test des Couleurs et Styles

#### Badges de Statut
1. **Vérifier** : Couleurs distinctes pour chaque statut
2. **Vérifier** : Bordures colorées visibles
3. **Vérifier** : Texte lisible sur fond coloré
4. **Vérifier** : Taille appropriée des badges

#### Liste Déroulante
1. **Vérifier** : Bordure visible autour de la liste
2. **Vérifier** : Focus visible lors du clic
3. **Vérifier** : Options bien espacées
4. **Vérifier** : Responsive sur mobile

## 📊 Résultats Attendus

### Fonctionnalité
- ✅ **Changement immédiat** : Badge se met à jour instantanément
- ✅ **Persistance** : Statut conservé après rechargement
- ✅ **Feedback** : Messages de succès/erreur appropriés
- ✅ **API** : Données synchronisées avec la base

### Interface
- ✅ **4 options** : En attente, Approuvé, Actif, Rejeté
- ✅ **Couleurs cohérentes** : Jaune, Vert, Bleu, Rouge
- ✅ **Design moderne** : Bordures, focus, responsive
- ✅ **Accessibilité** : Contraste, navigation clavier

### Données
- ✅ **10 candidats** au total (selon test automatique)
- ✅ **Répartition actuelle** :
  - 6 candidats "Actif"
  - 2 candidats "Rejeté"
  - 1 candidat "En attente"
  - 1 candidat "Approuvé"

## 🐛 Problèmes Possibles

### Liste déroulante ne s'ouvre pas
**Causes :**
- Erreur JavaScript
- Problème de CSS
- Conflit d'événements

**Solutions :**
- Vérifier console navigateur
- Recharger la page
- Tester avec un autre navigateur

### Changement de statut ne fonctionne pas
**Causes :**
- API inaccessible
- Erreur de validation
- Problème de réseau

**Solutions :**
- Vérifier les logs serveur
- Tester API directement
- Vérifier la connexion

### Badge ne se met pas à jour
**Causes :**
- Problème de state React
- Erreur de rendu
- Cache navigateur

**Solutions :**
- Recharger la page
- Vider le cache
- Vérifier les props du composant

### Couleurs incorrectes
**Causes :**
- CSS non chargé
- Classes manquantes
- Conflit de styles

**Solutions :**
- Vérifier les classes CSS
- Inspecter l'élément
- Recharger les styles

## 🔧 Commandes de Debug

### Test Automatique
```bash
# Test complet de la liste déroulante
npm run test:candidate-status

# Vérifier l'API candidats
curl http://localhost:3000/api/candidates

# Tester changement de statut
curl -X PATCH "http://localhost:3000/api/candidates/ID/status" \
  -H "Content-Type: application/json" \
  -d '{"status":"approved"}'
```

### Console Navigateur
```javascript
// Vérifier les candidats chargés
console.log('Candidats:', candidates)

// Vérifier les fonctions de statut
console.log('Fonction mise à jour:', handleUpdateCandidateStatus)

// Vérifier les badges
document.querySelectorAll('[class*="bg-"]').forEach(el => 
  console.log(el.textContent, el.className)
)
```

### Base de Données
```sql
-- Vérifier les statuts en base
SELECT "membershipStatus", COUNT(*) 
FROM "Profile" 
GROUP BY "membershipStatus";

-- Voir les candidats
SELECT u.name, u.email, p."membershipStatus" 
FROM "User" u 
JOIN "Profile" p ON u.id = p."userId" 
WHERE u."userType" != 'HR_ADMIN';
```

## 📈 Métriques de Performance

### Temps de Réponse
- ⏱️ **Changement de statut** : < 1 seconde
- ⏱️ **Mise à jour visuelle** : Immédiate
- ⏱️ **Chargement initial** : < 2 secondes

### Fiabilité
- 🎯 **Taux de succès** : 100% des changements
- 🎯 **Persistance** : Données conservées
- 🎯 **Synchronisation** : API et interface cohérentes

## ✅ Checklist de Validation

### Interface
- [ ] Onglet Candidatures accessible
- [ ] Tableau avec colonne Statut visible
- [ ] Listes déroulantes présentes sur chaque ligne
- [ ] 4 options disponibles dans chaque liste
- [ ] Badges colorés selon les statuts

### Fonctionnalité
- [ ] Clic sur liste déroulante ouvre les options
- [ ] Sélection d'option change le statut
- [ ] Badge se met à jour immédiatement
- [ ] Message de succès s'affiche
- [ ] Statut persistant après rechargement

### Couleurs
- [ ] En attente : Badge jaune
- [ ] Approuvé : Badge vert
- [ ] Actif : Badge bleu
- [ ] Rejeté : Badge rouge
- [ ] Bordures colorées visibles

### API
- [ ] Changements enregistrés en base
- [ ] Réponses API correctes
- [ ] Gestion d'erreurs fonctionnelle
- [ ] Synchronisation temps réel

## 🎉 Résultat Final

Si tous les tests passent :
- ✅ **Liste déroulante complètement fonctionnelle**
- ✅ **Changements de statuts en temps réel**
- ✅ **Interface moderne et intuitive**
- ✅ **Persistance des données garantie**

**Le bug de la liste déroulante des statuts est corrigé !** 🚀

## 📊 Corrections Apportées

1. **Ajout de l'option "Actif"** dans la liste déroulante
2. **Amélioration du style** de la liste (bordures, focus)
3. **Mise à jour immédiate** de l'état local
4. **Messages de feedback** pour l'utilisateur
5. **Gestion d'erreurs** améliorée
6. **Logs de debug** pour traçabilité
