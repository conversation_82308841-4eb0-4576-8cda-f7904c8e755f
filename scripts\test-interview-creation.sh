#!/bin/bash

# Script pour tester la création d'entretien via API
echo "🧪 Test Création Entretien - API Direct"
echo "======================================="

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# URL de l'API
API_URL="http://localhost:3001/api"

# Test 1: Créer un bénévole de test
log "1. Création d'un bénévole de test..."

VOLUNTEER_DATA='{
  "firstName": "Test",
  "lastName": "Bénévole",
  "email": "<EMAIL>",
  "phoneNumber": "06 12 34 56 78",
  "currentStatus": "En poste",
  "contributionPole": "IT et Transformation Numérique",
  "weeklyHours": "Entre 5 et 10 heures par semaine",
  "availability": "Soirée (18h à 22h)",
  "participationRhythm": "Régulier (chaque semaine)",
  "howDidYouKnowUs": "Site internet de Karma Com Solidarité"
}'

echo "Envoi des données bénévole..."
VOLUNTEER_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d "$VOLUNTEER_DATA" \
  "$API_URL/volunteer-application")

echo "Réponse API bénévole:"
echo "$VOLUNTEER_RESPONSE" | jq '.' 2>/dev/null || echo "$VOLUNTEER_RESPONSE"

# Extraire l'ID du bénévole
VOLUNTEER_ID=$(echo "$VOLUNTEER_RESPONSE" | jq -r '.applicationId' 2>/dev/null)

if [ "$VOLUNTEER_ID" != "null" ] && [ -n "$VOLUNTEER_ID" ]; then
    success "Bénévole créé avec ID: $VOLUNTEER_ID"
else
    error "Échec création bénévole"
    exit 1
fi

# Test 2: Créer un entretien pour le bénévole
log "2. Création d'un entretien pour le bénévole..."

INTERVIEW_DATA="{
  \"title\": \"Entretien Découverte - Test Bénévole\",
  \"description\": \"Entretien de découverte pour candidature bénévole\",
  \"candidateId\": \"$VOLUNTEER_ID\",
  \"scheduledAt\": \"$(date -d '+7 days' -Iseconds)\",
  \"type\": \"DISCOVERY\"
}"

echo "Données entretien:"
echo "$INTERVIEW_DATA" | jq '.' 2>/dev/null || echo "$INTERVIEW_DATA"

echo "Envoi de la requête d'entretien..."
INTERVIEW_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d "$INTERVIEW_DATA" \
  "$API_URL/interviews")

echo "Réponse API entretien:"
echo "$INTERVIEW_RESPONSE" | jq '.' 2>/dev/null || echo "$INTERVIEW_RESPONSE"

# Vérifier le statut de la réponse
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
  -H "Content-Type: application/json" \
  -d "$INTERVIEW_DATA" \
  "$API_URL/interviews")

echo "Statut HTTP: $HTTP_STATUS"

if [ "$HTTP_STATUS" = "201" ] || [ "$HTTP_STATUS" = "200" ]; then
    success "Entretien créé avec succès"
else
    error "Échec création entretien (HTTP $HTTP_STATUS)"
    
    # Afficher les détails de l'erreur
    echo "Détails de l'erreur:"
    echo "$INTERVIEW_RESPONSE"
fi

# Test 3: Vérifier les entretiens créés
log "3. Vérification des entretiens..."

INTERVIEWS_LIST=$(curl -s "$API_URL/interviews")
echo "Liste des entretiens:"
echo "$INTERVIEWS_LIST" | jq '.' 2>/dev/null || echo "$INTERVIEWS_LIST"

# Test 4: Nettoyage (optionnel)
log "4. Nettoyage des données de test..."

# Note: Vous devrez implémenter une API DELETE ou nettoyer manuellement
echo "Nettoyage manuel requis pour:"
echo "- Bénévole ID: $VOLUNTEER_ID"
echo "- Email: <EMAIL>"

echo ""
log "Test terminé"
echo "============"
echo ""
echo "📋 Résumé:"
echo "   - Création bénévole: $([ "$VOLUNTEER_ID" != "null" ] && echo "✅ OK" || echo "❌ ÉCHEC")"
echo "   - Création entretien: $([ "$HTTP_STATUS" = "201" ] || [ "$HTTP_STATUS" = "200" ] && echo "✅ OK" || echo "❌ ÉCHEC ($HTTP_STATUS)")"
echo ""
echo "🔍 Pour déboguer:"
echo "   1. Vérifiez les logs du serveur Next.js"
echo "   2. Vérifiez la base de données"
echo "   3. Testez avec Postman ou un autre client REST"
