// Fonction pour formater une date en français
export const formatDateFR = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('fr-FR')
}

// Fonction pour formater une date complète en français
export const formatFullDateFR = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('fr-FR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Fonction pour formater une heure en français
export const formatTimeFR = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleTimeString('fr-FR', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Fonction pour formater une date et heure complète
export const formatDateTimeFR = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return `${formatDateFR(dateObj)} à ${formatTimeFR(dateObj)}`
}

// Fonction pour formater une date pour un input datetime-local
export const formatDateForInput = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toISOString().slice(0, 16)
}

// Fonction pour obtenir la date d'aujourd'hui au format ISO
export const getTodayISO = (): string => {
  return new Date().toISOString().split('T')[0]
}

// Fonction pour obtenir la date dans X jours
export const getDateInDays = (days: number): Date => {
  const date = new Date()
  date.setDate(date.getDate() + days)
  return date
}

// Fonction pour vérifier si une date est dans le futur
export const isFutureDate = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj > new Date()
}

// Fonction pour vérifier si une date est aujourd'hui
export const isToday = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const today = new Date()
  return dateObj.toDateString() === today.toDateString()
}

// Fonction pour obtenir le temps relatif (il y a X jours, dans X jours)
export const getRelativeTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInMs = dateObj.getTime() - now.getTime()
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))

  if (diffInDays === 0) {
    return "Aujourd'hui"
  } else if (diffInDays === 1) {
    return "Demain"
  } else if (diffInDays === -1) {
    return "Hier"
  } else if (diffInDays > 1) {
    return `Dans ${diffInDays} jours`
  } else {
    return `Il y a ${Math.abs(diffInDays)} jours`
  }
}

// Fonction pour trier les dates
export const sortByDate = (a: string | Date, b: string | Date, ascending: boolean = true): number => {
  const dateA = typeof a === 'string' ? new Date(a) : a
  const dateB = typeof b === 'string' ? new Date(b) : b
  
  return ascending 
    ? dateA.getTime() - dateB.getTime()
    : dateB.getTime() - dateA.getTime()
}
