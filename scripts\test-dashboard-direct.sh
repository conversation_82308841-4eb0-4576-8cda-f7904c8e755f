#!/bin/bash

# Script de test pour l'accès direct au dashboard
echo "🧪 Test Accès Direct Dashboard - Karma Com Solidarité"
echo "===================================================="

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
log "Vérification des prérequis..."

if [ ! -f "package.json" ]; then
    error "Ce script doit être exécuté depuis la racine du projet"
    exit 1
fi

if [ ! -f "src/app/dashboard/page.tsx" ]; then
    error "Fichier dashboard non trouvé"
    exit 1
fi

success "Prérequis validés"

# Vérification des modifications dans le dashboard
log "Vérification des modifications du dashboard..."

# Vérifier que useAuth n'est plus importé
if grep -q "import.*useAuth" src/app/dashboard/page.tsx; then
    warning "useAuth est encore importé dans le dashboard"
else
    success "useAuth supprimé du dashboard"
fi

# Vérifier que LoginForm n'est plus importé
if grep -q "import.*LoginForm" src/app/dashboard/page.tsx; then
    warning "LoginForm est encore importé dans le dashboard"
else
    success "LoginForm supprimé du dashboard"
fi

# Vérifier que isAuthenticated est défini comme true
if grep -q "isAuthenticated = true" src/app/dashboard/page.tsx; then
    success "isAuthenticated défini comme true (accès direct)"
else
    warning "isAuthenticated pourrait ne pas être configuré pour l'accès direct"
fi

# Vérifier qu'il n'y a pas de vérification d'authentification
if grep -q "if.*!isAuthenticated" src/app/dashboard/page.tsx; then
    warning "Vérification d'authentification encore présente"
else
    success "Vérifications d'authentification supprimées"
fi

# Test de compilation TypeScript
log "Test de compilation TypeScript..."

if npx tsc --noEmit --project . 2>/dev/null; then
    success "Compilation TypeScript réussie"
else
    warning "Erreurs de compilation TypeScript détectées"
    echo "Exécutez 'npx tsc --noEmit' pour voir les détails"
fi

# Test de build Next.js (si possible)
log "Test de build Next.js..."

export DATABASE_URL="***********************************/dummy"
export NODE_ENV="production"
export NEXT_TELEMETRY_DISABLED=1

if npm run build &>/dev/null; then
    success "Build Next.js réussi"
else
    warning "Build Next.js échoué (peut être dû à la base de données)"
fi

# Vérification des routes
log "Vérification des routes..."

if [ -f "src/app/dashboard/page.tsx" ]; then
    success "Route /dashboard disponible"
else
    error "Route /dashboard manquante"
fi

# Vérification des composants du dashboard
log "Vérification des composants du dashboard..."

dashboard_components=(
    "src/components/dashboard/Overview.tsx"
    "src/components/dashboard/CandidatesTab.tsx"
    "src/components/dashboard/InterviewsTab.tsx"
    "src/components/dashboard/SettingsTab.tsx"
)

for component in "${dashboard_components[@]}"; do
    if [ -f "$component" ]; then
        success "Composant $(basename $component) présent"
    else
        warning "Composant $(basename $component) manquant"
    fi
done

# Test de démarrage en mode développement (simulation)
log "Simulation du test de démarrage..."

if command -v npm &> /dev/null; then
    success "npm disponible pour démarrer l'application"
    echo "   Commande de test: npm run dev"
    echo "   URL de test: http://localhost:3000/dashboard"
else
    warning "npm non disponible"
fi

# Résumé des modifications
echo ""
log "Résumé des Modifications Dashboard"
echo "=================================="

echo ""
echo "✅ Modifications apportées:"
echo "   - Suppression de l'import useAuth"
echo "   - Suppression de l'import LoginForm"
echo "   - isAuthenticated défini comme true"
echo "   - authLoading défini comme false"
echo "   - Suppression des vérifications d'authentification"
echo "   - Suppression du bouton de déconnexion"
echo "   - Suppression des fonctions de login/logout"
echo ""
echo "🎯 Résultat:"
echo "   - Accès direct au dashboard sans authentification"
echo "   - Plus de formulaire de login"
echo "   - Plus de redirection vers la page de connexion"
echo ""
echo "🌐 URLs d'accès:"
echo "   - Local: http://localhost:3000/dashboard"
echo "   - Production: https://kcs.zidani.org/dashboard"
echo ""
echo "🧪 Tests recommandés:"
echo "   1. Démarrer l'application: npm run dev"
echo "   2. Accéder directement à: http://localhost:3000/dashboard"
echo "   3. Vérifier que le dashboard s'affiche sans login"
echo "   4. Tester toutes les fonctionnalités du dashboard"
echo ""
echo "🚀 Déploiement:"
echo "   npm run deploy:vps-no-sudo"

success "Test d'accès direct au dashboard terminé"
