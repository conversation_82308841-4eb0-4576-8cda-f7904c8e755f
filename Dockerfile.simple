# Dockerfile simplifié et robuste
FROM node:18-alpine

WORKDIR /app

# Installer les dépendances système
RUN apk add --no-cache libc6-compat curl

# Copier les fichiers de configuration
COPY package.json package-lock.json* ./
COPY tsconfig.json ./
COPY next.config.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY prisma ./prisma

# Nettoyer le cache npm et installer les dépendances
RUN npm cache clean --force
RUN npm ci --no-optional --legacy-peer-deps

# Copier le code source
COPY . .

# Générer Prisma client
RUN npx prisma generate

# Créer le dossier public s'il n'existe pas
RUN mkdir -p public

# Variables d'environnement pour le build
ENV NODE_ENV=production
ENV DATABASE_URL="***********************************/dummy"
ENV NEXT_TELEMETRY_DISABLED=1

# Build de l'application avec gestion d'erreur
RUN npm run build || (echo "Build failed, trying with legacy options..." && npm run build --legacy-peer-deps)

# Exposer le port
EXPOSE 3000

# Commande de démarrage
CMD ["npm", "start"]
