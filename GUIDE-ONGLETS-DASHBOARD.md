# 📊 Guide des Onglets Dashboard - Karma Com Solidarité

## 🎯 Vue d'ensemble

Le dashboard RH de Karma Com Solidarité dispose maintenant de 3 onglets principaux pour gérer efficacement les candidatures et entretiens.

## 📋 Onglets Disponibles

### 1. 🏠 Vue d'ensemble
- **Statistiques générales** : Total candidats, en attente, approuvés
- **Graphiques** : Répartition par type et statut
- **Candidatures récentes** : Dernières inscriptions
- **Entretiens à venir** : Prochains rendez-vous planifiés

### 2. 👥 Candidatures
- **Tableau interactif** avec toutes les candidatures
- **Filtrage** par statut (En attente, Approuvé, Rejeté)
- **Mise à jour des statuts** en temps réel
- **Actions rapides** : Planifier entretien, Voir détails
- **Export** des données

### 3. 📅 Entretiens
- **Liste des entretiens** planifiés
- **Formulaire de création** d'entretiens
- **Types d'entretiens** : Découverte, Suivi, Final
- **Gestion des statuts** : Programmé, Confirmé, Terminé

## 🛠️ Fonctionnalités Détaillées

### Gestion des Candidatures

#### Tableau des Candidatures
```
| Candidat          | Type        | Organisation    | Statut    | Date       | Actions              |
|-------------------|-------------|-----------------|-----------|------------|---------------------|
| Marie Dubois      | Association | Les Amis Nature | En attente| 15/01/2024| Planifier entretien |
| Jean Martin       | Bénévole    | -               | Approuvé  | 14/01/2024| Voir détails        |
```

#### Actions Disponibles
- **Changer le statut** : Dropdown pour modifier En attente → Approuvé/Rejeté
- **Planifier entretien** : Ouvre le formulaire de création d'entretien
- **Voir détails** : Affiche le profil complet du candidat
- **Exporter** : Télécharge les données en CSV/Excel

### Gestion des Entretiens

#### Formulaire de Création
- **Titre** : Nom de l'entretien (ex: "Entretien découverte - Marie Dubois")
- **Type** : 
  - 🔍 **Découverte** : Premier contact, présentation mutuelle
  - 📋 **Suivi** : Point d'étape, évaluation des compétences
  - ✅ **Final** : Décision finale d'intégration
- **Candidat** : Sélection depuis la liste des candidatures
- **Date et heure** : Planification précise
- **Description** : Objectifs et points à aborder

#### Statuts d'Entretiens
- 📅 **Programmé** : Entretien créé, en attente de confirmation
- ✅ **Confirmé** : Candidat a confirmé sa présence
- 🎯 **Terminé** : Entretien réalisé
- ❌ **Annulé** : Entretien annulé

## 🔄 Workflow Complet

### 1. Nouvelle Candidature
1. **Candidat s'inscrit** via les formulaires web
2. **Apparaît dans l'onglet Candidatures** avec statut "En attente"
3. **RH examine** le profil et les motivations

### 2. Évaluation
1. **RH change le statut** selon l'évaluation :
   - ✅ **Approuvé** : Candidature retenue
   - ❌ **Rejeté** : Candidature non retenue
   - 📅 **En attente** : Nécessite plus d'informations

### 3. Planification d'Entretien
1. **Clic sur "Planifier entretien"** depuis le tableau
2. **Formulaire pré-rempli** avec les infos du candidat
3. **Sélection du type** et de la date
4. **Création automatique** de l'entretien

### 4. Suivi d'Entretien
1. **Entretien apparaît** dans l'onglet Entretiens
2. **Modification possible** des détails
3. **Mise à jour du statut** après réalisation
4. **Notes et commentaires** pour le suivi

## 🎨 Interface Utilisateur

### Design Cohérent
- **Couleurs Karma Com** : Bleu (#1e3a5f) et Rose (#e91e63)
- **Icônes intuitives** : Heart (Associations), Building2 (Organisations), Users (Bénévoles)
- **Badges de statut** : Couleurs distinctives pour chaque état
- **Responsive design** : Adapté mobile, tablette, desktop

### Navigation
- **Onglets clairs** en haut du dashboard
- **Breadcrumb** pour se repérer
- **Actions contextuelles** selon l'onglet actif
- **Boutons d'action** bien visibles

## 📊 APIs Disponibles

### Entretiens
```bash
# Récupérer tous les entretiens
GET /api/interviews

# Créer un nouvel entretien
POST /api/interviews
{
  "title": "Entretien découverte",
  "description": "Premier contact avec le candidat",
  "candidateId": "user-id",
  "scheduledAt": "2024-01-20T14:00:00Z",
  "type": "DISCOVERY"
}
```

### Candidats
```bash
# Récupérer tous les candidats (avec pagination)
GET /api/candidates?page=1&limit=10&status=pending

# Mettre à jour le statut d'un candidat
PATCH /api/candidates/{id}/status
{
  "status": "approved"
}
```

## 🧪 Tests et Validation

### Scripts de Test
```bash
# Tester tous les onglets et APIs
npm run test:tabs

# Vérifier le contenu du dashboard
npm run check:dashboard

# Test complet du dashboard
npm run test:dashboard
```

### Validation Manuelle
1. **Accéder au dashboard** : http://localhost:3000/dashboard
2. **Naviguer entre les onglets** : Vue d'ensemble → Candidatures → Entretiens
3. **Tester les actions** : Changer statut, créer entretien
4. **Vérifier la persistance** : Recharger la page, données conservées

## 🚀 Utilisation en Production

### Prérequis
- ✅ Base de données PostgreSQL configurée
- ✅ Données de test ou vraies candidatures
- ✅ Compte administrateur RH créé
- ✅ Application démarrée

### Accès
- **URL** : http://localhost:3000/dashboard
- **Authentification** : Mode développement (pas de login requis)
- **Permissions** : Accès complet aux fonctionnalités RH

### Sécurité
- 🔒 **En production** : Activer l'authentification obligatoire
- 🛡️ **Permissions** : Restreindre l'accès aux utilisateurs HR_ADMIN
- 📝 **Logs** : Tracer toutes les modifications de statuts
- 🔐 **Validation** : Vérifier les données avant enregistrement

## 📈 Métriques et Suivi

### Statistiques Disponibles
- **Total candidatures** par période
- **Taux de conversion** : En attente → Approuvé
- **Délai moyen** entre candidature et entretien
- **Répartition** par type d'utilisateur

### Rapports
- **Export CSV** des candidatures
- **Planning** des entretiens
- **Tableau de bord** avec graphiques
- **Historique** des modifications

---

## 🎉 Résumé

Le dashboard Karma Com Solidarité offre maintenant une **interface complète** pour :
- ✅ **Gérer les candidatures** avec tableau interactif
- ✅ **Planifier les entretiens** avec formulaire intuitif
- ✅ **Suivre les statuts** en temps réel
- ✅ **Exporter les données** pour reporting
- ✅ **Interface responsive** et moderne

**Toutes les fonctionnalités sont opérationnelles** et prêtes pour la production ! 🚀
