# Pipeline GitLab CI/CD Minimal - Test
variables:
  VPS_IP: "*************"
  VPS_USER: "vpsadmin"
  DOMAIN: "kcs.zidani.org"

stages:
  - test
  - deploy

test:basic:
  stage: test
  image: node:18-alpine
  before_script:
    - apk add --no-cache bash
    - npm ci
  script:
    - echo "🧪 Test basique"
    - npm run lint || echo "Lint terminé"
    - echo "✅ Test terminé"
  allow_failure: true

deploy:manual:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache bash curl openssh-client nodejs npm
  script:
    - echo "🚀 Déploiement manuel"
    - echo "✅ Déploiement simulé"
  when: manual
  allow_failure: true
