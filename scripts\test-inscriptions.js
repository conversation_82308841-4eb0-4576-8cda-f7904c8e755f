// Script de test pour les inscriptions
// Ce script teste les API d'inscription avec des données valides

const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

// Données de test pour association
const testAssociationData = {
  email: '<EMAIL>',
  password: 'motdepasse123',
  confirmPassword: 'motdepasse123',
  firstName: 'Marie',
  lastName: 'Dubois',
  phone: '+33123456789',
  organizationName: 'Association Nouvelle Solidarité',
  siret: '12345678901234',
  website: 'https://nouvelle-solidarite.fr',
  description: 'Association dédiée à l\'aide aux personnes en difficulté et à la promotion de la solidarité locale.',
  address: '123 Rue de la Paix',
  city: 'Paris',
  postalCode: '75001',
  country: 'France',
  motivation: 'Nous souhaitons rejoindre Karma Com pour bénéficier de votre expertise et développer nos actions.',
  acceptTerms: true,
  acceptRGPD: true
}

// Données de test pour organisation
const testOrganizationData = {
  email: '<EMAIL>',
  password: 'motdepasse123',
  confirmPassword: 'motdepasse123',
  firstName: '<PERSON>',
  lastName: 'Martin',
  phone: '+33123456789',
  organizationName: 'InnovTech Solutions',
  organizationType: 'Entreprise privée',
  sector: 'Technologie',
  siret: '98765432109876',
  website: 'https://innovtech-solutions.com',
  employeeCount: '51-200',
  description: 'Entreprise spécialisée dans les solutions technologiques innovantes pour les associations.',
  address: '456 Avenue de l\'Innovation',
  city: 'Lyon',
  postalCode: '69001',
  country: 'France',
  partnershipType: 'Mécénat de compétences',
  budget: '15 000€ - 50 000€',
  motivation: 'Nous voulons contribuer au développement du secteur associatif grâce à nos compétences techniques.',
  acceptTerms: true,
  acceptRGPD: true
}

// Données de test pour bénévole
const testVolunteerData = {
  email: '<EMAIL>',
  password: 'motdepasse123',
  confirmPassword: 'motdepasse123',
  firstName: 'Sophie',
  lastName: 'Laurent',
  phone: '+33123456789',
  dateOfBirth: '1990-05-15',
  address: '789 Rue du Bénévolat',
  city: 'Marseille',
  postalCode: '13001',
  country: 'France',
  skills: ['Communication', 'Marketing digital', 'Gestion de projet'],
  customSkills: 'Animation d\'ateliers, formation',
  availability: 'Une demi-journée par semaine',
  experience: 'J\'ai déjà été bénévole dans une association locale pendant 2 ans.',
  motivation: 'Je souhaite mettre mes compétences au service de causes qui me tiennent à cœur et contribuer à un monde plus solidaire.',
  preferredDepartments: ['Communication', 'RH/Asso'],
  remoteWork: true,
  acceptTerms: true,
  acceptRGPD: true
}

async function testInscription(endpoint, data, type) {
  console.log(`\n🧪 Test d'inscription ${type}...`)
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/register/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    const result = await response.json()

    if (response.ok) {
      console.log(`✅ Inscription ${type} réussie!`)
      console.log(`   Email: ${data.email}`)
      console.log(`   ID utilisateur: ${result.user.id}`)
      console.log(`   Statut: ${result.user.profile?.membershipStatus || 'N/A'}`)
      return true
    } else {
      console.log(`❌ Échec inscription ${type}:`)
      console.log(`   Erreur: ${result.error}`)
      if (result.details) {
        console.log('   Détails:')
        result.details.forEach(detail => {
          console.log(`     - ${detail.path?.join('.')}: ${detail.message}`)
        })
      }
      return false
    }
  } catch (error) {
    console.log(`❌ Erreur réseau pour ${type}:`, error.message)
    return false
  }
}

async function testLogin(email, password) {
  console.log(`\n🔐 Test de connexion pour ${email}...`)
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    })

    const result = await response.json()

    if (response.ok) {
      console.log(`✅ Connexion réussie pour ${email}`)
      console.log(`   Type d'utilisateur: ${result.user.userType}`)
      return true
    } else {
      console.log(`❌ Échec connexion pour ${email}: ${result.error}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Erreur réseau pour la connexion:`, error.message)
    return false
  }
}

async function main() {
  console.log('🚀 Démarrage des tests d\'inscription')
  console.log('=====================================')
  
  // Vérifier que l'application est accessible
  try {
    const response = await fetch(`${BASE_URL}`)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }
    console.log('✅ Application accessible')
  } catch (error) {
    console.log('❌ Application non accessible:', error.message)
    console.log('💡 Assurez-vous que l\'application est démarrée avec "npm run dev"')
    process.exit(1)
  }

  let successCount = 0
  let totalTests = 0

  // Test inscription association
  totalTests++
  if (await testInscription('association', testAssociationData, 'Association')) {
    successCount++
  }

  // Test inscription organisation
  totalTests++
  if (await testInscription('organization', testOrganizationData, 'Organisation')) {
    successCount++
  }

  // Test inscription bénévole
  totalTests++
  if (await testInscription('volunteer', testVolunteerData, 'Bénévole')) {
    successCount++
  }

  // Test de connexion avec les comptes créés
  if (successCount > 0) {
    console.log('\n🔐 Tests de connexion...')
    
    if (await testLogin(testAssociationData.email, testAssociationData.password)) {
      successCount++
    }
    totalTests++
    
    if (await testLogin(testOrganizationData.email, testOrganizationData.password)) {
      successCount++
    }
    totalTests++
    
    if (await testLogin(testVolunteerData.email, testVolunteerData.password)) {
      successCount++
    }
    totalTests++
  }

  // Résumé
  console.log('\n📊 Résumé des tests')
  console.log('===================')
  console.log(`✅ Tests réussis: ${successCount}/${totalTests}`)
  console.log(`❌ Tests échoués: ${totalTests - successCount}/${totalTests}`)
  
  if (successCount === totalTests) {
    console.log('\n🎉 Tous les tests sont passés avec succès!')
    console.log('\n💡 Vous pouvez maintenant:')
    console.log('   • Accéder au dashboard: http://localhost:3000/dashboard')
    console.log('   • Tester les formulaires: http://localhost:3000')
    console.log('   • Voir la base de données: npm run db:studio')
  } else {
    console.log('\n⚠️ Certains tests ont échoué. Vérifiez:')
    console.log('   • La base de données est-elle démarrée?')
    console.log('   • Les migrations sont-elles appliquées?')
    console.log('   • L\'application est-elle démarrée?')
    process.exit(1)
  }
}

// Vérifier si node-fetch est disponible
try {
  require('node-fetch')
} catch (error) {
  console.log('❌ node-fetch n\'est pas installé')
  console.log('💡 Installez-le avec: npm install node-fetch')
  process.exit(1)
}

main()
