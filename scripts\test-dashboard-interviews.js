#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testDashboardInterviews() {
  console.log('🧪 Test Dashboard Entretiens - Karma Com Solidarité')
  console.log('='.repeat(60))

  try {
    // Test 1: Créer des données de test
    console.log('\n1. Création de données de test...')
    
    // Créer un bénévole
    const testVolunteer = await prisma.volunteerApplication.create({
      data: {
        firstName: 'Sophie',
        lastName: 'Dashboard',
        email: '<EMAIL>',
        phoneNumber: '06 12 34 56 78',
        currentStatus: 'En poste',
        contributionPole: 'Communication et Stratégie Digitale',
        weeklyHours: 'Entre 5 et 10 heures par semaine',
        availability: 'Soirée (18h à 22h)',
        participationRhythm: 'Régulier (chaque semaine)',
        howDidYouKnowUs: 'Site internet de Karma Com Solidarité',
        status: 'PENDING'
      }
    })
    console.log('✅ Bénévole créé:', testVolunteer.id)

    // Créer un candidat classique
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Pierre Dashboard',
        phone: '06 98 76 54 32',
        password: 'test123',
        userType: 'VOLUNTEER',
        profile: {
          create: {
            firstName: 'Pierre',
            lastName: 'Dashboard',
            membershipStatus: 'PENDING'
          }
        }
      }
    })
    console.log('✅ Candidat classique créé:', testUser.id)

    // Créer un admin
    let adminUser = await prisma.user.findFirst({
      where: { userType: 'HR_ADMIN' }
    })

    if (!adminUser) {
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin Dashboard',
          password: 'admin123',
          userType: 'HR_ADMIN',
          profile: {
            create: {
              firstName: 'Admin',
              lastName: 'Dashboard',
              membershipStatus: 'ACTIVE'
            }
          }
        }
      })
      console.log('✅ Admin créé:', adminUser.id)
    } else {
      console.log('✅ Admin existant utilisé:', adminUser.id)
    }

    // Test 2: Créer des entretiens
    console.log('\n2. Création d\'entretiens de test...')

    // Créer un utilisateur temporaire pour le bénévole
    const volunteerUser = await prisma.user.create({
      data: {
        email: testVolunteer.email,
        name: `${testVolunteer.firstName} ${testVolunteer.lastName}`,
        phone: testVolunteer.phoneNumber,
        password: 'temp_password',
        userType: 'VOLUNTEER',
        profile: {
          create: {
            firstName: testVolunteer.firstName,
            lastName: testVolunteer.lastName,
            membershipStatus: 'PENDING'
          }
        }
      }
    })

    // Entretien pour bénévole
    const volunteerInterview = await prisma.appointment.create({
      data: {
        title: 'Entretien Découverte - Sophie Dashboard',
        description: 'Entretien de découverte pour candidature bénévole',
        candidateId: volunteerUser.id,
        scheduledAt: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // Dans 2 jours
        type: 'DISCOVERY',
        status: 'SCHEDULED',
        createdById: adminUser.id
      }
    })
    console.log('✅ Entretien bénévole créé:', volunteerInterview.id)

    // Entretien pour candidat classique
    const classicInterview = await prisma.appointment.create({
      data: {
        title: 'Entretien Technique - Pierre Dashboard',
        description: 'Entretien technique pour candidature',
        candidateId: testUser.id,
        scheduledAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // Dans 3 jours
        type: 'INTERVIEW',
        status: 'SCHEDULED',
        createdById: adminUser.id
      }
    })
    console.log('✅ Entretien classique créé:', classicInterview.id)

    // Mettre à jour le statut du bénévole
    await prisma.volunteerApplication.update({
      where: { id: testVolunteer.id },
      data: { status: 'INTERVIEW_SCHEDULED' }
    })

    // Test 3: Vérifier les données pour le dashboard
    console.log('\n3. Vérification des données pour le dashboard...')

    // Récupérer les entretiens
    const interviews = await prisma.appointment.findMany({
      include: {
        candidate: {
          include: {
            profile: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        scheduledAt: 'asc'
      }
    })

    console.log(`✅ ${interviews.length} entretien(s) trouvé(s)`)
    interviews.forEach(interview => {
      console.log(`   - ${interview.title} (${interview.candidate.name}) - ${interview.scheduledAt.toLocaleDateString('fr-FR')}`)
    })

    // Récupérer les bénévoles
    const volunteers = await prisma.volunteerApplication.findMany({
      orderBy: { createdAt: 'desc' }
    })

    console.log(`✅ ${volunteers.length} bénévole(s) trouvé(s)`)
    volunteers.forEach(volunteer => {
      console.log(`   - ${volunteer.firstName} ${volunteer.lastName} (${volunteer.status})`)
    })

    // Test 4: Simuler l'API dashboard/stats
    console.log('\n4. Test API dashboard/stats...')

    const totalVolunteerApplications = await prisma.volunteerApplication.count()
    const pendingVolunteerApplications = await prisma.volunteerApplication.count({
      where: { status: 'PENDING' }
    })

    console.log(`✅ Total bénévoles: ${totalVolunteerApplications}`)
    console.log(`✅ Bénévoles en attente: ${pendingVolunteerApplications}`)

    // Test 5: Nettoyage
    console.log('\n5. Nettoyage des données de test...')

    // Supprimer les entretiens
    await prisma.appointment.deleteMany({
      where: {
        candidate: {
          email: {
            in: ['<EMAIL>', '<EMAIL>']
          }
        }
      }
    })

    // Supprimer les utilisateurs
    await prisma.user.deleteMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
      }
    })

    // Supprimer le bénévole
    await prisma.volunteerApplication.deleteMany({
      where: {
        email: '<EMAIL>'
      }
    })

    console.log('✅ Données de test supprimées')

    console.log('\n🎉 Test dashboard entretiens réussi!')
    console.log('\n📋 Résumé:')
    console.log('   - Création bénévole: ✅ OK')
    console.log('   - Création candidat classique: ✅ OK')
    console.log('   - Création entretiens: ✅ OK')
    console.log('   - Récupération données dashboard: ✅ OK')
    console.log('   - API stats: ✅ OK')
    console.log('\n🚀 Le dashboard devrait maintenant afficher correctement les entretiens!')

  } catch (error) {
    console.error('❌ Erreur lors du test dashboard:', error)
    console.error('Stack:', error.stack)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Exécuter le test
testDashboardInterviews()
