/** @type {import('next').NextConfig} */
const nextConfig = {
  // Active la génération d'une app autonome pour Docker (créera server.js dans .next/standalone)
  output: 'standalone',

  // Paramètres pour l'import d'images externes (ex : depuis localhost pendant le dev)
  images: {
    domains: ['localhost'],
  },

  // Fonctions expérimentales (décommente si tu veux activer appDir, etc.)
  experimental: {
    // appDir: true,
  },
};

module.exports = nextConfig;
