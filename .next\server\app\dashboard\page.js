/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDaGFtemEuYmVkb3VpJTVDRG9jdW1lbnRzJTVDbWVzRG9jcyU1Q0FJJTVDS0NTJTVDYXVnbWVudC1rY3MlNUNzcmMlNUNhcHAlNUNkYXNoYm9hcmQlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rYXJtYS1jb20tZGFzaGJvYXJkLz9mODBkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaGFtemEuYmVkb3VpXFxcXERvY3VtZW50c1xcXFxtZXNEb2NzXFxcXEFJXFxcXEtDU1xcXFxhdWdtZW50LWtjc1xcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Logo */ \"(ssr)/./src/components/ui/Logo.tsx\");\n/* harmony import */ var _components_dashboard_LoginForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/LoginForm */ \"(ssr)/./src/components/dashboard/LoginForm.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Building2,Calendar,CheckCircle,Clock,Heart,LogOut,Plus,Settings,TrendingUp,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction DashboardPage() {\n    const { isAuthenticated, loading: authLoading, login, logout, getAuthHeaders } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loginLoading, setLoginLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fonction de connexion\n    const handleLogin = async (email, password)=>{\n        setLoginLoading(true);\n        setLoginError(null);\n        try {\n            const success = await login(email, password);\n            if (!success) {\n                setLoginError(\"Email ou mot de passe incorrect\");\n            }\n            return success;\n        } catch (error) {\n            setLoginError(\"Erreur lors de la connexion\");\n            return false;\n        } finally{\n            setLoginLoading(false);\n        }\n    };\n    // Fonction de déconnexion\n    const handleLogout = ()=>{\n        logout();\n    };\n    // Charger les données du dashboard\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated || authLoading) {\n            return;\n        }\n        const fetchDashboardData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/dashboard/stats\", {\n                    headers: getAuthHeaders()\n                });\n                if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n                const data = await response.json();\n                setDashboardData(data);\n            } catch (err) {\n                console.error(\"Erreur lors du chargement des donn\\xe9es:\", err);\n                setError(err instanceof Error ? err.message : \"Erreur inconnue\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchDashboardData();\n    }, [\n        isAuthenticated,\n        authLoading,\n        getAuthHeaders\n    ]);\n    // Données par défaut si pas encore chargées\n    const stats = dashboardData?.overview || {\n        totalCandidates: 0,\n        pendingApplications: 0,\n        scheduledInterviews: 0,\n        approvedMembers: 0\n    };\n    // Données des candidatures récentes depuis l'API\n    const recentApplications = dashboardData?.recentApplications || [];\n    // Données des entretiens à venir (simulées pour l'instant)\n    const upcomingInterviews = [\n        {\n            id: 1,\n            candidateName: \"Sophie Laurent\",\n            type: \"D\\xe9couverte\",\n            date: \"2024-01-16\",\n            time: \"14:00\",\n            status: \"confirmed\"\n        },\n        {\n            id: 2,\n            candidateName: \"Marc Rousseau\",\n            type: \"Int\\xe9gration\",\n            date: \"2024-01-16\",\n            time: \"16:30\",\n            status: \"pending\"\n        },\n        {\n            id: 3,\n            candidateName: \"Alice Bernard\",\n            type: \"Suivi\",\n            date: \"2024-01-17\",\n            time: \"10:00\",\n            status: \"confirmed\"\n        }\n    ];\n    const getStatusBadge = (status)=>{\n        const statusConfig = {\n            pending: {\n                label: \"En attente\",\n                class: \"status-pending\"\n            },\n            approved: {\n                label: \"Approuv\\xe9\",\n                class: \"status-approved\"\n            },\n            rejected: {\n                label: \"Rejet\\xe9\",\n                class: \"status-rejected\"\n            },\n            interview: {\n                label: \"Entretien\",\n                class: \"status-active\"\n            },\n            active: {\n                label: \"Actif\",\n                class: \"status-approved\"\n            },\n            confirmed: {\n                label: \"Confirm\\xe9\",\n                class: \"status-approved\"\n            }\n        };\n        const config = statusConfig[status] || {\n            label: status,\n            class: \"status-pending\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: `status-badge ${config.class}`,\n            children: config.label\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 155,\n            columnNumber: 12\n        }, this);\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case \"Association\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    size: 16,\n                    className: \"text-primary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 16\n                }, this);\n            case \"Organisation\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 16,\n                    className: \"text-secondary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 16\n                }, this);\n            case \"B\\xe9n\\xe9vole\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: 16,\n                    className: \"text-accent-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: 16,\n                    className: \"text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // Afficher le formulaire de login si pas authentifié\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"V\\xe9rification de l'authentification...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_LoginForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            onLogin: handleLogin,\n            loading: loginLoading,\n            error: loginError || undefined\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            size: \"md\",\n                                            showText: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Dashboard RH\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: \"RH\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden md:block text-sm font-medium text-gray-700\",\n                                                children: \"Admin RH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"p-2 text-gray-400 hover:text-red-600 transition-colors duration-200\",\n                                        title: \"Se d\\xe9connecter\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-gray-600\",\n                                children: \"Chargement des donn\\xe9es...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"text-red-400 mr-3\",\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-red-800 font-medium\",\n                                            children: \"Erreur de chargement\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 text-sm mt-1\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.reload(),\n                                            className: \"text-red-600 hover:text-red-800 text-sm underline mt-2\",\n                                            children: \"R\\xe9essayer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8\",\n                                    children: [\n                                        {\n                                            id: \"overview\",\n                                            label: \"Vue d'ensemble\",\n                                            icon: _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                        },\n                                        {\n                                            id: \"applications\",\n                                            label: \"Candidatures\",\n                                            icon: _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                        },\n                                        {\n                                            id: \"interviews\",\n                                            label: \"Entretiens\",\n                                            icon: _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                        },\n                                        {\n                                            id: \"members\",\n                                            label: \"Membres\",\n                                            icon: _barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: `flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${activeTab === tab.id ? \"bg-primary-100 text-primary-700 border-b-2 border-primary-600\" : \"text-gray-600 hover:text-primary-600 hover:bg-gray-100\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: tab.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 9\n                            }, this),\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Total Candidats\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                                    children: stats.totalCandidates\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"text-primary-600\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"En attente\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-yellow-600\",\n                                                                    children: stats.pendingApplications\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"text-yellow-600\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Entretiens pr\\xe9vus\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-blue-600\",\n                                                                    children: stats.scheduledInterviews\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"text-blue-600\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"stat-card\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Membres actifs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-green-600\",\n                                                                    children: stats.activeMembers\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"text-green-600\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"dashboard-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: \"Candidatures r\\xe9centes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/dashboard/applications\",\n                                                        className: \"text-primary-600 hover:text-primary-700 font-medium text-sm\",\n                                                        children: \"Voir tout\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"min-w-full divide-y divide-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Candidat\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Organisation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Statut\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"bg-white divide-y divide-gray-200\",\n                                                            children: recentApplications.map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: application.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 381,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: application.email\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 382,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 380,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    getTypeIcon(application.type),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-gray-900\",\n                                                                                        children: application.type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 388,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 386,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                            children: application.organization || \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 391,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                                            children: getStatusBadge(application.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: new Date(application.date).toLocaleDateString(\"fr-FR\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, application.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"dashboard-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: \"Entretiens \\xe0 venir\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"karma-button-primary flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Planifier\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: upcomingInterviews.map((interview)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"text-primary-600\",\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: interview.candidateName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    interview.type,\n                                                                                    \" • \",\n                                                                                    new Date(interview.date).toLocaleDateString(\"fr-FR\"),\n                                                                                    \" \\xe0 \",\n                                                                                    interview.time\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    getStatusBadge(interview.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-primary-600 hover:text-primary-700 font-medium text-sm\",\n                                                                        children: \"D\\xe9tails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, interview.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            activeTab !== \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dashboard-card text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Building2_Calendar_CheckCircle_Clock_Heart_LogOut_Plus_Settings_TrendingUp_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"text-gray-400\",\n                                            size: 32\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Section en d\\xe9veloppement\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Cette section sera bient\\xf4t disponible.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/LoginForm.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/LoginForm.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Logo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Logo */ \"(ssr)/./src/components/ui/Logo.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LoginForm({ onLogin, loading = false, error }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when user starts typing\n        if (formErrors[name]) {\n            setFormErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.email) {\n            newErrors.email = \"L'email est requis\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Format d'email invalide\";\n        }\n        if (!formData.password) {\n            newErrors.password = \"Le mot de passe est requis\";\n        }\n        setFormErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        await onLogin(formData.email, formData.password);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                size: \"lg\",\n                                showText: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Dashboard RH\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Connectez-vous pour acc\\xe9der au tableau de bord\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"karma-card p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 text-sm\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"karma-label\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"inline mr-2\",\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Adresse email\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: formData.email,\n                                            onChange: handleInputChange,\n                                            className: `karma-input ${formErrors.email ? \"border-red-500\" : \"\"}`,\n                                            placeholder: \"<EMAIL>\",\n                                            disabled: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        formErrors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-sm mt-1\",\n                                            children: formErrors.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"karma-label\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"inline mr-2\",\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Mot de passe\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    name: \"password\",\n                                                    value: formData.password,\n                                                    onChange: handleInputChange,\n                                                    className: `karma-input pr-10 ${formErrors.password ? \"border-red-500\" : \"\"}`,\n                                                    placeholder: \"Votre mot de passe\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\",\n                                                    disabled: loading,\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 35\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        formErrors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-sm mt-1\",\n                                            children: formErrors.password\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 39\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: `w-full karma-button-primary text-lg py-3 ${loading ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                                    children: loading ? \"Connexion en cours...\" : \"Se connecter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                        children: \"Comptes de test :\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Admin RH :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" <EMAIL> / admin123\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Fake data :\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" [email] / password123\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\dashboard\\\\LoginForm.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvTG9naW5Gb3JtLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUV1QztBQUNBO0FBQ2U7QUFRdkMsU0FBU08sVUFBVSxFQUFFQyxPQUFPLEVBQUVDLFVBQVUsS0FBSyxFQUFFQyxLQUFLLEVBQWtCO0lBQ25GLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHWCwrQ0FBUUEsQ0FBQztRQUN2Q1ksT0FBTztRQUNQQyxVQUFVO0lBQ1o7SUFDQSxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHZiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNnQixZQUFZQyxjQUFjLEdBQUdqQiwrQ0FBUUEsQ0FBeUIsQ0FBQztJQUV0RSxNQUFNa0Isb0JBQW9CLENBQUNDO1FBQ3pCLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBR0YsRUFBRUcsTUFBTTtRQUNoQ1gsWUFBWVksQ0FBQUEsT0FBUztnQkFDbkIsR0FBR0EsSUFBSTtnQkFDUCxDQUFDSCxLQUFLLEVBQUVDO1lBQ1Y7UUFFQSxzQ0FBc0M7UUFDdEMsSUFBSUwsVUFBVSxDQUFDSSxLQUFLLEVBQUU7WUFDcEJILGNBQWNNLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRSxDQUFDSCxLQUFLLEVBQUU7Z0JBQUc7UUFDL0M7SUFDRjtJQUVBLE1BQU1JLGVBQWU7UUFDbkIsTUFBTUMsWUFBb0MsQ0FBQztRQUUzQyxJQUFJLENBQUNmLFNBQVNFLEtBQUssRUFBRTtZQUNuQmEsVUFBVWIsS0FBSyxHQUFHO1FBQ3BCLE9BQU8sSUFBSSxDQUFDLGVBQWVjLElBQUksQ0FBQ2hCLFNBQVNFLEtBQUssR0FBRztZQUMvQ2EsVUFBVWIsS0FBSyxHQUFHO1FBQ3BCO1FBRUEsSUFBSSxDQUFDRixTQUFTRyxRQUFRLEVBQUU7WUFDdEJZLFVBQVVaLFFBQVEsR0FBRztRQUN2QjtRQUVBSSxjQUFjUTtRQUNkLE9BQU9FLE9BQU9DLElBQUksQ0FBQ0gsV0FBV0ksTUFBTSxLQUFLO0lBQzNDO0lBRUEsTUFBTUMsZUFBZSxPQUFPWDtRQUMxQkEsRUFBRVksY0FBYztRQUVoQixJQUFJLENBQUNQLGdCQUFnQjtZQUNuQjtRQUNGO1FBRUEsTUFBTWpCLFFBQVFHLFNBQVNFLEtBQUssRUFBRUYsU0FBU0csUUFBUTtJQUNqRDtJQUVBLHFCQUNFLDhEQUFDbUI7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNoQywyREFBSUE7Z0NBQUNpQyxNQUFLO2dDQUFLQyxVQUFVOzs7Ozs7Ozs7OztzQ0FFNUIsOERBQUNDOzRCQUFHSCxXQUFVO3NDQUF3Qzs7Ozs7O3NDQUd0RCw4REFBQ0k7NEJBQUVKLFdBQVU7c0NBQWdCOzs7Ozs7Ozs7Ozs7OEJBTS9CLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNLOzRCQUFLQyxVQUFVVDs0QkFBY0csV0FBVTs7Z0NBRXJDeEIsdUJBQ0MsOERBQUN1QjtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0k7d0NBQUVKLFdBQVU7a0RBQXdCeEI7Ozs7Ozs7Ozs7OzhDQUt6Qyw4REFBQ3VCOztzREFDQyw4REFBQ1E7NENBQU1QLFdBQVU7OzhEQUNmLDhEQUFDL0IsZ0dBQUlBO29EQUFDK0IsV0FBVTtvREFBY0MsTUFBTTs7Ozs7O2dEQUFNOzs7Ozs7O3NEQUc1Qyw4REFBQ087NENBQ0NDLE1BQUs7NENBQ0x0QixNQUFLOzRDQUNMQyxPQUFPWCxTQUFTRSxLQUFLOzRDQUNyQitCLFVBQVV6Qjs0Q0FDVmUsV0FBVyxDQUFDLFlBQVksRUFBRWpCLFdBQVdKLEtBQUssR0FBRyxtQkFBbUIsR0FBRyxDQUFDOzRDQUNwRWdDLGFBQVk7NENBQ1pDLFVBQVVyQzs7Ozs7O3dDQUVYUSxXQUFXSixLQUFLLGtCQUFJLDhEQUFDeUI7NENBQUVKLFdBQVU7c0RBQTZCakIsV0FBV0osS0FBSzs7Ozs7Ozs7Ozs7OzhDQUlqRiw4REFBQ29COztzREFDQyw4REFBQ1E7NENBQU1QLFdBQVU7OzhEQUNmLDhEQUFDOUIsZ0dBQUlBO29EQUFDOEIsV0FBVTtvREFBY0MsTUFBTTs7Ozs7O2dEQUFNOzs7Ozs7O3NEQUc1Qyw4REFBQ0Y7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDUTtvREFDQ0MsTUFBTTVCLGVBQWUsU0FBUztvREFDOUJNLE1BQUs7b0RBQ0xDLE9BQU9YLFNBQVNHLFFBQVE7b0RBQ3hCOEIsVUFBVXpCO29EQUNWZSxXQUFXLENBQUMsa0JBQWtCLEVBQUVqQixXQUFXSCxRQUFRLEdBQUcsbUJBQW1CLEdBQUcsQ0FBQztvREFDN0UrQixhQUFZO29EQUNaQyxVQUFVckM7Ozs7Ozs4REFFWiw4REFBQ3NDO29EQUNDSixNQUFLO29EQUNMSyxTQUFTLElBQU1oQyxnQkFBZ0IsQ0FBQ0Q7b0RBQ2hDbUIsV0FBVTtvREFDVlksVUFBVXJDOzhEQUVUTSw2QkFBZSw4REFBQ1QsZ0dBQU1BO3dEQUFDNkIsTUFBTTs7Ozs7NkVBQVMsOERBQUM5QixnR0FBR0E7d0RBQUM4QixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FHckRsQixXQUFXSCxRQUFRLGtCQUFJLDhEQUFDd0I7NENBQUVKLFdBQVU7c0RBQTZCakIsV0FBV0gsUUFBUTs7Ozs7Ozs7Ozs7OzhDQUl2Riw4REFBQ2lDO29DQUNDSixNQUFLO29DQUNMRyxVQUFVckM7b0NBQ1Z5QixXQUFXLENBQUMseUNBQXlDLEVBQ25EekIsVUFBVSxrQ0FBa0MsR0FDN0MsQ0FBQzs4Q0FFREEsVUFBVSwwQkFBMEI7Ozs7Ozs7Ozs7OztzQ0FLekMsOERBQUN3Qjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDZTt3Q0FBR2YsV0FBVTtrREFBeUM7Ozs7OztrREFDdkQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0k7O2tFQUFFLDhEQUFDWTtrRUFBTzs7Ozs7O29EQUFtQjs7Ozs7OzswREFDOUIsOERBQUNaOztrRUFBRSw4REFBQ1k7a0VBQU87Ozs7OztvREFBb0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUS9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2FybWEtY29tLWRhc2hib2FyZC8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9Mb2dpbkZvcm0udHN4P2I0NDUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IExvZ28gZnJvbSAnQC9jb21wb25lbnRzL3VpL0xvZ28nXG5pbXBvcnQgeyBVc2VyLCBMb2NrLCBFeWUsIEV5ZU9mZiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIExvZ2luRm9ybVByb3BzIHtcbiAgb25Mb2dpbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IFByb21pc2U8Ym9vbGVhbj5cbiAgbG9hZGluZz86IGJvb2xlYW5cbiAgZXJyb3I/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9naW5Gb3JtKHsgb25Mb2dpbiwgbG9hZGluZyA9IGZhbHNlLCBlcnJvciB9OiBMb2dpbkZvcm1Qcm9wcykge1xuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBlbWFpbDogJycsXG4gICAgcGFzc3dvcmQ6ICcnXG4gIH0pXG4gIGNvbnN0IFtzaG93UGFzc3dvcmQsIHNldFNob3dQYXNzd29yZF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2Zvcm1FcnJvcnMsIHNldEZvcm1FcnJvcnNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgc3RyaW5nPj4oe30pXG5cbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCB7IG5hbWUsIHZhbHVlIH0gPSBlLnRhcmdldFxuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbbmFtZV06IHZhbHVlXG4gICAgfSkpXG4gICAgXG4gICAgLy8gQ2xlYXIgZXJyb3Igd2hlbiB1c2VyIHN0YXJ0cyB0eXBpbmdcbiAgICBpZiAoZm9ybUVycm9yc1tuYW1lXSkge1xuICAgICAgc2V0Rm9ybUVycm9ycyhwcmV2ID0+ICh7IC4uLnByZXYsIFtuYW1lXTogJycgfSkpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdmFsaWRhdGVGb3JtID0gKCkgPT4ge1xuICAgIGNvbnN0IG5ld0Vycm9yczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHt9XG5cbiAgICBpZiAoIWZvcm1EYXRhLmVtYWlsKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAnTFxcJ2VtYWlsIGVzdCByZXF1aXMnXG4gICAgfSBlbHNlIGlmICghL1xcUytAXFxTK1xcLlxcUysvLnRlc3QoZm9ybURhdGEuZW1haWwpKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAnRm9ybWF0IGRcXCdlbWFpbCBpbnZhbGlkZSdcbiAgICB9XG5cbiAgICBpZiAoIWZvcm1EYXRhLnBhc3N3b3JkKSB7XG4gICAgICBuZXdFcnJvcnMucGFzc3dvcmQgPSAnTGUgbW90IGRlIHBhc3NlIGVzdCByZXF1aXMnXG4gICAgfVxuXG4gICAgc2V0Rm9ybUVycm9ycyhuZXdFcnJvcnMpXG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoID09PSAwXG4gIH1cblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgXG4gICAgaWYgKCF2YWxpZGF0ZUZvcm0oKSkge1xuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgYXdhaXQgb25Mb2dpbihmb3JtRGF0YS5lbWFpbCwgZm9ybURhdGEucGFzc3dvcmQpXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktMTIgcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctbWQgdy1mdWxsIHNwYWNlLXktOFwiPlxuICAgICAgICB7LyogRW4tdMOqdGUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgPExvZ28gc2l6ZT1cImxnXCIgc2hvd1RleHQ9e3RydWV9IC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgIERhc2hib2FyZCBSSFxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgQ29ubmVjdGV6LXZvdXMgcG91ciBhY2PDqWRlciBhdSB0YWJsZWF1IGRlIGJvcmRcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBGb3JtdWxhaXJlICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImthcm1hLWNhcmQgcC04XCI+XG4gICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICB7LyogRXJyZXVyIGfDqW7DqXJhbGUgKi99XG4gICAgICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgdGV4dC1zbVwiPntlcnJvcn08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIEVtYWlsICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImthcm1hLWxhYmVsXCI+XG4gICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaW5saW5lIG1yLTJcIiBzaXplPXsxNn0gLz5cbiAgICAgICAgICAgICAgICBBZHJlc3NlIGVtYWlsXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgbmFtZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZW1haWx9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGthcm1hLWlucHV0ICR7Zm9ybUVycm9ycy5lbWFpbCA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiYWRtaW5Aa2FybWFjb20uZnJcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICB7Zm9ybUVycm9ycy5lbWFpbCAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC1zbSBtdC0xXCI+e2Zvcm1FcnJvcnMuZW1haWx9PC9wPn1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTW90IGRlIHBhc3NlICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImthcm1hLWxhYmVsXCI+XG4gICAgICAgICAgICAgICAgPExvY2sgY2xhc3NOYW1lPVwiaW5saW5lIG1yLTJcIiBzaXplPXsxNn0gLz5cbiAgICAgICAgICAgICAgICBNb3QgZGUgcGFzc2VcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd1Bhc3N3b3JkID8gJ3RleHQnIDogJ3Bhc3N3b3JkJ31cbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGFzc3dvcmR9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BrYXJtYS1pbnB1dCBwci0xMCAke2Zvcm1FcnJvcnMucGFzc3dvcmQgPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVm90cmUgbW90IGRlIHBhc3NlXCJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93UGFzc3dvcmQoIXNob3dQYXNzd29yZCl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC15LTAgcmlnaHQtMCBwci0zIGZsZXggaXRlbXMtY2VudGVyIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMFwiXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7c2hvd1Bhc3N3b3JkID8gPEV5ZU9mZiBzaXplPXsyMH0gLz4gOiA8RXllIHNpemU9ezIwfSAvPn1cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIHtmb3JtRXJyb3JzLnBhc3N3b3JkICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtIG10LTFcIj57Zm9ybUVycm9ycy5wYXNzd29yZH08L3A+fVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBCb3V0b24gZGUgY29ubmV4aW9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBrYXJtYS1idXR0b24tcHJpbWFyeSB0ZXh0LWxnIHB5LTMgJHtcbiAgICAgICAgICAgICAgICBsb2FkaW5nID8gJ29wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkJyA6ICcnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7bG9hZGluZyA/ICdDb25uZXhpb24gZW4gY291cnMuLi4nIDogJ1NlIGNvbm5lY3Rlcid9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Zvcm0+XG5cbiAgICAgICAgICB7LyogQWlkZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcHQtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+Q29tcHRlcyBkZSB0ZXN0IDo8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICA8cD48c3Ryb25nPkFkbWluIFJIIDo8L3N0cm9uZz4gYWRtaW5Aa2FybWFjb20uZnIgLyBhZG1pbjEyMzwvcD5cbiAgICAgICAgICAgICAgICA8cD48c3Ryb25nPkZha2UgZGF0YSA6PC9zdHJvbmc+IFtlbWFpbF0gLyBwYXNzd29yZDEyMzwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJMb2dvIiwiVXNlciIsIkxvY2siLCJFeWUiLCJFeWVPZmYiLCJMb2dpbkZvcm0iLCJvbkxvZ2luIiwibG9hZGluZyIsImVycm9yIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsImVtYWlsIiwicGFzc3dvcmQiLCJzaG93UGFzc3dvcmQiLCJzZXRTaG93UGFzc3dvcmQiLCJmb3JtRXJyb3JzIiwic2V0Rm9ybUVycm9ycyIsImhhbmRsZUlucHV0Q2hhbmdlIiwiZSIsIm5hbWUiLCJ2YWx1ZSIsInRhcmdldCIsInByZXYiLCJ2YWxpZGF0ZUZvcm0iLCJuZXdFcnJvcnMiLCJ0ZXN0IiwiT2JqZWN0Iiwia2V5cyIsImxlbmd0aCIsImhhbmRsZVN1Ym1pdCIsInByZXZlbnREZWZhdWx0IiwiZGl2IiwiY2xhc3NOYW1lIiwic2l6ZSIsInNob3dUZXh0IiwiaDIiLCJwIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJpbnB1dCIsInR5cGUiLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwiZGlzYWJsZWQiLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiLCJzdHJvbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Logo.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Logo.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Logo = ({ size = \"md\", showText = true, className = \"\" })=>{\n    const sizeClasses = {\n        sm: \"h-8 w-8\",\n        md: \"h-12 w-12\",\n        lg: \"h-16 w-16\",\n        xl: \"h-24 w-24\"\n    };\n    const textSizeClasses = {\n        sm: \"text-lg\",\n        md: \"text-xl\",\n        lg: \"text-2xl\",\n        xl: \"text-3xl\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center space-x-3 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${sizeClasses[size]} relative`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    viewBox: \"0 0 100 100\",\n                    className: \"w-full h-full\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"logoGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"100%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#0ea5e9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#ec4899\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                            x: \"5\",\n                            y: \"5\",\n                            width: \"90\",\n                            height: \"90\",\n                            rx: \"20\",\n                            ry: \"20\",\n                            fill: \"#f8fafc\",\n                            stroke: \"#e2e8f0\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M25 20 L25 80 L35 80 L35 55 L50 70 L65 70 L45 50 L65 30 L50 30 L35 45 L35 20 Z\",\n                            fill: \"url(#logoGradient)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"70\",\n                            cy: \"30\",\n                            r: \"8\",\n                            fill: \"#ec4899\",\n                            opacity: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `font-bold text-primary-700 ${textSizeClasses[size]} leading-tight`,\n                        children: \"Karma Com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `font-medium text-secondary-600 ${size === \"sm\" ? \"text-xs\" : size === \"md\" ? \"text-sm\" : \"text-base\"} leading-tight`,\n                        children: \"Solidarit\\xe9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Logo);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useAuth() {\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: true\n    });\n    // Vérifier l'authentification au chargement\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const token = localStorage.getItem(\"auth-token\");\n        const userStr = localStorage.getItem(\"user\");\n        if (token && userStr) {\n            try {\n                const user = JSON.parse(userStr);\n                setAuthState({\n                    user,\n                    token,\n                    isAuthenticated: true,\n                    loading: false\n                });\n            } catch (error) {\n                console.error(\"Erreur lors de la lecture des donn\\xe9es utilisateur:\", error);\n                logout();\n            }\n        } else {\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: false\n                }));\n        }\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Stocker les données d'authentification\n                localStorage.setItem(\"auth-token\", data.token);\n                localStorage.setItem(\"user\", JSON.stringify(data.user));\n                setAuthState({\n                    user: data.user,\n                    token: data.token,\n                    isAuthenticated: true,\n                    loading: false\n                });\n                return true;\n            } else {\n                console.error(\"Erreur de connexion:\", data.error);\n                return false;\n            }\n        } catch (error) {\n            console.error(\"Erreur lors de la connexion:\", error);\n            return false;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"auth-token\");\n        localStorage.removeItem(\"user\");\n        setAuthState({\n            user: null,\n            token: null,\n            isAuthenticated: false,\n            loading: false\n        });\n    };\n    const getAuthHeaders = ()=>{\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (authState.token) {\n            headers[\"Authorization\"] = `Bearer ${authState.token}`;\n        }\n        return headers;\n    };\n    return {\n        ...authState,\n        login,\n        logout,\n        getAuthHeaders\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f9200e9d0ff7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2FybWEtY29tLWRhc2hib2FyZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YTBiNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY5MjAwZTlkMGZmN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\mesDocs\AI\KCS\augment-kcs\src\app\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Karma Com Solidarit\\xe9 - Gestion d'Adh\\xe9sion\",\n    description: \"Plateforme de gestion d'adh\\xe9sion pour Karma Com Solidarit\\xe9 - Associations, Organisations et B\\xe9n\\xe9voles\",\n    keywords: \"karma com, solidarit\\xe9, adh\\xe9sion, associations, b\\xe9n\\xe9voles, organisations\",\n    authors: [\n        {\n            name: \"Karma Com Solidarit\\xe9\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBdUI7S0FBRTtJQUMzQ0MsVUFBVTtBQUNaLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV2IsK0pBQWU7c0JBQzlCLDRFQUFDYztnQkFBSUQsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2thcm1hLWNvbS1kYXNoYm9hcmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0thcm1hIENvbSBTb2xpZGFyaXTDqSAtIEdlc3Rpb24gZFxcJ0FkaMOpc2lvbicsXG4gIGRlc2NyaXB0aW9uOiAnUGxhdGVmb3JtZSBkZSBnZXN0aW9uIGRcXCdhZGjDqXNpb24gcG91ciBLYXJtYSBDb20gU29saWRhcml0w6kgLSBBc3NvY2lhdGlvbnMsIE9yZ2FuaXNhdGlvbnMgZXQgQsOpbsOpdm9sZXMnLFxuICBrZXl3b3JkczogJ2thcm1hIGNvbSwgc29saWRhcml0w6ksIGFkaMOpc2lvbiwgYXNzb2NpYXRpb25zLCBiw6luw6l2b2xlcywgb3JnYW5pc2F0aW9ucycsXG4gIGF1dGhvcnM6IFt7IG5hbWU6ICdLYXJtYSBDb20gU29saWRhcml0w6knIH1dLFxuICB2aWV3cG9ydDogJ3dpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImZyXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsInZpZXdwb3J0IiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();