# 🚀 Guide Configuration GitLab CI/CD - Karma Com Solidarité

## 📋 Vue d'Ensemble

Le pipeline GitLab CI/CD automatise complètement le déploiement de l'application Karma Com Solidarité avec tests intégrés et déploiement sur VPS.

## 🏗️ Architecture du Pipeline

### Stages du Pipeline

1. **validate** - Validation et préparation
2. **test** - Tests unitaires, design et intégration
3. **build** - Build application et images Docker
4. **deploy** - Déploiement staging et production

### Jobs Principaux

| Job | Stage | Description |
|-----|-------|-------------|
| `validate` | validate | Validation config et préparation VPS |
| `test:unit` | test | Tests unitaires avec base de données |
| `test:design` | test | Tests interface et design |
| `test:integration` | test | Tests d'intégration |
| `test:docker` | build | Tests Docker et Docker Compose |
| `build:application` | build | Build Next.js |
| `build:docker` | build | Build et push images Docker |
| `deploy:staging` | deploy | Déploiement staging (manuel) |
| `deploy:production` | deploy | Déploiement production (manuel) |

## ⚙️ Configuration GitLab CI

### 1. Variables d'Environnement Requises

Allez dans **Settings > CI/CD > Variables** de votre projet GitLab et ajoutez :

#### Variables Obligatoires

```bash
# Clé SSH pour accès VPS
SSH_PRIVATE_KEY
Type: File
Protected: ✅
Masked: ❌
Value: [Contenu de votre clé privée SSH]

# Registry Docker GitLab (automatiques)
CI_REGISTRY_USER (automatique)
CI_REGISTRY_PASSWORD (automatique)
```

#### Variables Optionnelles

```bash
# Configuration VPS (déjà dans .gitlab-ci.yml)
VPS_IP=*************
VPS_USER=vpsadmin
DOMAIN=kcs.zidani.org

# Configuration base de données
DATABASE_URL=postgresql://karma_user:karma_password_2024@localhost:5432/karma_com_db
```

### 2. Configuration de la Clé SSH

#### Générer une clé SSH (si nécessaire)

```bash
# Sur votre machine locale
ssh-keygen -t rsa -b 4096 -C "gitlab-ci@karma-com"
```

#### Ajouter la clé publique au VPS

```bash
# Copier la clé publique vers le VPS
ssh-copy-id -i ~/.ssh/id_rsa.pub vpsadmin@*************

# Ou manuellement
cat ~/.ssh/id_rsa.pub | ssh vpsadmin@************* "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"
```

#### Ajouter la clé privée à GitLab

1. Allez dans **Settings > CI/CD > Variables**
2. Cliquez **Add Variable**
3. Key: `SSH_PRIVATE_KEY`
4. Type: `File`
5. Value: Contenu de `~/.ssh/id_rsa` (clé privée)
6. Protected: ✅
7. Masked: ❌

## 🧪 Tests Intégrés

### Tests Unitaires et Fonctionnels
- `npm run test:dashboard-direct`
- `npm run test:inscriptions`
- `npm run test:dashboard`
- `npm run test:auth`
- `npm run diagnose`
- `npm run quick-test`

### Tests de Design
- `npm run test:design`
- `npm run test:loading`
- `npm run check:dashboard`
- `npm run test:tabs`
- `npm run test:interviews`
- `npm run test:overview`

### Tests d'Intégration
- `npm run test:interview-mgmt`
- `npm run test:interview-style`
- `npm run test:candidate-status`

### Tests Docker
- `npm run test:docker`
- `npm run test:docker-compose`
- `npm run test:archive`

## 🚀 Déploiement

### Déploiement Staging

**Déclenchement :** Manuel sur toutes les branches (sauf main)

```bash
# Le pipeline exécute automatiquement :
bash deploy-to-vps-no-sudo.sh
```

**URL :** https://kcs.zidani.org (environnement staging)

### Déploiement Production

**Déclenchement :** Manuel sur la branche main/master uniquement

```bash
# Le pipeline exécute :
bash deploy-to-vps-no-sudo.sh
```

**URLs :**
- Application: https://kcs.zidani.org
- Dashboard: https://kcs.zidani.org/dashboard

### Monitoring Post-Déploiement

Le pipeline vérifie automatiquement :
- ✅ Application accessible
- ✅ Dashboard accessible  
- ✅ API accessible (optionnel)

## 🔧 Utilisation du Pipeline

### 1. Validation Locale

Avant de pousser votre code :

```bash
# Valider le pipeline
npm run validate:pipeline

# Tester les scripts
npm run prepare:vps
npm run verify:config
```

### 2. Déclenchement du Pipeline

```bash
# Pousser le code déclenche automatiquement :
git add .
git commit -m "feat: nouvelle fonctionnalité"
git push origin feature-branch
```

### 3. Déploiement Manuel

1. Allez dans **CI/CD > Pipelines**
2. Cliquez sur le pipeline en cours
3. Cliquez **Deploy** sur le job souhaité :
   - `deploy:staging` pour staging
   - `deploy:production` pour production

### 4. Monitoring

Surveillez les logs dans GitLab :
- **Jobs** pour voir les détails d'exécution
- **Environments** pour voir les déploiements
- **Artifacts** pour télécharger les rapports

## 🛠️ Maintenance

### Rollback

En cas de problème, utilisez le job `rollback` :

1. Allez dans **CI/CD > Pipelines**
2. Cliquez sur le pipeline problématique
3. Cliquez **Rollback** (job manuel)

### Nettoyage

Le job `cleanup` nettoie automatiquement les artefacts temporaires.

### Logs et Debugging

```bash
# Logs sur le VPS
ssh vpsadmin@*************
cd /home/<USER>/kcs
docker-compose logs -f

# Vérifier l'état des services
docker-compose ps
```

## 📊 Artefacts et Rapports

Le pipeline génère :
- **Test Reports** (JUnit XML)
- **Coverage Reports**
- **Build Artifacts** (.next/, out/)
- **Deployment Logs**
- **Docker Images** (GitLab Registry)

## 🔍 Troubleshooting

### Erreur SSH

```bash
# Vérifier la clé SSH
ssh -T vpsadmin@*************

# Régénérer si nécessaire
ssh-keygen -t rsa -b 4096 -C "gitlab-ci@karma-com"
```

### Erreur Docker

```bash
# Vérifier les images
docker images | grep karma-com

# Nettoyer si nécessaire
docker system prune -a
```

### Erreur de Build

```bash
# Tester localement
npm run build
npm run test:docker
```

## 🎯 Avantages du Pipeline

- ✅ **Automatisation complète** du déploiement
- ✅ **Tests intégrés** à chaque push
- ✅ **Déploiement sécurisé** avec clés SSH
- ✅ **Monitoring** post-déploiement
- ✅ **Rollback** en cas de problème
- ✅ **Environnements** staging et production
- ✅ **Artefacts** et rapports détaillés

**Votre pipeline GitLab CI/CD est maintenant configuré pour un déploiement automatisé et sécurisé !** 🚀
