import { useState, useEffect } from 'react'

interface User {
  id: string
  email: string
  name: string
  userType: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  loading: boolean
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isAuthenticated: false,
    loading: true
  })

  // Vérifier l'authentification au chargement
  useEffect(() => {
    const token = localStorage.getItem('auth-token')
    const userStr = localStorage.getItem('user')
    
    if (token && userStr) {
      try {
        const user = JSON.parse(userStr)
        setAuthState({
          user,
          token,
          isAuthenticated: true,
          loading: false
        })
      } catch (error) {
        console.error('Erreur lors de la lecture des données utilisateur:', error)
        logout()
      }
    } else {
      setAuthState(prev => ({ ...prev, loading: false }))
    }
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (response.ok) {
        // Stocker les données d'authentification
        localStorage.setItem('auth-token', data.token)
        localStorage.setItem('user', JSON.stringify(data.user))
        
        setAuthState({
          user: data.user,
          token: data.token,
          isAuthenticated: true,
          loading: false
        })
        
        return true
      } else {
        console.error('Erreur de connexion:', data.error)
        return false
      }
    } catch (error) {
      console.error('Erreur lors de la connexion:', error)
      return false
    }
  }

  const logout = () => {
    localStorage.removeItem('auth-token')
    localStorage.removeItem('user')
    
    setAuthState({
      user: null,
      token: null,
      isAuthenticated: false,
      loading: false
    })
  }

  const getAuthHeaders = (): Record<string, string> => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    if (authState.token) {
      headers['Authorization'] = `Bearer ${authState.token}`
    }

    return headers
  }

  return {
    ...authState,
    login,
    logout,
    getAuthHeaders
  }
}
