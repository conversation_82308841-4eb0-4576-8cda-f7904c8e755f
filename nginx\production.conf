# Configuration Nginx pour Production - Karma Com Solidarité

# Limitation du taux de requêtes
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

# Upstream pour l'application Next.js
upstream karma_app {
    server app:3000;
    keepalive 32;
}

# Redirection HTTP vers HTTPS
server {
    listen 80;
    server_name karma-com-solidarite.fr www.karma-com-solidarite.fr;
    
    # Redirection permanente vers HTTPS
    return 301 https://$server_name$request_uri;
}

# Configuration HTTPS principale
server {
    listen 443 ssl http2;
    server_name karma-com-solidarite.fr www.karma-com-solidarite.fr;
    
    # Configuration SSL/TLS
    ssl_certificate /etc/nginx/ssl/karma-com-solidarite.fr.crt;
    ssl_certificate_key /etc/nginx/ssl/karma-com-solidarite.fr.key;
    
    # Paramètres SSL sécurisés
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Headers de sécurité
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;
    
    # Configuration des logs
    access_log /var/log/nginx/karma-com-access.log;
    error_log /var/log/nginx/karma-com-error.log;
    
    # Taille maximale des uploads
    client_max_body_size 10M;
    
    # Timeouts
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Cache pour les assets statiques
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        
        # Proxy vers l'application
        proxy_pass http://karma_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # API avec limitation de taux
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://karma_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Headers pour WebSocket (si nécessaire)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # Routes de connexion avec limitation stricte
    location ~ ^/(connexion|login|auth)/ {
        limit_req zone=login burst=5 nodelay;
        
        proxy_pass http://karma_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Health check
    location /health {
        access_log off;
        proxy_pass http://karma_app;
        proxy_set_header Host $host;
    }
    
    # Robots.txt
    location /robots.txt {
        proxy_pass http://karma_app;
        proxy_set_header Host $host;
    }
    
    # Sitemap
    location /sitemap.xml {
        proxy_pass http://karma_app;
        proxy_set_header Host $host;
    }
    
    # Toutes les autres routes
    location / {
        # Cache pour les pages
        proxy_cache_bypass $http_upgrade;
        
        proxy_pass http://karma_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Headers pour WebSocket
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Buffers
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Bloquer l'accès aux fichiers sensibles
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Page d'erreur personnalisée
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# Configuration pour le sous-domaine www
server {
    listen 443 ssl http2;
    server_name www.karma-com-solidarite.fr;
    
    ssl_certificate /etc/nginx/ssl/karma-com-solidarite.fr.crt;
    ssl_certificate_key /etc/nginx/ssl/karma-com-solidarite.fr.key;
    
    # Redirection vers le domaine principal
    return 301 https://karma-com-solidarite.fr$request_uri;
}
