import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const partnershipTypes = await prisma.partnershipType.findMany({
      where: { isActive: true },
      orderBy: { order: 'asc' },
      select: {
        id: true,
        name: true
      }
    })

    return NextResponse.json(partnershipTypes)
  } catch (error) {
    console.error('Erreur lors de la récupération des types de partenariat:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
