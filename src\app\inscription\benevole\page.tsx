'use client'

import React, { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { Users, User, Mail, Phone, MapPin, Heart, Calendar, Star } from 'lucide-react'

export default function BenevoleInscriptionPage() {
  const [formData, setFormData] = useState({
    // Informations de contact
    email: '',
    password: '',
    confirmPassword: '',
    
    // Informations personnelles
    firstName: '',
    lastName: '',
    phone: '',
    dateOfBirth: '',
    
    // Adresse
    address: '',
    city: '',
    postalCode: '',
    country: 'France',
    
    // Compétences et disponibilités
    skills: [] as string[],
    customSkills: '',
    availability: '',
    experience: '',
    motivation: '',
    
    // Préférences
    preferredDepartments: [] as string[],
    remoteWork: false,
    
    // Conditions
    acceptTerms: false,
    acceptRGPD: false
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const availableSkills = [
    'Communication',
    'Marketing digital',
    'Développement web',
    'Design graphique',
    'Gestion de projet',
    'Comptabilité',
    'Juridique',
    'Traduction',
    'Rédaction',
    'Photographie',
    'Vidéo',
    'Formation',
    'Événementiel',
    'Logistique',
    'Autre'
  ]

  const departments = [
    'RH/Asso',
    'Communication',
    'Partenariats & RSE',
    'IT',
    'Design',
    'Juridique',
    'Comptabilité',
    'Auto & AI'
  ]

  const availabilityOptions = [
    'Quelques heures par semaine',
    'Une demi-journée par semaine',
    'Une journée par semaine',
    'Plusieurs jours par semaine',
    'Ponctuellement selon les projets',
    'Disponibilité flexible'
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
    
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleSkillChange = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.includes(skill)
        ? prev.skills.filter(s => s !== skill)
        : [...prev.skills, skill]
    }))
  }

  const handleDepartmentChange = (department: string) => {
    setFormData(prev => ({
      ...prev,
      preferredDepartments: prev.preferredDepartments.includes(department)
        ? prev.preferredDepartments.filter(d => d !== department)
        : [...prev.preferredDepartments, department]
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Validation email
    if (!formData.email) {
      newErrors.email = 'L\'email est requis'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide'
    }

    // Validation mot de passe
    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères'
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas'
    }

    // Validation champs requis
    const requiredFields = ['firstName', 'lastName', 'motivation']
    requiredFields.forEach(field => {
      if (!formData[field as keyof typeof formData]) {
        newErrors[field] = 'Ce champ est requis'
      }
    })

    // Validation date de naissance
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth)
      const today = new Date()
      const age = today.getFullYear() - birthDate.getFullYear()
      if (age < 16) {
        newErrors.dateOfBirth = 'Vous devez avoir au moins 16 ans'
      }
    }

    // Validation acceptation des conditions
    if (!formData.acceptTerms) {
      newErrors.acceptTerms = 'Vous devez accepter les conditions d\'utilisation'
    }

    if (!formData.acceptRGPD) {
      newErrors.acceptRGPD = 'Vous devez accepter la politique de confidentialité'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/auth/register/volunteer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        alert('Inscription réussie ! Vous recevrez un email de confirmation.')
        // Réinitialiser le formulaire
        setFormData({
          email: '',
          password: '',
          confirmPassword: '',
          firstName: '',
          lastName: '',
          phone: '',
          dateOfBirth: '',
          address: '',
          city: '',
          postalCode: '',
          country: 'France',
          skills: [],
          customSkills: '',
          availability: '',
          experience: '',
          motivation: '',
          preferredDepartments: [],
          remoteWork: false,
          acceptTerms: false,
          acceptRGPD: false
        })
      } else {
        // Afficher les erreurs de validation
        if (data.details) {
          const newErrors: Record<string, string> = {}
          data.details.forEach((error: any) => {
            if (error.path && error.path.length > 0) {
              newErrors[error.path[0]] = error.message
            }
          })
          setErrors(newErrors)
        } else {
          alert(data.error || 'Une erreur est survenue lors de l\'inscription.')
        }
      }
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error)
      alert('Une erreur est survenue. Veuillez réessayer.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container-karma py-12">
        {/* En-tête */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full flex items-center justify-center">
              <Users className="text-white" size={32} />
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Devenir Bénévole
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Rejoignez notre équipe de bénévoles et contribuez à des projets solidaires
          </p>
        </div>

        {/* Formulaire */}
        <div className="max-w-4xl mx-auto">
          <form onSubmit={handleSubmit} className="space-y-8">
            
            {/* Section Compte */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <User className="mr-3 text-accent-600" size={24} />
                Informations de connexion
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="karma-label">Email *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.email ? 'border-red-500' : ''}`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                </div>
                <div>
                  <label className="karma-label">Téléphone</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="karma-input"
                    placeholder="+33 1 23 45 67 89"
                  />
                </div>
                <div>
                  <label className="karma-label">Mot de passe *</label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.password ? 'border-red-500' : ''}`}
                    placeholder="Minimum 8 caractères"
                  />
                  {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
                </div>
                <div>
                  <label className="karma-label">Confirmer le mot de passe *</label>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.confirmPassword ? 'border-red-500' : ''}`}
                    placeholder="Répétez le mot de passe"
                  />
                  {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>}
                </div>
              </div>
            </div>

            {/* Section Informations personnelles */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <User className="mr-3 text-accent-600" size={24} />
                Informations personnelles
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="karma-label">Prénom *</label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.firstName ? 'border-red-500' : ''}`}
                    placeholder="Votre prénom"
                  />
                  {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
                </div>
                <div>
                  <label className="karma-label">Nom *</label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.lastName ? 'border-red-500' : ''}`}
                    placeholder="Votre nom"
                  />
                  {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
                </div>
                <div>
                  <label className="karma-label">Date de naissance</label>
                  <input
                    type="date"
                    name="dateOfBirth"
                    value={formData.dateOfBirth}
                    onChange={handleInputChange}
                    className={`karma-input ${errors.dateOfBirth ? 'border-red-500' : ''}`}
                  />
                  {errors.dateOfBirth && <p className="text-red-500 text-sm mt-1">{errors.dateOfBirth}</p>}
                </div>
              </div>
            </div>

            {/* Section Adresse */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <MapPin className="mr-3 text-accent-600" size={24} />
                Adresse
              </h2>
              <div className="space-y-6">
                <div>
                  <label className="karma-label">Adresse</label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="karma-input"
                    placeholder="Numéro et nom de rue"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="karma-label">Ville</label>
                    <input
                      type="text"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      className="karma-input"
                      placeholder="Ville"
                    />
                  </div>
                  <div>
                    <label className="karma-label">Code postal</label>
                    <input
                      type="text"
                      name="postalCode"
                      value={formData.postalCode}
                      onChange={handleInputChange}
                      className="karma-input"
                      placeholder="75001"
                    />
                  </div>
                  <div>
                    <label className="karma-label">Pays</label>
                    <select
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      className="karma-input"
                    >
                      <option value="France">France</option>
                      <option value="Belgique">Belgique</option>
                      <option value="Suisse">Suisse</option>
                      <option value="Canada">Canada</option>
                      <option value="Autre">Autre</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Section Compétences */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <Star className="mr-3 text-accent-600" size={24} />
                Compétences et expérience
              </h2>
              <div className="space-y-6">
                <div>
                  <label className="karma-label">Compétences (sélectionnez toutes celles qui s'appliquent)</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                    {availableSkills.map(skill => (
                      <label key={skill} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.skills.includes(skill)}
                          onChange={() => handleSkillChange(skill)}
                          className="rounded border-gray-300 text-accent-600 focus:ring-accent-500"
                        />
                        <span className="text-sm text-gray-700">{skill}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="karma-label">Autres compétences</label>
                  <input
                    type="text"
                    name="customSkills"
                    value={formData.customSkills}
                    onChange={handleInputChange}
                    className="karma-input"
                    placeholder="Décrivez d'autres compétences non listées..."
                  />
                </div>

                <div>
                  <label className="karma-label">Expérience en bénévolat</label>
                  <textarea
                    name="experience"
                    value={formData.experience}
                    onChange={handleInputChange}
                    rows={3}
                    className="karma-input"
                    placeholder="Décrivez votre expérience en bénévolat ou dans des projets similaires..."
                  />
                </div>
              </div>
            </div>

            {/* Section Disponibilités */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <Calendar className="mr-3 text-accent-600" size={24} />
                Disponibilités et préférences
              </h2>
              <div className="space-y-6">
                <div>
                  <label className="karma-label">Disponibilité</label>
                  <select
                    name="availability"
                    value={formData.availability}
                    onChange={handleInputChange}
                    className="karma-input"
                  >
                    <option value="">Sélectionnez votre disponibilité</option>
                    {availabilityOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="karma-label">Pôles d'intérêt (sélectionnez ceux qui vous intéressent)</label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-2">
                    {departments.map(dept => (
                      <label key={dept} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.preferredDepartments.includes(dept)}
                          onChange={() => handleDepartmentChange(dept)}
                          className="rounded border-gray-300 text-accent-600 focus:ring-accent-500"
                        />
                        <span className="text-sm text-gray-700">{dept}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    name="remoteWork"
                    checked={formData.remoteWork}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-accent-600 focus:ring-accent-500"
                  />
                  <label className="text-sm text-gray-700">
                    Je suis disponible pour du travail à distance
                  </label>
                </div>
              </div>
            </div>

            {/* Section Motivation */}
            <div className="form-section">
              <h2 className="form-title flex items-center">
                <Heart className="mr-3 text-accent-600" size={24} />
                Motivation
              </h2>
              <div>
                <label className="karma-label">
                  Pourquoi souhaitez-vous devenir bénévole chez Karma Com ? *
                </label>
                <textarea
                  name="motivation"
                  value={formData.motivation}
                  onChange={handleInputChange}
                  rows={4}
                  className={`karma-input ${errors.motivation ? 'border-red-500' : ''}`}
                  placeholder="Expliquez vos motivations et ce que vous espérez apporter à notre association..."
                />
                {errors.motivation && <p className="text-red-500 text-sm mt-1">{errors.motivation}</p>}
              </div>
            </div>

            {/* Conditions */}
            <div className="form-section">
              <div className="space-y-4">
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    name="acceptTerms"
                    checked={formData.acceptTerms}
                    onChange={handleInputChange}
                    className="mt-1 mr-3"
                  />
                  <label className="text-sm text-gray-700">
                    J'accepte les{' '}
                    <a href="/conditions" className="text-accent-600 hover:underline">
                      conditions d'utilisation
                    </a>{' '}
                    de Karma Com Solidarité *
                  </label>
                </div>
                {errors.acceptTerms && <p className="text-red-500 text-sm">{errors.acceptTerms}</p>}
                
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    name="acceptRGPD"
                    checked={formData.acceptRGPD}
                    onChange={handleInputChange}
                    className="mt-1 mr-3"
                  />
                  <label className="text-sm text-gray-700">
                    J'accepte la{' '}
                    <a href="/politique-confidentialite" className="text-accent-600 hover:underline">
                      politique de confidentialité
                    </a>{' '}
                    et le traitement de mes données personnelles selon le RGPD *
                  </label>
                </div>
                {errors.acceptRGPD && <p className="text-red-500 text-sm">{errors.acceptRGPD}</p>}
              </div>
            </div>

            {/* Bouton de soumission */}
            <div className="text-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`bg-gradient-to-r from-accent-600 to-accent-700 text-white font-medium py-3 px-12 rounded-lg hover:from-accent-700 hover:to-accent-800 transition-all duration-200 transform hover:scale-105 text-lg ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? 'Inscription en cours...' : 'Rejoindre l\'équipe'}
              </button>
            </div>
          </form>
        </div>
      </div>

      <Footer />
    </div>
  )
}
