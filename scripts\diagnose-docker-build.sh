#!/bin/bash

# Script de diagnostic pour les problèmes de build Docker
echo "🔍 Diagnostic Build Docker - Karma Com Solidarité"
echo "================================================"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier la structure locale
print_status "Vérification de la structure locale..."
echo "📁 Structure src/components:"
if [ -d "src/components" ]; then
    find src/components -name "*.tsx" -o -name "*.ts" | head -10
    print_success "Composants trouvés localement"
else
    print_error "Dossier src/components manquant"
fi

echo ""
print_status "Vérification des fichiers de configuration..."
config_files=("tsconfig.json" "next.config.js" "tailwind.config.js" "postcss.config.js")
for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "$file présent"
    else
        print_warning "$file manquant"
    fi
done

echo ""
print_status "Vérification du .dockerignore..."
if [ -f ".dockerignore" ]; then
    print_success ".dockerignore présent"
    echo "📋 Fichiers exclus importants:"
    grep -E "(tsconfig|next\.config|tailwind|postcss)" .dockerignore || echo "Aucun fichier de config exclu"
else
    print_warning ".dockerignore manquant"
fi

echo ""
print_status "Test de résolution des modules localement..."
if command -v node &> /dev/null; then
    # Tester la résolution des chemins TypeScript
    cat > temp_test.js << 'EOF'
const fs = require('fs');
const path = require('path');

// Vérifier que les composants existent
const components = [
    'src/components/layout/Header.tsx',
    'src/components/layout/Footer.tsx',
    'src/components/ui/Logo.tsx'
];

console.log('🔍 Vérification des composants:');
components.forEach(comp => {
    if (fs.existsSync(comp)) {
        console.log(`✅ ${comp}`);
    } else {
        console.log(`❌ ${comp}`);
    }
});

// Vérifier tsconfig.json
if (fs.existsSync('tsconfig.json')) {
    const tsconfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
    console.log('\n📋 Configuration TypeScript:');
    console.log('baseUrl:', tsconfig.compilerOptions?.baseUrl || 'non défini');
    console.log('paths:', JSON.stringify(tsconfig.compilerOptions?.paths || {}, null, 2));
} else {
    console.log('❌ tsconfig.json manquant');
}
EOF

    node temp_test.js
    rm temp_test.js
else
    print_warning "Node.js non disponible pour les tests"
fi

echo ""
print_status "Simulation du contexte Docker..."
echo "📦 Fichiers qui seraient copiés dans Docker:"
# Simuler ce qui serait copié (en excluant .dockerignore)
if [ -f ".dockerignore" ]; then
    # Compter les fichiers qui seraient copiés
    total_files=$(find . -type f | wc -l)
    excluded_files=$(find . -type f | grep -f .dockerignore | wc -l 2>/dev/null || echo 0)
    copied_files=$((total_files - excluded_files))
    echo "   Total fichiers: $total_files"
    echo "   Fichiers exclus: $excluded_files"
    echo "   Fichiers copiés: $copied_files"
else
    echo "   Tous les fichiers seraient copiés"
fi

echo ""
print_status "Vérification des dépendances critiques..."
critical_deps=("next" "react" "typescript" "@types/node")
if [ -f "package.json" ]; then
    for dep in "${critical_deps[@]}"; do
        if grep -q "\"$dep\"" package.json; then
            print_success "$dep présent dans package.json"
        else
            print_warning "$dep manquant dans package.json"
        fi
    done
else
    print_error "package.json manquant"
fi

echo ""
print_status "Test de build local (simulation)..."
if command -v npm &> /dev/null; then
    echo "📋 Vérification des scripts npm:"
    npm run --silent 2>/dev/null | grep -E "(build|dev)" || echo "Scripts build/dev non trouvés"
    
    # Tester si les modules peuvent être résolus
    if [ -d "node_modules" ]; then
        print_success "node_modules présent"
    else
        print_warning "node_modules manquant - exécutez 'npm install'"
    fi
else
    print_warning "npm non disponible"
fi

echo ""
print_status "Recommandations pour corriger les erreurs Docker:"
echo "🔧 Actions suggérées:"
echo "   1. Vérifier que tsconfig.json n'est PAS dans .dockerignore"
echo "   2. Vérifier que next.config.js n'est PAS dans .dockerignore"
echo "   3. Vérifier que tailwind.config.js n'est PAS dans .dockerignore"
echo "   4. S'assurer que tous les composants src/ sont copiés"
echo "   5. Vérifier les alias de chemins dans tsconfig.json"

echo ""
echo "🐳 Pour déboguer le build Docker:"
echo "   docker build --no-cache --progress=plain -t karma-com-app ."
echo ""
echo "🔍 Pour inspecter le conteneur en cas d'échec:"
echo "   docker run --rm -it node:18-alpine sh"
echo "   # Puis copier manuellement les fichiers pour tester"

print_success "Diagnostic terminé"
