const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  console.log('🗄️ Initialisation de la base de données...')
  
  try {
    // Vérifier la connexion à la base de données
    await prisma.$connect()
    console.log('✅ Connexion à la base de données réussie')
    
    // Créer un utilisateur admin RH par défaut
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!existingAdmin) {
      const bcrypt = require('bcryptjs')
      const hashedPassword = await bcrypt.hash('admin123', 12)
      
      const admin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Admin RH',
          userType: 'HR_ADMIN',
          profile: {
            create: {
              firstName: 'Admin',
              lastName: 'RH',
              membershipStatus: 'ACTIVE'
            }
          }
        }
      })
      
      console.log('✅ Utilisateur admin créé:', admin.email)
    } else {
      console.log('✅ Utilisateur admin déjà existant')
    }
    
    // Créer quelques données de test
    console.log('🌱 Création de données de test...')
    
    // Association de test
    const testAssociation = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!testAssociation) {
      const bcrypt = require('bcryptjs')
      const hashedPassword = await bcrypt.hash('test123', 12)
      
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Association Test',
          userType: 'ASSOCIATION',
          profile: {
            create: {
              firstName: 'Marie',
              lastName: 'Dupont',
              organizationName: 'Association Les Amis de la Nature',
              description: 'Association dédiée à la protection de l\'environnement',
              membershipStatus: 'PENDING'
            }
          }
        }
      })
      console.log('✅ Association de test créée')
    }
    
    // Organisation de test
    const testOrganization = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!testOrganization) {
      const bcrypt = require('bcryptjs')
      const hashedPassword = await bcrypt.hash('test123', 12)
      
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'TechCorp Solutions',
          userType: 'ORGANIZATION',
          profile: {
            create: {
              firstName: 'Jean',
              lastName: 'Martin',
              organizationName: 'TechCorp Solutions',
              description: 'Entreprise de solutions technologiques',
              membershipStatus: 'APPROVED'
            }
          }
        }
      })
      console.log('✅ Organisation de test créée')
    }
    
    // Bénévole de test
    const testVolunteer = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!testVolunteer) {
      const bcrypt = require('bcryptjs')
      const hashedPassword = await bcrypt.hash('test123', 12)
      
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Sophie Laurent',
          userType: 'VOLUNTEER',
          profile: {
            create: {
              firstName: 'Sophie',
              lastName: 'Laurent',
              skills: ['Communication', 'Marketing digital', 'Gestion de projet'],
              availability: 'Une demi-journée par semaine',
              motivation: 'Je souhaite contribuer à des projets solidaires et mettre mes compétences au service de causes importantes.',
              membershipStatus: 'ACTIVE'
            }
          }
        }
      })
      console.log('✅ Bénévole de test créé')
    }
    
    // Statistiques
    const stats = await prisma.user.groupBy({
      by: ['userType'],
      _count: {
        id: true
      }
    })
    
    console.log('\n📊 Statistiques de la base de données:')
    stats.forEach(stat => {
      console.log(`  ${stat.userType}: ${stat._count.id} utilisateur(s)`)
    })
    
    console.log('\n🎉 Initialisation terminée avec succès!')
    console.log('\n🔑 Comptes de test créés:')
    console.log('  Admin RH: <EMAIL> / admin123')
    console.log('  Association: <EMAIL> / test123')
    console.log('  Organisation: <EMAIL> / test123')
    console.log('  Bénévole: <EMAIL> / test123')
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
