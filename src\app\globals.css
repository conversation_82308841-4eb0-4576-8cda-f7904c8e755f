@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  font-family: 'Inter', sans-serif;
}

/* Styles personnalisés pour Karma Com Solidarité */
.karma-gradient {
  background: linear-gradient(135deg, #1e3a5f 0%, #e91e63 100%);
}

.karma-gradient-text {
  background: linear-gradient(135deg, #1e3a5f 0%, #e91e63 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.karma-card {
  @apply bg-white rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 p-6;
}

.karma-button-primary {
  @apply bg-karma-blue hover:bg-primary-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.karma-button-secondary {
  @apply bg-karma-pink hover:bg-secondary-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.karma-button-outline {
  @apply border-2 border-karma-blue text-karma-blue hover:bg-karma-blue hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200;
}

.karma-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-karma-pink focus:border-transparent transition-all duration-200;
}

/* Styles spécifiques pour les éléments select (correction Mac/Safari) */
select.karma-input {
  @apply appearance-none bg-white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Correction spécifique pour Safari sur Mac */
@supports (-webkit-appearance: none) {
  select.karma-input {
    background-color: white !important;
    color: #374151 !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    min-height: 2.75rem !important;
  }

  select.karma-input:focus {
    border-color: #e91e63 !important;
    box-shadow: 0 0 0 2px rgba(233, 30, 99, 0.2) !important;
    outline: none !important;
  }

  select.karma-input option {
    background-color: white !important;
    color: #374151 !important;
    padding: 0.5rem !important;
  }
}

/* Composant Select personnalisé pour une meilleure compatibilité */
.karma-select {
  @apply w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-karma-pink focus:border-transparent transition-all duration-200 bg-white text-gray-900;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: none;
  min-height: 2.75rem;
}

/* Styles spécifiques pour Mac/Safari */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .karma-select {
    background-color: white !important;
    color: #111827 !important;
    border: 1px solid #d1d5db !important;
    font-size: 1rem !important;
    line-height: 1.5rem !important;
    padding: 0.75rem 2.5rem 0.75rem 1rem !important;
  }

  .karma-select:focus {
    border-color: #e91e63 !important;
    box-shadow: 0 0 0 2px rgba(233, 30, 99, 0.2) !important;
    outline: none !important;
  }

  .karma-select option {
    background-color: white !important;
    color: #111827 !important;
    padding: 0.5rem 1rem !important;
    font-size: 1rem !important;
  }
}

/* Styles pour les options sur différents navigateurs */
.karma-select option {
  @apply bg-white text-gray-900 py-2 px-4;
}

.karma-select option:checked {
  @apply bg-karma-pink text-white;
}

.karma-select option:hover {
  @apply bg-gray-100;
}

/* Corrections spécifiques pour Mac Safari - Force la visibilité */
@supports (-webkit-appearance: none) and (not (appearance: none)) {
  /* Correction pour Safari sur Mac */
  select.karma-input,
  .karma-select {
    -webkit-appearance: none !important;
    appearance: none !important;
    background-color: #ffffff !important;
    color: #111827 !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    padding: 0.75rem 2.5rem 0.75rem 1rem !important;
    font-size: 1rem !important;
    line-height: 1.5rem !important;
    min-height: 2.75rem !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 0.5rem center !important;
    background-repeat: no-repeat !important;
    background-size: 1.5em 1.5em !important;
  }

  select.karma-input option,
  .karma-select option {
    background-color: #ffffff !important;
    color: #111827 !important;
    padding: 0.5rem 1rem !important;
    font-size: 1rem !important;
    font-weight: normal !important;
    opacity: 1 !important;
  }

  select.karma-input:focus,
  .karma-select:focus {
    border-color: #e91e63 !important;
    box-shadow: 0 0 0 2px rgba(233, 30, 99, 0.2) !important;
    outline: none !important;
  }
}

/* Correction pour tous les navigateurs WebKit (Safari, Chrome sur Mac) */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
  }

  select.karma-input,
  .karma-select {
    background-color: white !important;
    color: #374151 !important;
    opacity: 1 !important;
  }

  select.karma-input option,
  .karma-select option {
    background-color: white !important;
    color: #374151 !important;
    opacity: 1 !important;
    font-weight: normal !important;
  }
}

.karma-label {
  @apply block text-sm font-medium text-karma-blue mb-2;
}

/* Animations personnalisées */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Responsive design helpers */
.container-karma {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Styles pour les formulaires */
.form-section {
  @apply bg-white p-6 rounded-lg shadow-md border border-gray-200 mb-6;
}

.form-title {
  @apply text-xl font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200;
}

/* Styles pour le dashboard */
.dashboard-card {
  @apply bg-white p-6 rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow duration-200;
}

.stat-card {
  @apply bg-gradient-to-br from-white to-gray-50 p-6 rounded-lg shadow-md border border-gray-200;
}

/* Styles pour les badges de statut */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800;
}

.status-approved {
  @apply bg-green-100 text-green-800;
}

.status-rejected {
  @apply bg-red-100 text-red-800;
}

.status-active {
  @apply bg-blue-100 text-blue-800;
}

.status-inactive {
  @apply bg-gray-100 text-gray-800;
}
