# 🐳 Guide Docker - Karma Com Solidarité

## 🎯 Objectif
Ce guide explique comment construire et déployer l'application Karma Com Solidarité avec Docker.

## 📋 Prérequis
- ✅ Docker Desktop installé et démarré
- ✅ Code source de l'application
- ✅ Fichiers de configuration présents

## 🔧 Corrections Apportées au Dockerfile

### Problèmes Identifiés
L'erreur `Cannot find module '/app/server.js'` était causée par :

1. **Mauvais chemins de copie** dans les étapes de build
2. **WORKDIR manquant** dans l'étape runner
3. **Chemins incorrects** pour les fichiers standalone
4. **Structure de répertoires** non respectée

### Solutions Implémentées

#### 1. Correction des Chemins de Dépendances
```dockerfile
# ❌ Avant (incorrect)
COPY package.json package-lock.json* app/

# ✅ Après (correct)
COPY package.json package-lock.json* ./
```

#### 2. Correction du WORKDIR
```dockerfile
# ❌ Avant (commenté)
# WORKDIR /app

# ✅ Après (actif)
WORKDIR /app
```

#### 3. Correction des Chemins de Build
```dockerfile
# ❌ Avant (chemins incorrects)
COPY --from=deps /node_modules ./node_modules
COPY --from=builder /.next/standalone /app

# ✅ Après (chemins corrects)
COPY --from=deps /app/node_modules ./node_modules
COPY --from=builder /app/.next/standalone ./
```

#### 4. Ajout des Fichiers Publics
```dockerfile
# ✅ Ajouté (manquant avant)
COPY --from=builder /app/public ./public
```

## 🏗️ Structure du Dockerfile Corrigé

### Étape 1 : Base
```dockerfile
FROM node:18-alpine AS base
```

### Étape 2 : Installation des Dépendances
```dockerfile
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY package.json package-lock.json* ./
RUN npm ci
```

### Étape 3 : Build de l'Application
```dockerfile
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npx prisma generate
RUN npm run build
```

### Étape 4 : Image de Production
```dockerfile
FROM base AS runner
WORKDIR /app
ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

## 🚀 Utilisation

### Construction de l'Image
```bash
# Construction de l'image
docker build -t karma-com-app:latest .

# Vérification de l'image créée
docker images | grep karma-com-app
```

### Démarrage du Conteneur
```bash
# Démarrage simple
docker run -p 3000:3000 karma-com-app:latest

# Démarrage en arrière-plan
docker run -d -p 3000:3000 --name karma-com karma-com-app:latest

# Avec variables d'environnement
docker run -p 3000:3000 \
  -e DATABASE_URL="********************************/db" \
  -e NEXTAUTH_SECRET="your-secret" \
  karma-com-app:latest
```

### Accès à l'Application
- **URL** : http://localhost:3000
- **Dashboard** : http://localhost:3000/dashboard

## 🧪 Tests

### Test Automatique
```bash
# Test complet du Dockerfile
npm run test:docker
```

### Test Manuel
```bash
# 1. Construire l'image
docker build -t karma-com-app .

# 2. Démarrer le conteneur
docker run -d -p 3000:3000 --name test-karma karma-com-app

# 3. Vérifier les logs
docker logs test-karma

# 4. Tester l'accès
curl http://localhost:3000

# 5. Nettoyer
docker stop test-karma
docker rm test-karma
```

## 📊 Optimisations

### .dockerignore
Le fichier `.dockerignore` exclut :
- `node_modules/` (reconstruit dans le conteneur)
- `.next/` (reconstruit pendant le build)
- Fichiers de développement et documentation
- Scripts et configurations non nécessaires

### Multi-stage Build
- **deps** : Installation des dépendances uniquement
- **builder** : Build de l'application avec Prisma
- **runner** : Image finale optimisée pour la production

### Sécurité
- Utilisateur non-root (`nextjs:nodejs`)
- Image Alpine légère
- Exposition du port 3000 uniquement

## 🐛 Dépannage

### Erreur "Cannot find module"
```bash
# Vérifier la structure dans le conteneur
docker run --rm -it karma-com-app sh
ls -la /app/
```

### Problème de Build
```bash
# Build avec logs détaillés
docker build --no-cache --progress=plain -t karma-com-app .
```

### Problème de Démarrage
```bash
# Vérifier les logs du conteneur
docker logs <container-id>

# Accéder au conteneur
docker exec -it <container-id> sh
```

### Problème de Connectivité
```bash
# Vérifier que le port est exposé
docker port <container-id>

# Tester depuis l'intérieur du conteneur
docker exec -it <container-id> wget -qO- http://localhost:3000
```

## 🔧 Variables d'Environnement

### Variables Requises
```bash
DATABASE_URL=************************************/database
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000
```

### Variables Optionnelles
```bash
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0
```

## 📈 Performance

### Taille de l'Image
- **Base** : node:18-alpine (~40MB)
- **Dépendances** : +~200MB
- **Application** : +~50MB
- **Total** : ~290MB

### Temps de Build
- **Première fois** : 3-5 minutes
- **Avec cache** : 30-60 secondes
- **Changements code** : 1-2 minutes

## 🌐 Déploiement

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/karma_com_db
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: karma_com_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### Production
```bash
# Tag pour production
docker tag karma-com-app:latest karma-com-app:v1.0.0

# Push vers registry
docker push your-registry/karma-com-app:v1.0.0

# Déploiement
docker run -d \
  --name karma-com-prod \
  -p 80:3000 \
  --restart unless-stopped \
  -e NODE_ENV=production \
  your-registry/karma-com-app:v1.0.0
```

## ✅ Checklist de Validation

### Build
- [ ] Image se construit sans erreur
- [ ] Taille de l'image raisonnable (<500MB)
- [ ] Toutes les dépendances incluses
- [ ] Prisma client généré

### Runtime
- [ ] Conteneur démarre sans erreur
- [ ] Application accessible sur le port 3000
- [ ] Logs sans erreurs critiques
- [ ] Utilisateur non-root utilisé

### Fonctionnalité
- [ ] Page d'accueil accessible
- [ ] Dashboard fonctionnel
- [ ] APIs répondent correctement
- [ ] Base de données connectée (si configurée)

## 🎉 Résultat Final

Avec ces corrections, le Dockerfile :
- ✅ **Construit l'image** sans erreur
- ✅ **Démarre l'application** correctement
- ✅ **Trouve le server.js** au bon endroit
- ✅ **Respecte les bonnes pratiques** Docker
- ✅ **Optimise la taille** et les performances

**L'application Karma Com Solidarité est maintenant prête pour le déploiement Docker !** 🚀
