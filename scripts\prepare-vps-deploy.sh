#!/bin/bash

# Script de préparation pour le déploiement VPS
echo "🔍 Préparation Déploiement VPS - Karma Com Solidarité"
echo "===================================================="

# Configuration
VPS_IP="*************"
VPS_USER="vpsadmin"
DOMAIN="kcs.zidani.org"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des fichiers locaux
print_status "Vérification des fichiers locaux..."

required_files=(
    "package.json"
    "Dockerfile.simple"
    "src/app/page.tsx"
    "prisma/schema.prisma"
    "next.config.js"
    "tailwind.config.js"
    "tsconfig.json"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "$file présent"
    else
        print_error "$file manquant"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    print_error "Fichiers manquants: ${missing_files[*]}"
    exit 1
fi

# Vérification de la structure du projet
print_status "Vérification de la structure du projet..."

required_dirs=(
    "src/app"
    "src/components"
    "prisma"
)

for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_success "Répertoire $dir présent"
    else
        print_error "Répertoire $dir manquant"
    fi
done

# Vérification des dépendances
print_status "Vérification des dépendances..."

if [ -f "package.json" ]; then
    if grep -q "next" package.json; then
        print_success "Next.js configuré"
    else
        print_error "Next.js non trouvé dans package.json"
    fi
    
    if grep -q "prisma" package.json; then
        print_success "Prisma configuré"
    else
        print_warning "Prisma non trouvé dans package.json"
    fi
    
    if grep -q "tailwindcss" package.json; then
        print_success "Tailwind CSS configuré"
    else
        print_warning "Tailwind CSS non trouvé"
    fi
fi

# Test de build local
print_status "Test de build local..."

if command -v npm &> /dev/null; then
    if [ -d "node_modules" ]; then
        print_success "node_modules présent"
    else
        print_warning "node_modules manquant - exécutez 'npm install'"
    fi
    
    # Test de build (sans base de données)
    export DATABASE_URL="***********************************/dummy"
    if npm run build &> /dev/null; then
        print_success "Build local réussi"
    else
        print_warning "Build local échoué - vérifiez les erreurs"
    fi
else
    print_warning "npm non disponible pour les tests"
fi

# Test de connexion SSH
print_status "Test de connexion SSH au VPS..."

if command -v ssh &> /dev/null; then
    if ssh -o ConnectTimeout=10 -o BatchMode=yes $VPS_USER@$VPS_IP exit 2>/dev/null; then
        print_success "Connexion SSH au VPS réussie"
    else
        print_error "Impossible de se connecter au VPS"
        echo "Vérifiez :"
        echo "  - Votre clé SSH est configurée"
        echo "  - Le VPS est accessible"
        echo "  - L'utilisateur $VPS_USER existe"
    fi
else
    print_error "SSH non disponible"
fi

# Vérification DNS
print_status "Vérification DNS pour $DOMAIN..."

if command -v nslookup &> /dev/null; then
    DNS_RESULT=$(nslookup $DOMAIN 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}')
    if [ "$DNS_RESULT" = "$VPS_IP" ]; then
        print_success "DNS configuré correctement ($DOMAIN → $VPS_IP)"
    else
        print_warning "DNS non configuré ou en cours de propagation"
        echo "  Attendu: $VPS_IP"
        echo "  Trouvé: $DNS_RESULT"
    fi
else
    print_warning "nslookup non disponible pour vérifier le DNS"
fi

# Vérification des services VPS
print_status "Vérification des services sur le VPS..."

if ssh -o ConnectTimeout=10 $VPS_USER@$VPS_IP 'command -v docker' &>/dev/null; then
    print_success "Docker installé sur le VPS"
else
    print_error "Docker non installé sur le VPS"
fi

if ssh -o ConnectTimeout=10 $VPS_USER@$VPS_IP 'command -v docker-compose' &>/dev/null; then
    print_success "Docker Compose installé sur le VPS"
else
    print_error "Docker Compose non installé sur le VPS"
fi

if ssh -o ConnectTimeout=10 $VPS_USER@$VPS_IP 'sudo nginx -t' &>/dev/null; then
    print_success "Nginx configuré sur le VPS"
else
    print_warning "Nginx non configuré ou erreur de configuration"
fi

if ssh -o ConnectTimeout=10 $VPS_USER@$VPS_IP 'command -v certbot' &>/dev/null; then
    print_success "Certbot installé sur le VPS"
else
    print_warning "Certbot non installé sur le VPS"
fi

# Vérification de l'espace disque VPS
print_status "Vérification de l'espace disque sur le VPS..."

DISK_USAGE=$(ssh -o ConnectTimeout=10 $VPS_USER@$VPS_IP 'df -h / | tail -1 | awk "{print \$5}"' 2>/dev/null | sed 's/%//')

if [ ! -z "$DISK_USAGE" ]; then
    if [ "$DISK_USAGE" -lt 80 ]; then
        print_success "Espace disque suffisant (${DISK_USAGE}% utilisé)"
    else
        print_warning "Espace disque faible (${DISK_USAGE}% utilisé)"
    fi
else
    print_warning "Impossible de vérifier l'espace disque"
fi

# Vérification des ports
print_status "Vérification des ports sur le VPS..."

PORTS_TO_CHECK=("80" "443" "22")
for port in "${PORTS_TO_CHECK[@]}"; do
    if ssh -o ConnectTimeout=10 $VPS_USER@$VPS_IP "sudo netstat -tlnp | grep :$port" &>/dev/null; then
        print_success "Port $port ouvert"
    else
        print_warning "Port $port non ouvert ou service non démarré"
    fi
done

# Résumé et recommandations
echo ""
print_status "Résumé de la préparation"
echo "========================="

echo ""
echo "📋 Checklist avant déploiement :"
echo ""
echo "✅ Fichiers du projet :"
echo "   - Code source Next.js"
echo "   - Configuration Prisma"
echo "   - Dockerfile.simple"
echo "   - Configurations (next, tailwind, typescript)"
echo ""
echo "🌐 Configuration DNS :"
echo "   - $DOMAIN → $VPS_IP"
echo "   - www.$DOMAIN → $VPS_IP"
echo ""
echo "🖥️ VPS prêt :"
echo "   - Docker et Docker Compose installés"
echo "   - Nginx configuré"
echo "   - Certbot pour SSL"
echo "   - Accès SSH fonctionnel"
echo ""
echo "🚀 Commande de déploiement :"
echo "   ./deploy-to-vps.sh"
echo ""
echo "📚 Documentation :"
echo "   - Voir GUIDE-DEPLOIEMENT-VPS.md"
echo ""

# Vérification finale
if [ ${#missing_files[@]} -eq 0 ]; then
    print_success "Préparation terminée - Prêt pour le déploiement !"
    echo ""
    echo "🎯 Prochaines étapes :"
    echo "1. Exécuter : ./deploy-to-vps.sh"
    echo "2. Attendre la fin du déploiement"
    echo "3. Tester : https://$DOMAIN"
else
    print_error "Préparation incomplète - Corrigez les erreurs avant de déployer"
fi
