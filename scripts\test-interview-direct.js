#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testInterviewDirect() {
  console.log('🧪 Test Direct API Entretiens - Karma Com Solidarité')
  console.log('='.repeat(60))

  try {
    // Test 1: Créer un bénévole de test
    console.log('\n1. Création d\'un bénévole de test...')
    const testVolunteer = await prisma.volunteerApplication.create({
      data: {
        firstName: 'Marie',
        lastName: 'Test',
        email: '<EMAIL>',
        phoneNumber: '06 12 34 56 78',
        currentStatus: 'En poste',
        contributionPole: 'IT et Transformation Numérique',
        weeklyHours: 'Entre 5 et 10 heures par semaine',
        availability: 'Soirée (18h à 22h)',
        participationRhythm: 'Régulier (chaque semaine)',
        howDidYouKnowUs: 'Site internet de Karma Com Solidarité',
        status: 'PENDING'
      }
    })
    console.log('✅ Bénévole créé:', testVolunteer.id)

    // Test 2: Simuler l'appel API directement
    console.log('\n2. Simulation appel API création entretien...')
    
    const interviewData = {
      title: 'Entretien Découverte - Marie Test',
      description: 'Entretien de découverte pour candidature bénévole',
      candidateId: testVolunteer.id,
      scheduledAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      type: 'DISCOVERY'
    }
    
    console.log('Données entretien:', interviewData)

    // Test 3: Vérifier que le bénévole existe
    console.log('\n3. Vérification existence bénévole...')
    const volunteerCheck = await prisma.volunteerApplication.findUnique({
      where: { id: testVolunteer.id }
    })
    
    if (volunteerCheck) {
      console.log('✅ Bénévole trouvé:', volunteerCheck.firstName, volunteerCheck.lastName)
    } else {
      console.log('❌ Bénévole non trouvé')
      return
    }

    // Test 4: Vérifier s'il y a déjà un utilisateur avec cet email
    console.log('\n4. Vérification utilisateur existant...')
    const existingUser = await prisma.user.findUnique({
      where: { email: volunteerCheck.email }
    })
    
    if (existingUser) {
      console.log('⚠️ Utilisateur existant trouvé:', existingUser.id)
    } else {
      console.log('✅ Aucun utilisateur existant avec cet email')
    }

    // Test 5: Créer un admin si nécessaire
    console.log('\n5. Vérification/création admin...')
    let adminUser = await prisma.user.findFirst({
      where: { userType: 'HR_ADMIN' }
    })

    if (!adminUser) {
      console.log('🔄 Création admin par défaut...')
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin Karma Com',
          password: 'admin123',
          userType: 'HR_ADMIN',
          profile: {
            create: {
              firstName: 'Admin',
              lastName: 'Karma Com',
              membershipStatus: 'ACTIVE'
            }
          }
        }
      })
      console.log('✅ Admin créé:', adminUser.id)
    } else {
      console.log('✅ Admin existant trouvé:', adminUser.id)
    }

    // Test 6: Créer utilisateur temporaire pour bénévole si nécessaire
    console.log('\n6. Création utilisateur temporaire pour bénévole...')
    let candidateUser = existingUser
    
    if (!candidateUser) {
      try {
        candidateUser = await prisma.user.create({
          data: {
            email: volunteerCheck.email,
            name: `${volunteerCheck.firstName} ${volunteerCheck.lastName}`,
            phone: volunteerCheck.phoneNumber,
            password: 'temp_password',
            userType: 'VOLUNTEER',
            profile: {
              create: {
                firstName: volunteerCheck.firstName,
                lastName: volunteerCheck.lastName,
                membershipStatus: 'PENDING'
              }
            }
          }
        })
        console.log('✅ Utilisateur temporaire créé:', candidateUser.id)
      } catch (error) {
        console.error('❌ Erreur création utilisateur temporaire:', error.message)
        return
      }
    }

    // Test 7: Créer l'entretien
    console.log('\n7. Création de l\'entretien...')
    try {
      const interview = await prisma.appointment.create({
        data: {
          title: interviewData.title,
          description: interviewData.description,
          candidateId: candidateUser.id,
          scheduledAt: new Date(interviewData.scheduledAt),
          type: interviewData.type,
          status: 'SCHEDULED',
          createdById: adminUser.id
        },
        include: {
          candidate: {
            include: {
              profile: true
            }
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })
      
      console.log('✅ Entretien créé avec succès:', interview.id)
      console.log('   - Candidat:', interview.candidate.name)
      console.log('   - Date:', interview.scheduledAt)
      console.log('   - Type:', interview.type)
      
    } catch (error) {
      console.error('❌ Erreur création entretien:', error.message)
      console.error('   Stack:', error.stack)
      return
    }

    // Test 8: Mettre à jour le statut du bénévole
    console.log('\n8. Mise à jour statut bénévole...')
    await prisma.volunteerApplication.update({
      where: { id: testVolunteer.id },
      data: { status: 'INTERVIEW_SCHEDULED' }
    })
    console.log('✅ Statut bénévole mis à jour')

    // Test 9: Nettoyage
    console.log('\n9. Nettoyage des données de test...')
    
    // Supprimer les entretiens
    await prisma.appointment.deleteMany({
      where: {
        candidate: {
          email: '<EMAIL>'
        }
      }
    })
    
    // Supprimer l'utilisateur temporaire
    await prisma.user.deleteMany({
      where: {
        email: '<EMAIL>'
      }
    })
    
    // Supprimer le bénévole
    await prisma.volunteerApplication.deleteMany({
      where: {
        email: '<EMAIL>'
      }
    })
    
    console.log('✅ Données de test supprimées')

    console.log('\n🎉 Test direct réussi!')
    console.log('\n📋 Résumé:')
    console.log('   - Création bénévole: ✅ OK')
    console.log('   - Vérification existence: ✅ OK')
    console.log('   - Création utilisateur temporaire: ✅ OK')
    console.log('   - Création entretien: ✅ OK')
    console.log('   - Mise à jour statut: ✅ OK')
    console.log('\n🚀 L\'API devrait fonctionner correctement!')

  } catch (error) {
    console.error('❌ Erreur lors du test direct:', error)
    console.error('Stack:', error.stack)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Exécuter le test
testInterviewDirect()
