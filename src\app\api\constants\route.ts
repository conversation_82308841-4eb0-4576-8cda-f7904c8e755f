import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const [
      organizationTypes,
      sectors,
      partnershipTypes,
      skills,
      departments
    ] = await Promise.all([
      prisma.organizationType.findMany({
        where: { isActive: true },
        orderBy: { order: 'asc' },
        select: { id: true, name: true }
      }),
      prisma.sector.findMany({
        where: { isActive: true },
        orderBy: { order: 'asc' },
        select: { id: true, name: true }
      }),
      prisma.partnershipType.findMany({
        where: { isActive: true },
        orderBy: { order: 'asc' },
        select: { id: true, name: true }
      }),
      prisma.skill.findMany({
        where: { isActive: true },
        orderBy: { order: 'asc' },
        select: { id: true, name: true, category: true }
      }),
      prisma.departmentOption.findMany({
        where: { isActive: true },
        orderBy: { order: 'asc' },
        select: { id: true, name: true }
      })
    ])

    // Grouper les compétences par catégorie
    const skillsByCategory = skills.reduce((acc, skill) => {
      const category = skill.category || 'Autre'
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push({
        id: skill.id,
        name: skill.name
      })
      return acc
    }, {} as Record<string, Array<{id: string, name: string}>>)

    // Options de disponibilité (statiques pour l'instant)
    const availabilityOptions = [
      'Quelques heures par semaine',
      'Une demi-journée par semaine',
      'Une journée par semaine',
      'Plusieurs jours par semaine',
      'Ponctuellement selon les projets',
      'Disponibilité flexible'
    ]

    // Options de taille d'entreprise (statiques)
    const employeeCountOptions = [
      '1-10',
      '11-50',
      '51-200',
      '201-500',
      '500+'
    ]

    // Options de budget (statiques)
    const budgetOptions = [
      '< 5 000€',
      '5 000€ - 15 000€',
      '15 000€ - 50 000€',
      '50 000€ - 100 000€',
      '> 100 000€'
    ]

    return NextResponse.json({
      organizationTypes,
      sectors,
      partnershipTypes,
      skills,
      skillsByCategory,
      departments,
      availabilityOptions,
      employeeCountOptions,
      budgetOptions
    })
  } catch (error) {
    console.error('Erreur lors de la récupération des constantes:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
