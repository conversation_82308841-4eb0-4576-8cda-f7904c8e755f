import { useState, useCallback } from 'react'
import { RecentApplication } from '../utils/constants'

export const useCandidates = () => {
  const [candidates, setCandidates] = useState<RecentApplication[]>([])
  const [candidatesLoading, setCandidatesLoading] = useState(false)
  const [recentApplicationsPage, setRecentApplicationsPage] = useState(1)
  const [recentApplicationsTotal, setRecentApplicationsTotal] = useState(0)
  const recentApplicationsLimit = 10

  // Charger les candidats
  const loadCandidates = useCallback(async (page = 1, limit = 50) => {
    try {
      setCandidatesLoading(true)
      console.log('👥 Chargement des candidats...')
      const response = await fetch(`/api/candidates?page=${page}&limit=${limit}`)
      if (response.ok) {
        const data = await response.json()
        console.log('👥 Candidats chargés:', data.candidates.length, 'candidats')
        setCandidates(data.candidates)
        setRecentApplicationsTotal(data.pagination.total)
      } else {
        console.error('❌ Erreur chargement candidats:', response.status)
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des candidats:', error)
    } finally {
      setCandidatesLoading(false)
    }
  }, [])

  // Mettre à jour le statut d'un candidat
  const handleUpdateCandidateStatus = async (candidateId: string, newStatus: string, onSuccess?: () => void, onError?: (error: string) => void) => {
    try {
      console.log(`🔄 Mise à jour statut candidat ${candidateId}: ${newStatus}`)

      const response = await fetch(`/api/candidates/${candidateId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        const updatedCandidate = await response.json()
        console.log('✅ Statut mis à jour:', updatedCandidate)

        // Recharger les candidats
        await loadCandidates()

        if (onSuccess) {
          onSuccess()
        }

      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ Erreur API:', errorData)
        throw new Error(errorData.error || 'Erreur lors de la mise à jour du statut')
      }
    } catch (error) {
      console.error('❌ Erreur mise à jour statut:', error)
      if (onError) {
        onError(error instanceof Error ? error.message : 'Impossible de mettre à jour le statut')
      }
    }
  }

  return {
    candidates,
    candidatesLoading,
    recentApplicationsPage,
    setRecentApplicationsPage,
    recentApplicationsTotal,
    recentApplicationsLimit,
    loadCandidates,
    handleUpdateCandidateStatus
  }
}
