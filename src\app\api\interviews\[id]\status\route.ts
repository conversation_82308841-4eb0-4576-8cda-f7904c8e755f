import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { status } = await request.json()
    const interviewId = params.id

    // Validation du statut
    const validStatuses = ['SCHEDULED', 'CONFIRMED', 'COMPLETED', 'CANCELLED']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Statut invalide' },
        { status: 400 }
      )
    }

    // Vérifier que l'entretien existe
    const existingInterview = await prisma.appointment.findUnique({
      where: { id: interviewId },
      include: {
        candidate: true,
        createdBy: true
      }
    })

    if (!existingInterview) {
      return NextResponse.json(
        { error: 'Entretien non trouvé' },
        { status: 404 }
      )
    }

    // Mettre à jour le statut
    const updatedInterview = await prisma.appointment.update({
      where: { id: interviewId },
      data: {
        status: status as any
      },
      include: {
        candidate: true,
        createdBy: true
      }
    })

    // Formater la réponse
    const formattedInterview = {
      id: updatedInterview.id,
      title: updatedInterview.title,
      description: updatedInterview.description,
      candidateId: updatedInterview.candidateId,
      candidateName: updatedInterview.candidate.name,
      candidateEmail: updatedInterview.candidate.email,
      scheduledAt: updatedInterview.scheduledAt.toISOString(),
      type: updatedInterview.type,
      status: updatedInterview.status,
      notes: updatedInterview.notes,
      createdAt: updatedInterview.createdAt.toISOString(),
      createdBy: {
        id: updatedInterview.createdBy.id,
        name: updatedInterview.createdBy.name,
        email: updatedInterview.createdBy.email
      }
    }

    return NextResponse.json(formattedInterview)
  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut de l\'entretien:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
