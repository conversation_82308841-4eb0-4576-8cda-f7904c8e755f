#!/bin/bash

# Variables de Configuration
NEW_USER="vpsadmin"
USER_PASSWORD="Besmillah2025" # !!! ATTENTION: Changez ce mot de passe IMMÉDIATEMENT après l'installation !!!
YOUR_EMAIL="<EMAIL>" # Votre adresse email pour Let's Encrypt
DOMAIN_NAME="zidani.org"
PUBLIC_IP="*************" # Votre adresse IP
N8N_SUBDOMAIN="n8n.$DOMAIN_NAME" # Sous-domaine pour n8n
SUPABASE_SUBDOMAIN="supabase.$DOMAIN_NAME" # Sous-domaine pour Supabase (si vous l'auto-hébergez ou pour une autre utilité)

# --- Fonctions utilitaires pour le logging ---
log_info() {
    echo -e "\n\033[0;32m[INFO]\033[0m $1"
}

log_error() {
    echo -e "\n\033[0;31m[ERROR]\033[0m $1" >&2
    exit 1
}

# --- Vérification des Prérequis ---
if [[ $EUID -ne 0 ]]; then
   log_error "Ce script doit être exécuté en tant que root."
fi

# --- 1. Mise à jour du Système et Installation des Outils de Base ---
log_info "Mise à jour du système et installation des outils de base (build-essential, unzip, curl, wget, ca-certificates, gnupg, etc.)..."
apt update -y && apt upgrade -y || log_error "Échec de la mise à jour du système."
apt autoremove -y
# Inclure build-essential ici, essentiel pour les dépendances npm/node-gyp
apt install -y unzip curl wget ca-certificates gnupg software-properties-common apt-transport-https build-essential || log_error "Échec de l'installation des outils utilitaires et build-essential."

# --- 2. Configuration de l'Utilisateur non-root et SSH ---
log_info "Configuration de l'utilisateur '$NEW_USER' et de SSH..."

# Vérifier si l'utilisateur existe déjà
if id "$NEW_USER" &>/dev/null; then
    log_info "L'utilisateur '$NEW_USER' existe déjà. Saut de la création de l'utilisateur."
    # Tuer les processus de l'ancien utilisateur si le script est relancé et qu'ils sont actifs
    log_info "Tentative de tuer les processus de l'utilisateur '$NEW_USER' si existants..."
    pkill -9 -u "$NEW_USER" &>/dev/null || true # Ignorer les erreurs si aucun processus
else
    log_info "Création de l'utilisateur '$NEW_USER'..."
    adduser --disabled-password --gecos "" "$NEW_USER" || log_error "Échec de la création de l'utilisateur."
    echo "$NEW_USER:$USER_PASSWORD" | chpasswd || log_error "Échec de la définition du mot de passe pour l'utilisateur."
    usermod -aG sudo "$NEW_USER" || log_error "Échec d'ajout de l'utilisateur au groupe sudo."

    # Création du répertoire .ssh et du fichier authorized_keys pour le nouvel utilisateur
    mkdir -p "/home/<USER>/.ssh" || log_error "Échec de la création du répertoire .ssh."
    touch "/home/<USER>/.ssh/authorized_keys" || log_error "Échec de la création du fichier authorized_keys."
    chmod 700 "/home/<USER>/.ssh" || log_error "Échec du changement de permissions sur .ssh."
    chmod 600 "/home/<USER>/.ssh/authorized_keys" || log_error "Échec du changement de permissions sur authorized_keys."
    chown -R "$NEW_USER":"$NEW_USER" "/home/<USER>/.ssh" || log_error "Échec du changement de propriétaire sur .ssh."

    # Copier la clé SSH publique de root (si elle existe) ou demander à l'utilisateur de la coller
    if [ -f "/root/.ssh/authorized_keys" ]; then
        cat "/root/.ssh/authorized_keys" >> "/home/<USER>/.ssh/authorized_keys"
        log_info "Clé SSH de root copiée pour l'utilisateur '$NEW_USER'."
    else
        log_info "ATTENTION: Aucune clé SSH trouvée pour root. Vous DEVEZ copier votre clé publique SSH dans /home/<USER>/.ssh/authorized_keys MANUELLEMENT."
        log_info "Après avoir exécuté ce script, déconnectez-vous de root et connectez-vous avec l'utilisateur '$NEW_USER' et le mot de passe fourni ou votre clé SSH."
        read -p "Appuyez sur Entrée pour continuer (après avoir pris note de copier votre clé SSH)..."
    fi
fi

# Configuration du démon SSH
log_info "Configuration du démon SSH..."
SSH_CONFIG="/etc/ssh/sshd_config"
cp "$SSH_CONFIG" "${SSH_CONFIG}.bak" # Sauvegarde du fichier original

# Utiliser sed pour modifier les lignes existantes ou ajouter si elles n'existent pas
sed -i 's/^PermitRootLogin yes/PermitRootLogin no/' "$SSH_CONFIG"
sed -i 's/^#PermitRootLogin yes/PermitRootLogin no/' "$SSH_CONFIG"
sed -i 's/^PasswordAuthentication yes/PasswordAuthentication no/' "$SSH_CONFIG"
sed -i 's/^#PasswordAuthentication yes/PasswordAuthentication no/' "$SSH_CONFIG"

# Supprimer les lignes AllowUsers précédentes pour éviter les doublons avant d'ajouter la nouvelle
sed -i '/^AllowUsers/d' "$SSH_CONFIG"
echo "AllowUsers $NEW_USER" >> "$SSH_CONFIG" # Autorise seulement le nouvel utilisateur

systemctl restart ssh || log_error "Échec du redémarrage du service SSH. Vérifiez le service 'ssh'."
log_info "SSH configuré. Veuillez vous déconnecter et vous reconnecter avec l'utilisateur '$NEW_USER' et sa clé SSH ou son mot de passe."

# --- 3. Installation de Git ---
log_info "Installation de Git..."
apt install -y git || log_error "Échec de l'installation de Git."

# --- 4. Installation de Nginx ---
log_info "Installation de Nginx..."
apt install -y nginx || log_error "Échec de l'installation de Nginx."
systemctl enable nginx
systemctl start nginx

# Nettoyage des anciens liens Nginx (important pour la robustesse du script)
log_info "Nettoyage des anciens liens Nginx pour $DOMAIN_NAME, $N8N_SUBDOMAIN, $SUPABASE_SUBDOMAIN et le default..."
rm -f "/etc/nginx/sites-enabled/$DOMAIN_NAME"
rm -f "/etc/nginx/sites-enabled/$N8N_SUBDOMAIN"
rm -f "/etc/nginx/sites-enabled/$SUPABASE_SUBDOMAIN"
rm -f "/etc/nginx/sites-enabled/default" # S'assurer que le lien par défaut est supprimé avant de le recréer si nécessaire

# Configuration de Nginx pour zidani.org (fichier de base)
NGINX_MAIN_CONF_PATH="/etc/nginx/sites-available/$DOMAIN_NAME"
cat <<EOF > "$NGINX_MAIN_CONF_PATH"
server {
    listen 80;
    listen [::]:80;

    server_name $DOMAIN_NAME www.$DOMAIN_NAME;

    root /var/www/$DOMAIN_NAME;
    index index.html index.htm index.nginx-debian.html;

    location / {
        try_files \$uri \$uri/ =404;
    }
}
EOF

mkdir -p "/var/www/$DOMAIN_NAME" || log_error "Échec de la création du répertoire web pour le domaine."
echo "<h1>Bienvenue sur $DOMAIN_NAME !</h1>" > "/var/www/$DOMAIN_NAME/index.html"

ln -s "$NGINX_MAIN_CONF_PATH" "/etc/nginx/sites-enabled/$DOMAIN_NAME" || log_error "Échec de la création du lien symbolique Nginx pour le domaine principal."

# Recréer le lien par défaut de Nginx si absent et nécessaire
if [ ! -f "/etc/nginx/sites-enabled/default" ] && [ -f "/etc/nginx/sites-available/default" ]; then
    log_info "Recréation du lien symbolique Nginx 'default'..."
    ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default || log_error "Échec de la recréation du lien symbolique Nginx 'default'."
fi

nginx -t && systemctl restart nginx || log_error "Erreur de configuration Nginx ou échec du redémarrage après configuration du domaine principal."
log_info "Nginx installé et configuré pour $DOMAIN_NAME (port 80)."

# --- 5. Installation de Docker et Docker Compose ---
log_info "Installation de Docker et Docker Compose..."
# Supprimer les versions précédentes pour éviter les conflits
for pkg in docker.io docker-doc docker-compose docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin docker-engine; do sudo apt remove -y $pkg; done

# Ajouter les dépôts Docker officiels
install -m 0755 -d /etc/apt/keyrings || log_error "Échec de la création du répertoire keyrings."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc || log_error "Échec du téléchargement de la clé GPG de Docker."
chmod a+r /etc/apt/keyrings/docker.asc || log_error "Échec du changement de permissions sur la clé GPG."

echo \
  "deb [arch=\"$(dpkg --print-architecture)\" signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  \"$(. /etc/os-release && echo \"$VERSION_CODENAME\")\" stable" | \
  tee /etc/apt/sources.list.d/docker.list > /dev/null || log_error "Échec de l'ajout du dépôt Docker."

apt update -y || log_error "Échec de la mise à jour des dépôts après ajout de Docker."

apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin || log_error "Échec de l'installation de Docker."

# Ajouter l'utilisateur au groupe docker pour éviter d'utiliser sudo
usermod -aG docker "$NEW_USER" || log_error "Échec d'ajout de l'utilisateur au groupe docker."
log_info "Docker et Docker Compose installés. Vous devrez peut-être vous déconnecter/reconnecter pour que les changements de groupe prennent effet pour '$NEW_USER'."

# --- 6. Installation de Node.js et NPM (via NVM pour plus de flexibilité) ---
log_info "Installation de Node.js et NPM via NVM..."
# Installer NVM pour le nouvel utilisateur
# Exécuter les commandes NVM sous l'utilisateur $NEW_USER
su - "$NEW_USER" -c "curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash" || log_error "Échec de l'installation de NVM."

# Charger NVM et installer la dernière version LTS de Node.js
su - "$NEW_USER" -c 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && nvm install --lts' || log_error "Échec de l'installation de Node.js via NVM."
su - "$NEW_USER" -c 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && nvm use --lts' || log_error "Échec de la sélection de la version LTS de Node.js."

log_info "Node.js et NPM installés (via NVM pour l'utilisateur '$NEW_USER')."

# --- 7. Installation de n8n et PM2 ---
log_info "Installation de n8n globalement et PM2..."

# Nettoyer le cache NPM avant l'installation de n8n
su - "$NEW_USER" -c 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && npm cache clean --force' || log_error "Échec du nettoyage du cache NPM."

# Installer n8n globalement avec NPM pour l'utilisateur non-root
su - "$NEW_USER" -c 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && npm install -g n8n' || log_error "Échec de l'installation de n8n. Vérifiez les dépendances de compilation."

# Installer PM2 pour gérer n8n en arrière-plan
su - "$NEW_USER" -c 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && npm install -g pm2' || log_error "Échec de l'installation de PM2."

# Démarrer n8n avec PM2 pour l'utilisateur non-root
log_info "Démarrage de n8n avec PM2..."
# S'assurer que n8n n'est pas déjà en cours d'exécution via PM2
su - "$NEW_USER" -c 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && pm2 delete n8n' &>/dev/null || true # Supprimer l'ancienne instance si elle existe

su - "$NEW_USER" -c 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && pm2 start n8n --name n8n' || log_error "Échec du démarrage de n8n avec PM2."

# Générer la commande de démarrage PM2 et l'exécuter en tant que root
log_info "Génération et exécution de la commande de démarrage automatique PM2..."
PM2_STARTUP_CMD=$(su - "$NEW_USER" -c 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && pm2 startup systemd' | grep "sudo env PATH")

if [ -z "$PM2_STARTUP_CMD" ]; then
    log_error "Impossible de générer la commande de démarrage PM2. Vérifiez l'installation de PM2."
fi

# Exécuter la commande PM2 générée en tant que root
eval "$PM2_STARTUP_CMD" || log_error "Échec de l'exécution de la commande de démarrage PM2."

# S'assurer que PM2 save est exécuté après startup, car startup peut réinitialiser la liste
su - "$NEW_USER" -c 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && pm2 save' || log_error "Échec de la sauvegarde de la configuration PM2 après démarrage automatique."

log_info "n8n installé et démarré avec PM2. Accès par défaut sur le port 5678."

# Configuration Nginx pour proxy_pass n8n
NGINX_N8N_CONF_PATH="/etc/nginx/sites-available/$N8N_SUBDOMAIN"
cat <<EOF > "$NGINX_N8N_CONF_PATH"
server {
    listen 80;
    server_name $N8N_SUBDOMAIN;

    location / {
        proxy_pass http://localhost:5678;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
ln -s "$NGINX_N8N_CONF_PATH" "/etc/nginx/sites-enabled/$N8N_SUBDOMAIN" || log_error "Échec de la création du lien symbolique Nginx pour n8n."

# Configuration Nginx pour proxy_pass Supabase (exemple, adaptez le port si nécessaire)
# Si Supabase n'est pas auto-hébergé sur ce VPS avec un service exposé sur un port, cette section pourrait être ajustée
# Pour l'exemple, nous allons juste créer un bloc Nginx simple
NGINX_SUPABASE_CONF_PATH="/etc/nginx/sites-available/$SUPABASE_SUBDOMAIN"
cat <<EOF > "$NGINX_SUPABASE_CONF_PATH"
server {
    listen 80;
    server_name $SUPABASE_SUBDOMAIN;

    location / {
        # Si Supabase est auto-hébergé localement sur un port spécifique (ex: 8000), utilisez:
        # proxy_pass http://localhost:8000; 
        # Sinon, pour une redirection vers l'URL officielle ou un message simple:
        return 302 https://supabase.com; # Redirection vers le site officiel
        # ou un message simple pour indiquer que c'est un point d'entrée pour la CLI par exemple
        # root /var/www/$SUPABASE_SUBDOMAIN;
        # index index.html;
    }
}
EOF
mkdir -p "/var/www/$SUPABASE_SUBDOMAIN" # Créer le répertoire même si c'est une redirection
echo "<h1>Supabase CLI endpoint for $SUPABASE_SUBDOMAIN</h1>" > "/var/www/$SUPABASE_SUBDOMAIN/index.html" # Contenu simple
ln -s "$NGINX_SUPABASE_CONF_PATH" "/etc/nginx/sites-enabled/$SUPABASE_SUBDOMAIN" || log_error "Échec de la création du lien symbolique Nginx pour Supabase."


nginx -t && systemctl restart nginx || log_error "Erreur de configuration Nginx ou échec du redémarrage."
log_info "Nginx configuré pour proxifier n8n (sur $N8N_SUBDOMAIN) et Supabase (sur $SUPABASE_SUBDOMAIN)."
log_info "N'oubliez pas de créer les enregistrements DNS A pour $N8N_SUBDOMAIN et $SUPABASE_SUBDOMAIN pointant vers $PUBLIC_IP."

# --- 8. Configuration SSL avec Certbot (Let's Encrypt) ---
log_info "Installation de Certbot pour SSL..."
apt install -y certbot python3-certbot-nginx || log_error "Échec de l'installation de Certbot."

log_info "Génération des certificats SSL pour $DOMAIN_NAME, $N8N_SUBDOMAIN et $SUPABASE_SUBDOMAIN..."
# Tenter d'obtenir les certificats. Si déjà existants, Certbot les gérera.
certbot --nginx --agree-tos --redirect --hsts --staple-ocsp --email "$YOUR_EMAIL" -d "$DOMAIN_NAME" -d "www.$DOMAIN_NAME" -d "$N8N_SUBDOMAIN" -d "$SUPABASE_SUBDOMAIN" || log_error "Échec de la génération des certificats SSL."

log_info "Certificats SSL générés et configurés. Le renouvellement automatique est en place."

# --- 9. Installation de Kubernetes (kubeadm, kubelet, kubectl) ---
log_info "Installation des composants Kubernetes (kubeadm, kubelet, kubectl)..."

# Désactiver le swap (prérequis pour Kubernetes)
log_info "Désactivation du swap (prérequis Kubernetes)..."
swapoff -a
# S'assurer que le swap est désactivé au démarrage
sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab

# Charger les modules du noyau
log_info "Chargement des modules du noyau pour Kubernetes..."
cat <<EOF | tee /etc/modules-load.d/k8s.conf
overlay
br_netfilter
EOF

modprobe overlay
modprobe br_netfilter

# Configurer les paramètres réseau du sysctl
log_info "Configuration des paramètres sysctl pour Kubernetes..."
cat <<EOF | tee /etc/sysctl.d/k8s.conf
net.bridge.bridge-nf-call-iptables  = 1
net.bridge.bridge-nf-call-ip6tables = 1
net.ipv4.ip_forward                 = 1
EOF

sysctl --system || log_error "Échec de la configuration sysctl."

# Ajouter le dépôt Kubernetes
log_info "Ajout du dépôt Kubernetes..."
curl -fsSL https://pkgs.k8s.io/core:/stable:/v1.29/deb/Release.key | gpg --dearmor -o /etc/apt/keyrings/kubernetes-apt-keyring.gpg || log_error "Échec du téléchargement de la clé GPG Kubernetes."

echo "deb [signed-by=/etc/apt/keyrings/kubernetes-apt-keyring.gpg] https://pkgs.k8s.io/core:/stable:/v1.29/deb/ /" | tee /etc/apt/sources.list.d/kubernetes.list > /dev/null || log_error "Échec de l'ajout du dépôt Kubernetes."

apt update -y || log_error "Échec de la mise à jour des dépôts après ajout de Kubernetes."
apt install -y kubelet kubeadm kubectl || log_error "Échec de l'installation de Kubernetes components."
apt-mark hold kubelet kubeadm kubectl || log_error "Échec du verrouillage des paquets Kubernetes."

log_info "Composants Kubernetes installés."
log_info "L'initialisation d'un cluster Kubernetes est une étape manuelle qui nécessite des décisions sur le réseau des Pods (ex: --pod-network-cidr=10.244.0.0/16 pour Flannel)."
log_info "Pour initialiser le master node, exécutez (après avoir choisi votre CIDR de réseau Pod):"
log_info "sudo kubeadm init --pod-network-cidr=10.244.0.0/16"
log_info "Après l'initialisation, suivez les instructions à l'écran pour configurer kubectl et joindre d'autres nœuds."

# --- 10. Installation de Supabase CLI ---
log_info "Installation de Supabase CLI..."

# Ajouter la clé GPG de Supabase
if [ ! -f /etc/apt/keyrings/supabase-cli.asc ]; then
    curl -fsSL https://pkgs.supabase.dev/debian/apt.asc | gpg --dearmor -o /etc/apt/keyrings/supabase-cli.asc || log_error "Échec du téléchargement de la clé GPG de Supabase CLI."
fi

# Ajouter le dépôt de Supabase
if [ ! -f /etc/apt/sources.list.d/supabase-cli.list ]; then
    echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/supabase-cli.asc] https://pkgs.supabase.dev/debian stable main" | tee /etc/apt/sources.list.d/supabase-cli.list > /dev/null || log_error "Échec de l'ajout du dépôt Supabase CLI."
fi

apt update -y || log_error "Échec de la mise à jour des dépôts après ajout de Supabase CLI."
apt install -y supabase || log_error "Échec de l'installation de Supabase CLI."

log_info "Supabase CLI installé."
log_info "Vous pouvez exécuter 'supabase help' pour voir les commandes disponibles."


# --- Fin du script ---
log_info "Installation terminée !"
log_info "Récapitulatif :"
log_info " - Nouvel utilisateur : $NEW_USER (mot de passe: $USER_PASSWORD - CHANGEZ-LE RAPIDEMENT !)"
log_info " - Nginx : Configuré pour $DOMAIN_NAME (https://zidani.org), $N8N_SUBDOMAIN (https://n8n.zidani.org) et $SUPABASE_SUBDOMAIN (https://supabase.zidani.org)."
log_info " - Docker & Docker Compose : Installés. L'utilisateur '$NEW_USER' fait partie du groupe docker."
log_info " - Node.js & NPM : Installés via NVM pour l'utilisateur '$NEW_USER'."
log_info " - n8n : Installé et démarré avec PM2, accessible via https://$N8N_SUBDOMAIN."
log_info " - SSL : Certificats Let's Encrypt pour les trois domaines sont en place."
log_info " - Git : Installé."
log_info " - Kubernetes : kubeadm, kubelet, kubectl installés. Initialisation manuelle requise."
log_info " - Supabase CLI : Installé."

log_info "Veuillez vous déconnecter de la session root et vous reconnecter avec l'utilisateur '$NEW_USER' pour que les changements de groupe (Docker, NVM) prennent effet."
log_info "N'oubliez pas de configurer les enregistrements DNS A pour $DOMAIN_NAME, www.$DOMAIN_NAME, $N8N_SUBDOMAIN et $SUPABASE_SUBDOMAIN chez votre registraire."