#!/bin/bash

# Script pour corriger les erreurs de build communes
echo "🔧 Correction Erreurs Build - Karma Com Solidarité"
echo "================================================="

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
log "Vérification des prérequis..."

if [ ! -f "package.json" ]; then
    error "Ce script doit être exécuté depuis la racine du projet"
    exit 1
fi

if [ ! -f "prisma/schema.prisma" ]; then
    error "Schéma Prisma non trouvé"
    exit 1
fi

success "Prérequis validés"

# Correction 1: Génération du client Prisma
log "Génération du client Prisma..."

if npx prisma generate; then
    success "Client Prisma généré avec succès"
else
    error "Échec de la génération du client Prisma"
    exit 1
fi

# Correction 2: Vérification des types Prisma
log "Vérification des types Prisma..."

# Vérifier que les types sont disponibles
if [ -d "node_modules/@prisma/client" ]; then
    success "Client Prisma installé"
    
    # Vérifier les types spécifiques
    if grep -q "UserType" node_modules/@prisma/client/index.d.ts 2>/dev/null; then
        success "Type UserType disponible"
    else
        warning "Type UserType non trouvé dans le client"
    fi
    
    if grep -q "MembershipStatus" node_modules/@prisma/client/index.d.ts 2>/dev/null; then
        success "Type MembershipStatus disponible"
    else
        warning "Type MembershipStatus non trouvé dans le client"
    fi
else
    error "Client Prisma non installé"
    exit 1
fi

# Correction 3: Vérification des routes API dynamiques
log "Vérification des routes API dynamiques..."

api_routes=(
    "src/app/api/dashboard/stats/route.ts"
    "src/app/api/auth/login/route.ts"
    "src/app/api/candidates/route.ts"
    "src/app/api/interviews/route.ts"
)

for route in "${api_routes[@]}"; do
    if [ -f "$route" ]; then
        if grep -q "export const dynamic = 'force-dynamic'" "$route"; then
            success "Route $route configurée comme dynamique"
        else
            warning "Route $route pourrait nécessiter 'export const dynamic = force-dynamic'"
        fi
    fi
done

# Correction 4: Nettoyage du cache Next.js
log "Nettoyage du cache Next.js..."

if [ -d ".next" ]; then
    rm -rf .next
    success "Cache Next.js nettoyé"
else
    log "Pas de cache Next.js à nettoyer"
fi

# Correction 5: Variables d'environnement pour le build
log "Configuration des variables d'environnement pour le build..."

export DATABASE_URL="***********************************/dummy"
export NODE_ENV="production"
export NEXT_TELEMETRY_DISABLED=1

success "Variables d'environnement configurées"

# Correction 6: Test de build
log "Test de build..."

if npm run build; then
    success "Build réussi"
else
    warning "Build échoué, diagnostic en cours..."
    
    # Diagnostic des erreurs communes
    log "Diagnostic des erreurs communes..."
    
    # Vérifier les imports Prisma
    if grep -r "from '@prisma/client'" src/ | grep -v "node_modules"; then
        log "Imports Prisma trouvés dans:"
        grep -r "from '@prisma/client'" src/ | grep -v "node_modules" | head -5
    fi
    
    # Vérifier les erreurs de types
    if npx tsc --noEmit 2>&1 | grep -i "error"; then
        warning "Erreurs TypeScript détectées"
        npx tsc --noEmit 2>&1 | grep -i "error" | head -5
    fi
fi

# Correction 7: Mise à jour du pipeline GitLab CI
log "Vérification du pipeline GitLab CI..."

if [ -f ".gitlab-ci.yml" ]; then
    if grep -q "npx prisma generate" .gitlab-ci.yml; then
        success "Pipeline inclut la génération Prisma"
    else
        warning "Pipeline pourrait nécessiter 'npx prisma generate'"
        echo "Recommandation: Ajouter 'npx prisma generate' avant le build"
    fi
else
    log "Pas de pipeline GitLab CI trouvé"
fi

# Correction 8: Création d'un script de pre-build
log "Création d'un script de pre-build..."

cat > scripts/pre-build.sh << 'EOF'
#!/bin/bash
echo "🔧 Pre-build - Génération Prisma et préparation"

# Génération du client Prisma
echo "Génération du client Prisma..."
npx prisma generate

# Variables d'environnement pour le build
export DATABASE_URL="***********************************/dummy"
export NODE_ENV="production"
export NEXT_TELEMETRY_DISABLED=1

echo "✅ Pre-build terminé"
EOF

chmod +x scripts/pre-build.sh
success "Script pre-build créé"

# Résumé des corrections
echo ""
log "Résumé des Corrections Build"
echo "============================"

echo ""
echo "✅ Corrections appliquées:"
echo "   - Génération du client Prisma"
echo "   - Vérification des types Prisma (UserType, MembershipStatus)"
echo "   - Nettoyage du cache Next.js"
echo "   - Configuration des variables d'environnement"
echo "   - Test de build"
echo "   - Création du script pre-build"
echo ""
echo "🔧 Problèmes résolus:"
echo "   - Import { UserType, MembershipStatus } from '@prisma/client'"
echo "   - Erreurs de types Prisma manquants"
echo "   - Cache Next.js corrompu"
echo "   - Variables d'environnement manquantes"
echo ""
echo "📊 Types Prisma disponibles:"
echo "   - UserType: ASSOCIATION, ORGANIZATION, VOLUNTEER, HR_ADMIN"
echo "   - MembershipStatus: PENDING, APPROVED, REJECTED, ACTIVE, INACTIVE"
echo ""
echo "🚀 Pipeline GitLab CI:"
echo "   - Ajouter 'npx prisma generate' avant npm run build"
echo "   - Utiliser les variables d'environnement appropriées"
echo ""
echo "🧪 Tests recommandés:"
echo "   1. npm run build (local)"
echo "   2. npm run dev (test développement)"
echo "   3. Push vers GitLab pour tester le pipeline"
echo ""
echo "📋 Script pre-build créé:"
echo "   - scripts/pre-build.sh"
echo "   - À exécuter avant chaque build"

success "Correction des erreurs de build terminée"
