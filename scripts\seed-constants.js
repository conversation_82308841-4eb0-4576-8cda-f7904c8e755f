const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Données constantes à insérer
const organizationTypes = [
  'Entreprise privée',
  'Institution publique',
  'Collectivité territoriale',
  'Établissement public',
  'Fondation',
  'Autre'
]

const sectors = [
  'Technologie',
  'Finance',
  'Santé',
  'Éducation',
  'Commerce',
  'Industrie',
  'Services',
  'Administration publique',
  'Autre'
]

const partnershipTypes = [
  'Mécénat financier',
  'Mécénat de compétences',
  'Partenariat stratégique',
  'Sponsoring événementiel',
  'Collaboration projet',
  'Autre'
]

const skills = [
  { name: 'Communication', category: 'Marketing & Communication' },
  { name: 'Marketing digital', category: 'Marketing & Communication' },
  { name: 'Développement web', category: 'Technologie' },
  { name: 'Design graphique', category: 'Créatif' },
  { name: 'Gestion de projet', category: 'Management' },
  { name: 'Comptabilité', category: 'Finance' },
  { name: 'Juridique', category: 'Juridique' },
  { name: 'Traduction', category: 'Langues' },
  { name: 'Rédaction', category: 'Communication' },
  { name: 'Photographie', category: 'Créatif' },
  { name: 'Vidéo', category: 'Créatif' },
  { name: 'Formation', category: 'Éducation' },
  { name: 'Événementiel', category: 'Organisation' },
  { name: 'Logistique', category: 'Organisation' },
  { name: 'Autre', category: 'Autre' }
]

const departments = [
  'RH/Asso',
  'Communication',
  'Partenariats & RSE',
  'IT',
  'Design',
  'Juridique',
  'Comptabilité',
  'Auto & AI'
]

async function seedConstants() {
  console.log('🌱 Initialisation des constantes dans la base de données...')
  
  try {
    // Seed Organization Types
    console.log('📋 Insertion des types d\'organisations...')
    for (let i = 0; i < organizationTypes.length; i++) {
      await prisma.organizationType.upsert({
        where: { name: organizationTypes[i] },
        update: { order: i },
        create: {
          name: organizationTypes[i],
          order: i,
          isActive: true
        }
      })
    }
    console.log(`✅ ${organizationTypes.length} types d'organisations insérés`)

    // Seed Sectors
    console.log('🏢 Insertion des secteurs d\'activité...')
    for (let i = 0; i < sectors.length; i++) {
      await prisma.sector.upsert({
        where: { name: sectors[i] },
        update: { order: i },
        create: {
          name: sectors[i],
          order: i,
          isActive: true
        }
      })
    }
    console.log(`✅ ${sectors.length} secteurs d'activité insérés`)

    // Seed Partnership Types
    console.log('🤝 Insertion des types de partenariat...')
    for (let i = 0; i < partnershipTypes.length; i++) {
      await prisma.partnershipType.upsert({
        where: { name: partnershipTypes[i] },
        update: { order: i },
        create: {
          name: partnershipTypes[i],
          order: i,
          isActive: true
        }
      })
    }
    console.log(`✅ ${partnershipTypes.length} types de partenariat insérés`)

    // Seed Skills
    console.log('💪 Insertion des compétences...')
    for (let i = 0; i < skills.length; i++) {
      await prisma.skill.upsert({
        where: { name: skills[i].name },
        update: { 
          category: skills[i].category,
          order: i 
        },
        create: {
          name: skills[i].name,
          category: skills[i].category,
          order: i,
          isActive: true
        }
      })
    }
    console.log(`✅ ${skills.length} compétences insérées`)

    // Seed Departments
    console.log('🏛️ Insertion des départements/pôles...')
    for (let i = 0; i < departments.length; i++) {
      await prisma.departmentOption.upsert({
        where: { name: departments[i] },
        update: { order: i },
        create: {
          name: departments[i],
          order: i,
          isActive: true
        }
      })
    }
    console.log(`✅ ${departments.length} départements insérés`)

    // Afficher les statistiques
    const stats = await Promise.all([
      prisma.organizationType.count(),
      prisma.sector.count(),
      prisma.partnershipType.count(),
      prisma.skill.count(),
      prisma.departmentOption.count()
    ])

    console.log('\n📊 Statistiques des constantes:')
    console.log(`  Types d'organisations: ${stats[0]}`)
    console.log(`  Secteurs d'activité: ${stats[1]}`)
    console.log(`  Types de partenariat: ${stats[2]}`)
    console.log(`  Compétences: ${stats[3]}`)
    console.log(`  Départements: ${stats[4]}`)

    console.log('\n🎉 Initialisation des constantes terminée avec succès!')

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation des constantes:', error)
    throw error
  }
}

async function main() {
  try {
    await seedConstants()
  } catch (error) {
    console.error(error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Exporter la fonction pour pouvoir l'utiliser dans d'autres scripts
module.exports = { seedConstants }

// Exécuter seulement si le script est appelé directement
if (require.main === module) {
  main()
}
