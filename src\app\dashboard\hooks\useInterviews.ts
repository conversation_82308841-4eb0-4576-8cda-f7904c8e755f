import { useState, useCallback } from 'react'
import { Interview, InterviewFormData } from '../utils/constants'

export const useInterviews = () => {
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [interviewsLoading, setInterviewsLoading] = useState(false)
  const [showInterviewForm, setShowInterviewForm] = useState(false)
  const [interviewFormData, setInterviewFormData] = useState<InterviewFormData>({
    title: '',
    description: '',
    candidateId: '',
    scheduledAt: '',
    type: 'DISCOVERY'
  })
  const [editingInterview, setEditingInterview] = useState<Interview | null>(null)
  const [showInterviewDetails, setShowInterviewDetails] = useState<string | null>(null)
  const [startingInterview, setStartingInterview] = useState<Interview | null>(null)

  // Charger les entretiens
  const loadInterviews = useCallback(async () => {
    try {
      setInterviewsLoading(true)
      console.log('📅 Chargement des entretiens...')
      const response = await fetch('/api/interviews')
      if (response.ok) {
        const data = await response.json()
        console.log('📅 Entretiens chargés:', data.length, 'entretiens')
        setInterviews(data)
      } else {
        console.error('❌ Erreur chargement entretiens:', response.status)
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des entretiens:', error)
    } finally {
      setInterviewsLoading(false)
    }
  }, [])

  // Créer un entretien
  const handleCreateInterview = async (formData: InterviewFormData, onSuccess?: () => void, onError?: (error: string) => void) => {
    try {
      console.log('📝 Création entretien avec données:', formData)

      const response = await fetch('/api/interviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      console.log('📡 Réponse API:', response.status, response.statusText)

      if (response.ok) {
        const newInterview = await response.json()
        console.log('✅ Entretien créé:', newInterview)

        // Fermer le formulaire et réinitialiser
        setShowInterviewForm(false)
        setInterviewFormData({
          title: '',
          description: '',
          candidateId: '',
          scheduledAt: '',
          type: 'DISCOVERY'
        })

        // Recharger les entretiens
        await loadInterviews()

        if (onSuccess) {
          onSuccess()
        }

        console.log('✅ Données rechargées avec succès')
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ Erreur API:', errorData)
        throw new Error(errorData.error || 'Erreur lors de la création de l\'entretien')
      }
    } catch (error) {
      console.error('❌ Erreur création entretien:', error)
      if (onError) {
        onError(error instanceof Error ? error.message : 'Impossible de créer l\'entretien')
      }
    }
  }

  // Mettre à jour le statut d'un entretien
  const handleUpdateInterviewStatus = async (interviewId: string, newStatus: string, onSuccess?: () => void, onError?: (error: string) => void) => {
    try {
      const response = await fetch(`/api/interviews/${interviewId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        await loadInterviews()
        if (onSuccess) {
          onSuccess()
        }
      } else {
        throw new Error('Erreur lors de la mise à jour du statut')
      }
    } catch (error) {
      console.error('Erreur:', error)
      if (onError) {
        onError('Impossible de mettre à jour le statut de l\'entretien')
      }
    }
  }

  // Modifier un entretien
  const handleEditInterview = (interview: Interview) => {
    setEditingInterview(interview)
    setInterviewFormData({
      title: interview.title,
      description: interview.description || '',
      candidateId: interview.candidateId,
      scheduledAt: interview.scheduledAt.slice(0, 16), // Format pour datetime-local
      type: interview.type
    })
    setShowInterviewForm(true)
  }

  // Démarrer un entretien
  const handleStartInterview = (interview: Interview) => {
    setStartingInterview(interview)
  }

  return {
    interviews,
    interviewsLoading,
    showInterviewForm,
    setShowInterviewForm,
    interviewFormData,
    setInterviewFormData,
    editingInterview,
    setEditingInterview,
    showInterviewDetails,
    setShowInterviewDetails,
    startingInterview,
    setStartingInterview,
    loadInterviews,
    handleCreateInterview,
    handleUpdateInterviewStatus,
    handleEditInterview,
    handleStartInterview
  }
}
