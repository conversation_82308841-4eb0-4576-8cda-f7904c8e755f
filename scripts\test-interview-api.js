#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testInterviewAPI() {
  console.log('🧪 Test de l\'API Entretiens - Karma Com Solidarité')
  console.log('='.repeat(60))

  try {
    // Test 1: Créer un bénévole de test
    console.log('\n1. Création d\'un bénévole de test...')
    const testVolunteer = await prisma.volunteerApplication.create({
      data: {
        firstName: 'Marie',
        lastName: '<PERSON><PERSON>',
        email: '<EMAIL>',
        phoneNumber: '06 12 34 56 78',
        currentStatus: 'En poste',
        contributionPole: 'IT et Transformation Numérique',
        weeklyHours: 'Entre 5 et 10 heures par semaine',
        availability: 'Soirée (18h à 22h)',
        participationRhythm: 'Régulier (chaque semaine)',
        howDidYouKnowUs: 'Site internet de Karma Com Solidarité',
        status: 'PENDING'
      }
    })
    console.log('✅ Bénévole créé:', testVolunteer.id)

    // Test 2: Créer un utilisateur classique de test
    console.log('\n2. Création d\'un candidat classique de test...')
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Jean Martin',
        password: 'test123',
        userType: 'VOLUNTEER',
        profile: {
          create: {
            firstName: 'Jean',
            lastName: 'Martin',
            membershipStatus: 'PENDING'
          }
        }
      }
    })
    console.log('✅ Candidat classique créé:', testUser.id)

    // Test 3: Simuler la création d'entretien pour bénévole
    console.log('\n3. Test création entretien pour bénévole...')
    const volunteerInterviewData = {
      title: 'Entretien Découverte - Marie Dupont',
      description: 'Entretien de découverte pour candidature bénévole',
      candidateId: testVolunteer.id,
      scheduledAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Dans 7 jours
      type: 'DISCOVERY'
    }
    console.log('   Données préparées:', volunteerInterviewData)

    // Test 4: Simuler la création d'entretien pour candidat classique
    console.log('\n4. Test création entretien pour candidat classique...')
    const classicInterviewData = {
      title: 'Entretien Technique - Jean Martin',
      description: 'Entretien technique pour candidature',
      candidateId: testUser.id,
      scheduledAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // Dans 5 jours
      type: 'TECHNICAL'
    }
    console.log('   Données préparées:', classicInterviewData)

    // Test 5: Vérifier les statuts
    console.log('\n5. Vérification des statuts...')
    const volunteerStatus = await prisma.volunteerApplication.findUnique({
      where: { id: testVolunteer.id },
      select: { status: true }
    })
    console.log('   Statut bénévole:', volunteerStatus?.status)

    const userStatus = await prisma.user.findUnique({
      where: { id: testUser.id },
      include: { profile: true }
    })
    console.log('   Statut candidat classique:', userStatus?.profile?.membershipStatus)

    // Test 6: Vérifier les logs d'activité
    console.log('\n6. Vérification des logs d\'activité...')
    const activityLogs = await prisma.activityLog.findMany({
      where: {
        action: 'INTERVIEW_SCHEDULED'
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })
    console.log(`   ${activityLogs.length} log(s) d'entretien trouvé(s)`)

    // Test 7: Vérifier les rendez-vous
    console.log('\n7. Vérification des rendez-vous...')
    const appointments = await prisma.appointment.findMany({
      include: {
        candidate: {
          include: { profile: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })
    console.log(`   ${appointments.length} rendez-vous trouvé(s)`)

    // Test 8: Nettoyage
    console.log('\n8. Nettoyage des données de test...')
    
    // Supprimer les rendez-vous liés
    await prisma.appointment.deleteMany({
      where: {
        candidate: {
          email: {
            in: ['<EMAIL>', '<EMAIL>']
          }
        }
      }
    })

    // Supprimer les utilisateurs et leurs profils
    await prisma.user.deleteMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>']
        }
      }
    })

    // Supprimer le bénévole
    await prisma.volunteerApplication.deleteMany({
      where: {
        email: '<EMAIL>'
      }
    })

    console.log('✅ Données de test supprimées')

    console.log('\n🎉 Tous les tests de préparation sont passés!')
    console.log('\n📋 Résumé:')
    console.log('   - Création bénévole: ✅ OK')
    console.log('   - Création candidat classique: ✅ OK')
    console.log('   - Structure données entretien: ✅ OK')
    console.log('   - Vérification statuts: ✅ OK')
    console.log('   - Logs d\'activité: ✅ OK')
    console.log('   - Rendez-vous: ✅ OK')
    console.log('\n🚀 L\'API est prête à recevoir les requêtes d\'entretien!')

  } catch (error) {
    console.error('❌ Erreur lors du test:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Exécuter les tests
testInterviewAPI()
