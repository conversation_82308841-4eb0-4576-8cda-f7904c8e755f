import React from 'react'
import { Building2, Heart, User } from 'lucide-react'
import { STATUS_CONFIG, INTERVIEW_TYPE_LABELS, TYPE_ICONS } from './constants'

// Fonction pour obtenir le badge de statut
export const getStatusBadge = (status: string) => {
  const config = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG] || STATUS_CONFIG.pending

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  )
}

// Fonction pour obtenir le label des types d'entretien
export const getInterviewTypeLabel = (type: string): string => {
  return INTERVIEW_TYPE_LABELS[type as keyof typeof INTERVIEW_TYPE_LABELS] || type
}

// Fonction pour obtenir l'icône par type
export const getTypeIcon = (type: string) => {
  switch (type) {
    case 'association':
      return <Building2 className="text-blue-600" size={20} />
    case 'volunteer':
      return <Heart className="text-red-500" size={20} />
    case 'individual':
    default:
      return <User className="text-gray-600" size={20} />
  }
}

// Fonction pour formater le statut d'affichage
export const formatStatusForDisplay = (status: string): string => {
  const config = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG]
  return config ? config.label : status
}

// Fonction pour obtenir la couleur du statut
export const getStatusColor = (status: string): string => {
  const config = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG]
  return config ? config.color : STATUS_CONFIG.pending.color
}

// Fonction pour valider un statut
export const isValidStatus = (status: string): boolean => {
  return status in STATUS_CONFIG
}

// Fonction pour obtenir tous les statuts disponibles pour les candidats
export const getCandidateStatusOptions = () => [
  { value: 'pending', label: 'En attente' },
  { value: 'approved', label: 'Approuvé' },
  { value: 'active', label: 'Actif' },
  { value: 'rejected', label: 'Rejeté' },
  { value: 'inactive', label: 'Inactif' },
  { value: 'suspended', label: 'Suspendu' }
]

// Fonction pour obtenir tous les statuts disponibles pour les entretiens
export const getInterviewStatusOptions = () => [
  { value: 'SCHEDULED', label: 'Programmé' },
  { value: 'CONFIRMED', label: 'Confirmé' },
  { value: 'COMPLETED', label: 'Terminé' },
  { value: 'CANCELLED', label: 'Annulé' }
]

// Fonction pour obtenir tous les types d'entretien disponibles
export const getInterviewTypeOptions = () => [
  { value: 'DISCOVERY', label: 'Découverte' },
  { value: 'INTEGRATION', label: 'Intégration' },
  { value: 'FOLLOW_UP', label: 'Suivi' },
  { value: 'INTERVIEW', label: 'Entretien' }
]
