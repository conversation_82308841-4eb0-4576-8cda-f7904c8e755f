import { useState, useCallback } from 'react'

interface DashboardData {
  overview: {
    totalCandidates: number
    pendingApplications: number
    scheduledInterviews: number
    approvedMembers: number
  }
  recentApplications: any[]
}

export const useDashboardData = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fonction de chargement des données stabilisée avec useCallback
  const fetchDashboardData = useCallback(async () => {
    const isDevelopment = process.env.NODE_ENV === 'development'

    try {
      setLoading(true)
      setError(null)

      // Récupérer les headers d'authentification directement
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      const response = await fetch('/api/dashboard/stats', { headers })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setDashboardData(data)
    } catch (err) {
      console.error('Erreur lors du chargement des données:', err)
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
    } finally {
      setLoading(false)
    }
  }, [])

  // Données par défaut si pas encore chargées
  const stats = dashboardData?.overview || {
    totalCandidates: 0,
    pendingApplications: 0,
    scheduledInterviews: 0,
    approvedMembers: 0
  }

  // Données des candidatures récentes depuis l'API (limitées à 10 pour la vue d'ensemble)
  const recentApplications = (dashboardData?.recentApplications || []).slice(0, 10)

  // Entretiens à venir (limités à 5 pour la vue d'ensemble)
  const upcomingInterviews = []

  return {
    dashboardData,
    stats,
    recentApplications,
    upcomingInterviews,
    loading,
    error,
    fetchDashboardData,
    setError
  }
}
