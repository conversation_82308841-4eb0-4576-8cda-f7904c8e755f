#!/bin/bash

# Script de test pour vérifier le Dockerfile
echo "🐳 Test du Dockerfile - Karma Com Solidarité"
echo "============================================"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier que Docker est installé
print_status "Vérification de Docker..."
if ! command -v docker &> /dev/null; then
    print_error "Docker n'est pas installé ou n'est pas dans le PATH"
    exit 1
fi

print_success "Docker est disponible"

# Vérifier que Docker daemon est en cours d'exécution
print_status "Vérification du daemon Docker..."
if ! docker info &> /dev/null; then
    print_error "Docker daemon n'est pas en cours d'exécution"
    print_warning "Démarrez Docker Desktop ou le service Docker"
    exit 1
fi

print_success "Docker daemon est actif"

# Nettoyer les images précédentes
print_status "Nettoyage des images précédentes..."
docker rmi karma-com-app:latest 2>/dev/null || true
docker system prune -f &> /dev/null

# Construire l'image Docker
print_status "Construction de l'image Docker..."
echo "📦 Début du build..."

if docker build -t karma-com-app:latest .; then
    print_success "Image Docker construite avec succès"
else
    print_error "Échec de la construction de l'image Docker"
    exit 1
fi

# Vérifier que l'image a été créée
print_status "Vérification de l'image créée..."
if docker images | grep -q "karma-com-app"; then
    print_success "Image karma-com-app trouvée"
    docker images | grep karma-com-app
else
    print_error "Image karma-com-app non trouvée"
    exit 1
fi

# Tester le démarrage du conteneur
print_status "Test de démarrage du conteneur..."
CONTAINER_ID=$(docker run -d -p 3001:3000 karma-com-app:latest)

if [ $? -eq 0 ]; then
    print_success "Conteneur démarré avec l'ID: $CONTAINER_ID"
else
    print_error "Échec du démarrage du conteneur"
    exit 1
fi

# Attendre que l'application démarre
print_status "Attente du démarrage de l'application..."
sleep 10

# Vérifier les logs du conteneur
print_status "Vérification des logs du conteneur..."
echo "📋 Logs du conteneur:"
docker logs $CONTAINER_ID

# Vérifier que le conteneur est toujours en cours d'exécution
if docker ps | grep -q $CONTAINER_ID; then
    print_success "Conteneur toujours en cours d'exécution"
else
    print_error "Conteneur s'est arrêté"
    echo "📋 Logs d'erreur:"
    docker logs $CONTAINER_ID
    docker rm $CONTAINER_ID
    exit 1
fi

# Tester la connectivité HTTP
print_status "Test de connectivité HTTP..."
sleep 5

if curl -f http://localhost:3001 &> /dev/null; then
    print_success "Application accessible sur http://localhost:3001"
else
    print_warning "Application pas encore accessible, vérification des logs..."
    docker logs $CONTAINER_ID | tail -10
fi

# Afficher les informations du conteneur
print_status "Informations du conteneur:"
docker inspect $CONTAINER_ID --format='{{.State.Status}}: {{.State.Health.Status}}'

# Nettoyer
print_status "Nettoyage..."
docker stop $CONTAINER_ID &> /dev/null
docker rm $CONTAINER_ID &> /dev/null

print_success "Test Docker terminé"

echo ""
echo "🎉 Résumé du test:"
echo "✅ Image Docker construite avec succès"
echo "✅ Conteneur démarré sans erreur"
echo "✅ Application potentiellement accessible"
echo ""
echo "🚀 Pour démarrer l'application:"
echo "   docker run -p 3000:3000 karma-com-app:latest"
echo ""
echo "🌐 Accès à l'application:"
echo "   http://localhost:3000"
