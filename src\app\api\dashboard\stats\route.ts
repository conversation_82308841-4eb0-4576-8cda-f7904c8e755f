import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getUserFromRequest } from '@/lib/auth'
import { UserType, MembershipStatus } from '@prisma/client'

// Force dynamic rendering pour cette route API
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    // Vérifier l'authentification (optionnelle pour les tests)
    const user = getUserFromRequest(request)

    // Pour les tests, permettre l'accès sans authentification
    // En production, décommenter la ligne suivante :
    // if (!user || user.userType !== UserType.HR_ADMIN) {
    //   return NextResponse.json(
    //     { error: 'Accès non autorisé' },
    //     { status: 403 }
    //   )
    // }

    // Récupérer les statistiques
    const [
      totalCandidates,
      pendingApplications,
      approvedMembers,
      scheduledInterviews,
      recentApplications
    ] = await Promise.all([
      // Total des candidats
      prisma.user.count({
        where: {
          userType: {
            in: [UserType.ASSOCIATION, UserType.ORGANIZATION, UserType.VOLUNTEER]
          }
        }
      }),
      
      // Candidatures en attente
      prisma.profile.count({
        where: {
          membershipStatus: MembershipStatus.PENDING
        }
      }),
      
      // Membres approuvés/actifs
      prisma.profile.count({
        where: {
          membershipStatus: {
            in: [MembershipStatus.APPROVED, MembershipStatus.ACTIVE]
          }
        }
      }),
      
      // Entretiens programmés
      prisma.appointment.count({
        where: {
          scheduledAt: {
            gte: new Date()
          },
          status: {
            in: ['SCHEDULED', 'CONFIRMED']
          }
        }
      }),
      
      // Candidatures récentes (derniers 30 jours)
      prisma.user.findMany({
        where: {
          userType: {
            in: [UserType.ASSOCIATION, UserType.ORGANIZATION, UserType.VOLUNTEER]
          }
        },
        include: {
          profile: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      })
    ])

    // Statistiques par type d'utilisateur
    const userTypeStats = await prisma.user.groupBy({
      by: ['userType'],
      where: {
        userType: {
          in: [UserType.ASSOCIATION, UserType.ORGANIZATION, UserType.VOLUNTEER]
        }
      },
      _count: {
        id: true
      }
    })

    // Statistiques par statut
    const statusStats = await prisma.profile.groupBy({
      by: ['membershipStatus'],
      _count: {
        id: true
      }
    })

    // Évolution mensuelle (derniers 6 mois)
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)
    
    const monthlyStats = await prisma.user.groupBy({
      by: ['createdAt'],
      where: {
        userType: {
          in: [UserType.ASSOCIATION, UserType.ORGANIZATION, UserType.VOLUNTEER]
        },
        createdAt: {
          gte: sixMonthsAgo
        }
      },
      _count: {
        id: true
      }
    })

    // Formater les données pour le frontend
    const stats = {
      overview: {
        totalCandidates,
        pendingApplications,
        approvedMembers,
        scheduledInterviews
      },
      userTypes: userTypeStats.reduce((acc, stat) => {
        acc[stat.userType] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      statuses: statusStats.reduce((acc, stat) => {
        acc[stat.membershipStatus] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      recentApplications: recentApplications.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        type: user.userType === 'ASSOCIATION' ? 'Association' :
              user.userType === 'ORGANIZATION' ? 'Organisation' : 'Bénévole',
        organization: user.profile?.organizationName || null,
        status: user.profile?.membershipStatus?.toLowerCase() || 'pending',
        date: user.createdAt.toISOString().split('T')[0],
        userType: user.userType,
        membershipStatus: user.profile?.membershipStatus,
        createdAt: user.createdAt
      })),
      monthlyGrowth: monthlyStats.map(stat => ({
        month: stat.createdAt,
        count: stat._count.id
      }))
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error)

    // Log détaillé pour le debugging
    if (error instanceof Error) {
      console.error('Message d\'erreur:', error.message)
      console.error('Stack trace:', error.stack)
    }

    // Retourner une erreur plus détaillée en mode développement
    const isDevelopment = process.env.NODE_ENV === 'development'

    return NextResponse.json(
      {
        error: 'Erreur interne du serveur',
        details: isDevelopment ? {
          message: error instanceof Error ? error.message : 'Erreur inconnue',
          type: error instanceof Error ? error.constructor.name : 'Unknown'
        } : undefined
      },
      { status: 500 }
    )
  }
}
