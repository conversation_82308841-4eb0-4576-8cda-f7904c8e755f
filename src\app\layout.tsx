import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Karma Com Solidarité - Gestion d\'Adhésion',
  description: 'Plateforme de gestion d\'adhésion pour Karma Com Solidarité - Associations, Organisations et Bénévoles',
  keywords: 'karma com, solidarité, adhésion, associations, bénévoles, organisations',
  authors: [{ name: 'Karma Com Solidarité' }],
 // viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr">
      <body className={inter.className}>
        <div className="min-h-screen bg-gray-50">
          {children}
        </div>
      </body>
    </html>
  )
}
