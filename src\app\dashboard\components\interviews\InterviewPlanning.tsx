import React, { useState } from 'react'
import { Plus, Calendar, Clock, User, Edit, Play, Trash2, Check<PERSON>ircle, XCircle, AlertTriangle } from 'lucide-react'
import { getStatusBadge, getInterviewTypeLabel } from '../../utils/statusHelpers'
import { formatDateFR, formatTimeFR, formatDateForInput } from '../../utils/dateHelpers'
import { Interview, InterviewFormData } from '../../utils/constants'

interface InterviewPlanningProps {
  interviews: Interview[]
  loading: boolean
  onCreateInterview: (formData: any) => Promise<void>
  onUpdateInterviewStatus: (interviewId: string, newStatus: string) => Promise<void>
  onEditInterview: (interview: Interview) => void
  onStartInterview: (interview: Interview) => void
  candidates: any[]
}

const InterviewPlanning: React.FC<InterviewPlanningProps> = ({
  interviews,
  loading,
  onCreateInterview,
  onUpdateInterviewStatus,
  onEditInterview,
  onStartInterview,
  candidates
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [formData, setFormData] = useState<InterviewFormData>({
    title: '',
    description: '',
    candidateId: '',
    scheduledAt: '',
    type: 'DISCOVERY'
  })
  const [submitting, setSubmitting] = useState(false)
  const [message, setMessage] = useState<{
    type: 'success' | 'error' | 'warning'
    text: string
  } | null>(null)

  // Entretiens à venir (prochains 7 jours)
  const upcomingInterviews = interviews.filter(interview => {
    const interviewDate = new Date(interview.scheduledAt)
    const now = new Date()
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    return interviewDate >= now && interviewDate <= nextWeek && 
           ['SCHEDULED', 'CONFIRMED'].includes(interview.status)
  }).sort((a, b) => new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime())

  // Entretiens du jour
  const todayInterviews = interviews.filter(interview => {
    const interviewDate = new Date(interview.scheduledAt)
    const today = new Date()
    return interviewDate.toDateString() === today.toDateString() &&
           ['SCHEDULED', 'CONFIRMED'].includes(interview.status)
  }).sort((a, b) => new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime())

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.title || !formData.candidateId || !formData.scheduledAt) {
      setMessage({
        type: 'warning',
        text: 'Veuillez remplir tous les champs obligatoires'
      })
      setTimeout(() => setMessage(null), 5000)
      return
    }

    setSubmitting(true)
    setMessage(null)

    try {
      await onCreateInterview(formData)

      // Message de succès
      setMessage({
        type: 'success',
        text: `Entretien "${formData.title}" créé avec succès !`
      })

      // Réinitialiser le formulaire
      setFormData({
        title: '',
        description: '',
        candidateId: '',
        scheduledAt: '',
        type: 'DISCOVERY'
      })
      setShowCreateForm(false)

      // Masquer le message après 5 secondes
      setTimeout(() => setMessage(null), 5000)

    } catch (error) {
      console.error('Erreur création entretien:', error)
      setMessage({
        type: 'error',
        text: 'Erreur lors de la création de l\'entretien. Veuillez réessayer.'
      })
      setTimeout(() => setMessage(null), 5000)
    } finally {
      setSubmitting(false)
    }
  }

  const handleQuickAction = async (interview: Interview, action: string) => {
    switch (action) {
      case 'confirm':
        await onUpdateInterviewStatus(interview.id, 'CONFIRMED')
        break
      case 'complete':
        await onUpdateInterviewStatus(interview.id, 'COMPLETED')
        break
      case 'cancel':
        await onUpdateInterviewStatus(interview.id, 'CANCELLED')
        break
      case 'start':
        onStartInterview(interview)
        break
      case 'edit':
        onEditInterview(interview)
        break
    }
  }

  // Composant pour les messages de notification
  const NotificationMessage = () => {
    if (!message) return null

    const getMessageConfig = () => {
      switch (message.type) {
        case 'success':
          return {
            bgColor: 'bg-green-50',
            borderColor: 'border-green-200',
            textColor: 'text-green-800',
            icon: CheckCircle,
            iconColor: 'text-green-600'
          }
        case 'error':
          return {
            bgColor: 'bg-red-50',
            borderColor: 'border-red-200',
            textColor: 'text-red-800',
            icon: XCircle,
            iconColor: 'text-red-600'
          }
        case 'warning':
          return {
            bgColor: 'bg-yellow-50',
            borderColor: 'border-yellow-200',
            textColor: 'text-yellow-800',
            icon: AlertTriangle,
            iconColor: 'text-yellow-600'
          }
        default:
          return {
            bgColor: 'bg-blue-50',
            borderColor: 'border-blue-200',
            textColor: 'text-blue-800',
            icon: CheckCircle,
            iconColor: 'text-blue-600'
          }
      }
    }

    const config = getMessageConfig()
    const IconComponent = config.icon

    return (
      <div className={`${config.bgColor} ${config.borderColor} border rounded-lg p-4 mb-6`}>
        <div className="flex items-center">
          <IconComponent className={`${config.iconColor} mr-3`} size={20} />
          <p className={`${config.textColor} font-medium`}>{message.text}</p>
          <button
            onClick={() => setMessage(null)}
            className={`ml-auto ${config.textColor} hover:opacity-70`}
          >
            <XCircle size={16} />
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Messages de notification */}
      <NotificationMessage />

      {/* Section: Entretiens du jour */}
      <div className="dashboard-card border-l-4 border-l-karma-blue">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-r from-karma-blue to-karma-pink rounded-full flex items-center justify-center mr-3">
              <Calendar className="text-white" size={16} />
            </div>
            Entretiens d'aujourd'hui
            <span className="ml-2 px-2 py-1 text-xs bg-karma-pink text-white rounded-full">
              {todayInterviews.length}
            </span>
          </h3>
        </div>

        {todayInterviews.length > 0 ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {todayInterviews.map((interview) => (
              <div key={interview.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-lg hover:border-karma-pink transition-all duration-200 bg-gradient-to-br from-white to-gray-50">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-medium text-karma-blue">{interview.title}</h4>
                    <p className="text-sm text-gray-600">{interview.candidateName}</p>
                  </div>
                  {getStatusBadge(interview.status)}
                </div>
                
                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <div className="flex items-center">
                    <Clock size={14} className="mr-2" />
                    {formatTimeFR(interview.scheduledAt)}
                  </div>
                  <div className="flex items-center">
                    <User size={14} className="mr-2" />
                    {getInterviewTypeLabel(interview.type)}
                  </div>
                </div>

                <div className="flex space-x-2">
                  {interview.status === 'SCHEDULED' && (
                    <button
                      onClick={() => handleQuickAction(interview, 'confirm')}
                      className="flex-1 px-3 py-1 text-xs bg-gradient-to-r from-green-500 to-green-600 text-white rounded-md hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm"
                    >
                      Confirmer
                    </button>
                  )}
                  {interview.status === 'CONFIRMED' && (
                    <button
                      onClick={() => handleQuickAction(interview, 'start')}
                      className="flex-1 px-3 py-1 text-xs bg-gradient-to-r from-karma-blue to-karma-pink text-white rounded-md hover:shadow-md transition-all duration-200 flex items-center justify-center"
                    >
                      <Play size={12} className="mr-1" />
                      Démarrer
                    </button>
                  )}
                  <button
                    onClick={() => handleQuickAction(interview, 'edit')}
                    className="px-3 py-1 text-xs bg-gray-100 text-karma-blue rounded-md hover:bg-karma-blue hover:text-white transition-all duration-200"
                  >
                    <Edit size={12} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <div className="w-16 h-16 bg-gradient-to-r from-karma-blue to-karma-pink rounded-full flex items-center justify-center mx-auto mb-4">
              <Calendar size={32} className="text-white" />
            </div>
            <p className="text-lg font-medium text-gray-600">Aucun entretien prévu aujourd'hui</p>
            <p className="text-sm text-gray-500 mt-2">Profitez de cette journée pour planifier de nouveaux rendez-vous</p>
          </div>
        )}
      </div>

      {/* Section: Planification et création */}
      <div className="dashboard-card border-l-4 border-l-karma-pink">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-r from-karma-pink to-karma-blue rounded-full flex items-center justify-center mr-3">
              <Plus className="text-white" size={16} />
            </div>
            Planification des rendez-vous
          </h3>
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="bg-gradient-to-r from-karma-blue to-karma-pink text-white px-6 py-3 rounded-lg hover:shadow-lg hover:scale-105 transition-all duration-200 flex items-center space-x-2 font-medium"
          >
            <Plus size={18} />
            <span>Nouveau rendez-vous</span>
          </button>
        </div>

        {/* Formulaire de création */}
        {showCreateForm && (
          <div className="mb-6 p-6 bg-gradient-to-br from-blue-50 to-pink-50 rounded-xl border border-karma-pink/20 shadow-sm">
            <div className="flex items-center mb-4">
              <div className="w-6 h-6 bg-gradient-to-r from-karma-blue to-karma-pink rounded-full flex items-center justify-center mr-3">
                <Plus className="text-white" size={14} />
              </div>
              <h4 className="text-lg font-semibold text-karma-blue">Créer un nouveau rendez-vous</h4>
            </div>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Titre de l'entretien *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-karma-pink focus:border-karma-pink transition-all duration-200 bg-white"
                    placeholder="Ex: Entretien de découverte"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Candidat *
                  </label>
                  <select
                    value={formData.candidateId}
                    onChange={(e) => setFormData({ ...formData, candidateId: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-karma-pink focus:border-karma-pink transition-all duration-200 bg-white"
                    required
                  >
                    <option value="">Sélectionner un candidat</option>
                    {candidates.map((candidate) => (
                      <option key={candidate.id} value={candidate.id}>
                        {candidate.name} ({candidate.email})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date et heure *
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.scheduledAt}
                    onChange={(e) => setFormData({ ...formData, scheduledAt: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-karma-pink focus:border-karma-pink transition-all duration-200 bg-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type d'entretien
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-karma-pink focus:border-karma-pink transition-all duration-200 bg-white"
                  >
                    <option value="DISCOVERY">Découverte</option>
                    <option value="INTEGRATION">Intégration</option>
                    <option value="FOLLOW_UP">Suivi</option>
                    <option value="INTERVIEW">Entretien</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-karma-pink focus:border-karma-pink transition-all duration-200 bg-white resize-none"
                  placeholder="Détails sur l'entretien..."
                />
              </div>

              <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-6 py-3 text-karma-blue bg-white border border-karma-blue rounded-lg hover:bg-karma-blue hover:text-white transition-all duration-200 font-medium"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  disabled={submitting}
                  className="px-6 py-3 bg-gradient-to-r from-karma-blue to-karma-pink text-white rounded-lg hover:shadow-lg hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 font-medium flex items-center space-x-2"
                >
                  {submitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Création...</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle size={16} />
                      <span>Créer l'entretien</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Entretiens à venir */}
        <div>
          <h4 className="font-medium text-karma-blue mb-4 flex items-center">
            <Clock className="mr-2 text-karma-pink" size={18} />
            Prochains entretiens (7 jours)
            <span className="ml-2 px-2 py-1 text-xs bg-karma-blue text-white rounded-full">
              {upcomingInterviews.length}
            </span>
          </h4>

          {upcomingInterviews.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 rounded-lg overflow-hidden shadow-sm">
                <thead className="bg-gradient-to-r from-karma-blue to-karma-pink">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Entretien
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Candidat
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Date & Heure
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {upcomingInterviews.map((interview) => (
                    <tr key={interview.id} className="hover:bg-gradient-to-r hover:from-blue-50 hover:to-pink-50 transition-all duration-200">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-karma-blue">{interview.title}</div>
                        {interview.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {interview.description}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{interview.candidateName}</div>
                        <div className="text-sm text-gray-500">{interview.candidateEmail}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{formatDateFR(interview.scheduledAt)}</div>
                        <div className="text-gray-500">{formatTimeFR(interview.scheduledAt)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {getInterviewTypeLabel(interview.type)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(interview.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {interview.status === 'SCHEDULED' && (
                            <button
                              onClick={() => handleQuickAction(interview, 'confirm')}
                              className="px-3 py-1 text-xs bg-gradient-to-r from-green-500 to-green-600 text-white rounded-md hover:shadow-md transition-all duration-200"
                              title="Confirmer"
                            >
                              Confirmer
                            </button>
                          )}
                          <button
                            onClick={() => handleQuickAction(interview, 'edit')}
                            className="p-2 text-karma-pink hover:bg-karma-pink hover:text-white rounded-md transition-all duration-200"
                            title="Modifier"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleQuickAction(interview, 'cancel')}
                            className="p-2 text-red-600 hover:bg-red-600 hover:text-white rounded-md transition-all duration-200"
                            title="Annuler"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <div className="w-16 h-16 bg-gradient-to-r from-karma-pink to-karma-blue rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock size={32} className="text-white" />
              </div>
              <p className="text-lg font-medium text-gray-600">Aucun entretien programmé dans les 7 prochains jours</p>
              <p className="text-sm text-gray-500 mt-2">Créez un nouveau rendez-vous pour commencer</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default InterviewPlanning
