// Script de test pour vérifier les nouveaux onglets du dashboard
const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function testInterviewsAPI() {
  console.log('📅 Test de l\'API Entretiens')
  console.log('===========================')
  
  try {
    // Test GET - Récupérer les entretiens
    console.log('📋 Test GET /api/interviews...')
    const getResponse = await fetch(`${BASE_URL}/api/interviews`)
    
    if (getResponse.ok) {
      const interviews = await getResponse.json()
      console.log(`✅ API Entretiens accessible - ${interviews.length} entretiens trouvés`)
      
      if (interviews.length > 0) {
        console.log(`   Premier entretien: ${interviews[0].title}`)
      }
    } else {
      console.log(`❌ API Entretiens erreur: HTTP ${getResponse.status}`)
      return false
    }

    // Test POST - Créer un entretien (simulation)
    console.log('📝 Test POST /api/interviews...')
    
    // D'abord, récupérer un candidat pour le test
    const candidatesResponse = await fetch(`${BASE_URL}/api/candidates`)
    if (!candidatesResponse.ok) {
      console.log('⚠️ Impossible de récupérer les candidats pour le test POST')
      return true // GET fonctionne, c'est déjà bien
    }
    
    const candidatesData = await candidatesResponse.json()
    if (candidatesData.candidates.length === 0) {
      console.log('⚠️ Aucun candidat disponible pour le test POST')
      return true
    }
    
    const testInterview = {
      title: 'Test Entretien - Script',
      description: 'Entretien de test créé par le script de validation',
      candidateId: candidatesData.candidates[0].id,
      scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Demain
      type: 'DISCOVERY'
    }
    
    const postResponse = await fetch(`${BASE_URL}/api/interviews`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testInterview),
    })
    
    if (postResponse.ok) {
      const newInterview = await postResponse.json()
      console.log(`✅ Création d'entretien réussie: ${newInterview.title}`)
    } else {
      const errorText = await postResponse.text()
      console.log(`❌ Erreur création entretien: HTTP ${postResponse.status}`)
      console.log(`   Détails: ${errorText}`)
    }
    
    return true
    
  } catch (error) {
    console.log('❌ Erreur test API Entretiens:', error.message)
    return false
  }
}

async function testCandidatesAPI() {
  console.log('\n👥 Test de l\'API Candidats')
  console.log('===========================')
  
  try {
    // Test GET - Récupérer les candidats
    console.log('📋 Test GET /api/candidates...')
    const getResponse = await fetch(`${BASE_URL}/api/candidates`)
    
    if (getResponse.ok) {
      const data = await getResponse.json()
      console.log(`✅ API Candidats accessible - ${data.candidates.length} candidats trouvés`)
      console.log(`   Pagination: page ${data.pagination.page}/${data.pagination.totalPages}`)
      
      if (data.candidates.length > 0) {
        const candidate = data.candidates[0]
        console.log(`   Premier candidat: ${candidate.name} (${candidate.type})`)
      }
    } else {
      console.log(`❌ API Candidats erreur: HTTP ${getResponse.status}`)
      return false
    }

    // Test avec filtres
    console.log('🔍 Test avec filtres...')
    const filterResponse = await fetch(`${BASE_URL}/api/candidates?status=pending&limit=5`)
    
    if (filterResponse.ok) {
      const filteredData = await filterResponse.json()
      console.log(`✅ Filtres fonctionnels - ${filteredData.candidates.length} candidats en attente`)
    } else {
      console.log(`⚠️ Filtres non fonctionnels: HTTP ${filterResponse.status}`)
    }
    
    return true
    
  } catch (error) {
    console.log('❌ Erreur test API Candidats:', error.message)
    return false
  }
}

async function testStatusUpdateAPI() {
  console.log('\n🔄 Test de Mise à Jour de Statut')
  console.log('=================================')
  
  try {
    // Récupérer un candidat pour le test
    const candidatesResponse = await fetch(`${BASE_URL}/api/candidates`)
    if (!candidatesResponse.ok) {
      console.log('❌ Impossible de récupérer les candidats')
      return false
    }
    
    const candidatesData = await candidatesResponse.json()
    if (candidatesData.candidates.length === 0) {
      console.log('⚠️ Aucun candidat disponible pour le test')
      return true
    }
    
    const candidateId = candidatesData.candidates[0].id
    const originalStatus = candidatesData.candidates[0].status
    
    console.log(`📝 Test mise à jour statut pour: ${candidatesData.candidates[0].name}`)
    console.log(`   Statut actuel: ${originalStatus}`)
    
    // Test de mise à jour
    const newStatus = originalStatus === 'pending' ? 'approved' : 'pending'
    const updateResponse = await fetch(`${BASE_URL}/api/candidates/${candidateId}/status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status: newStatus }),
    })
    
    if (updateResponse.ok) {
      const updatedCandidate = await updateResponse.json()
      console.log(`✅ Mise à jour réussie: ${originalStatus} → ${newStatus}`)
      
      // Remettre le statut original
      await fetch(`${BASE_URL}/api/candidates/${candidateId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: originalStatus }),
      })
      console.log(`🔄 Statut restauré: ${newStatus} → ${originalStatus}`)
    } else {
      const errorText = await updateResponse.text()
      console.log(`❌ Erreur mise à jour: HTTP ${updateResponse.status}`)
      console.log(`   Détails: ${errorText}`)
      return false
    }
    
    return true
    
  } catch (error) {
    console.log('❌ Erreur test mise à jour statut:', error.message)
    return false
  }
}

async function testDashboardTabs() {
  console.log('\n🖥️ Test des Onglets Dashboard')
  console.log('==============================')
  
  try {
    const response = await fetch(`${BASE_URL}/dashboard`)
    
    if (!response.ok) {
      console.log(`❌ Dashboard inaccessible: HTTP ${response.status}`)
      return false
    }
    
    const html = await response.text()
    
    // Vérifications des onglets
    const tabChecks = [
      { name: 'Onglet Vue d\'ensemble', test: html.includes('Vue d\'ensemble') || html.includes('overview') },
      { name: 'Onglet Candidatures', test: html.includes('Candidatures') || html.includes('applications') },
      { name: 'Onglet Entretiens', test: html.includes('Entretiens') || html.includes('interviews') },
      { name: 'Formulaire entretien', test: html.includes('Planifier un Entretien') },
      { name: 'Tableau candidatures', test: html.includes('Gestion des Candidatures') },
      { name: 'Actions candidats', test: html.includes('Planifier entretien') }
    ]
    
    console.log('🔍 Vérifications des onglets:')
    let passedChecks = 0
    
    tabChecks.forEach(check => {
      if (check.test) {
        console.log(`   ✅ ${check.name}`)
        passedChecks++
      } else {
        console.log(`   ❌ ${check.name}`)
      }
    })
    
    const score = passedChecks / tabChecks.length
    console.log(`\n📊 Score onglets: ${passedChecks}/${tabChecks.length} (${Math.round(score * 100)}%)`)
    
    return score >= 0.8
    
  } catch (error) {
    console.log('❌ Erreur test onglets dashboard:', error.message)
    return false
  }
}

async function main() {
  console.log('🧪 Tests des Onglets Dashboard - Karma Com Solidarité')
  console.log('=====================================================')
  
  // Vérifier que l'application est accessible
  try {
    const response = await fetch(`${BASE_URL}`)
    if (!response.ok) {
      throw new Error(`Application non accessible: HTTP ${response.status}`)
    }
    console.log('✅ Application accessible\n')
  } catch (error) {
    console.log('❌ Application non accessible:', error.message)
    console.log('💡 Assurez-vous que l\'application est démarrée avec "npm run dev"')
    process.exit(1)
  }
  
  // Exécuter les tests
  const interviewsOk = await testInterviewsAPI()
  const candidatesOk = await testCandidatesAPI()
  const statusUpdateOk = await testStatusUpdateAPI()
  const dashboardTabsOk = await testDashboardTabs()
  
  // Résumé final
  console.log('\n📋 Résumé des Tests Onglets')
  console.log('============================')
  
  const tests = [
    { name: 'API Entretiens', passed: interviewsOk },
    { name: 'API Candidats', passed: candidatesOk },
    { name: 'Mise à jour Statut', passed: statusUpdateOk },
    { name: 'Onglets Dashboard', passed: dashboardTabsOk }
  ]
  
  const passedCount = tests.filter(t => t.passed).length
  
  tests.forEach(test => {
    console.log(`${test.passed ? '✅' : '❌'} ${test.name}`)
  })
  
  console.log(`\n📊 Score global: ${passedCount}/${tests.length}`)
  
  if (passedCount === tests.length) {
    console.log('\n🎉 Tous les tests des onglets sont passés!')
    console.log('\n✅ Fonctionnalités disponibles:')
    console.log('   • Onglet Candidatures avec tableau interactif')
    console.log('   • Onglet Entretiens avec formulaire de création')
    console.log('   • Mise à jour des statuts en temps réel')
    console.log('   • APIs complètes pour la gestion')
    console.log('\n🌐 Accédez au dashboard: http://localhost:3000/dashboard')
  } else {
    console.log('\n⚠️ Certains tests ont échoué.')
    console.log('💡 Vérifiez:')
    console.log('   • La base de données est-elle accessible?')
    console.log('   • Les migrations sont-elles appliquées?')
    console.log('   • Les données de test sont-elles présentes?')
  }
}

// Vérifier si node-fetch est disponible
try {
  require('node-fetch')
} catch (error) {
  console.log('❌ node-fetch n\'est pas installé')
  console.log('💡 Il devrait être installé avec les devDependencies')
  process.exit(1)
}

main()
