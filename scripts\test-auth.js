// Script de test pour vérifier l'authentification du dashboard
const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function testLogin(email, password) {
  console.log(`🔐 Test de connexion pour ${email}...`)
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    })

    const result = await response.json()

    if (response.ok) {
      console.log(`✅ Connexion réussie pour ${email}`)
      console.log(`   Type d'utilisateur: ${result.user.userType}`)
      console.log(`   Token reçu: ${result.token ? 'Oui' : 'Non'}`)
      return { success: true, token: result.token, user: result.user }
    } else {
      console.log(`❌ Échec connexion pour ${email}: ${result.error}`)
      return { success: false, error: result.error }
    }
  } catch (error) {
    console.log(`❌ Erreur réseau pour la connexion:`, error.message)
    return { success: false, error: error.message }
  }
}

async function testDashboardWithAuth(token) {
  console.log('\n📊 Test du dashboard avec authentification...')
  
  try {
    const response = await fetch(`${BASE_URL}/api/dashboard/stats`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      console.log('✅ API Dashboard accessible avec token')
      console.log(`   Total candidats: ${data.overview.totalCandidates}`)
      return true
    } else {
      console.log(`❌ API Dashboard inaccessible: HTTP ${response.status}`)
      return false
    }
  } catch (error) {
    console.log('❌ Erreur lors de l\'accès au dashboard:', error.message)
    return false
  }
}

async function testDashboardWithoutAuth() {
  console.log('\n📊 Test du dashboard sans authentification...')
  
  try {
    const response = await fetch(`${BASE_URL}/api/dashboard/stats`)

    if (response.ok) {
      const data = await response.json()
      console.log('✅ API Dashboard accessible sans token (mode test)')
      console.log(`   Total candidats: ${data.overview.totalCandidates}`)
      return true
    } else {
      console.log(`❌ API Dashboard inaccessible: HTTP ${response.status}`)
      return false
    }
  } catch (error) {
    console.log('❌ Erreur lors de l\'accès au dashboard:', error.message)
    return false
  }
}

async function testDashboardPage() {
  console.log('\n🌐 Test de la page dashboard...')
  
  try {
    const response = await fetch(`${BASE_URL}/dashboard`)
    
    if (response.ok) {
      const html = await response.text()
      
      // Vérifier si la page contient le formulaire de login ou le dashboard
      const hasLoginForm = html.includes('Connectez-vous pour accéder')
      const hasDashboard = html.includes('Dashboard RH')
      
      console.log(`✅ Page dashboard accessible`)
      console.log(`   Contient formulaire login: ${hasLoginForm ? 'Oui' : 'Non'}`)
      console.log(`   Contient dashboard: ${hasDashboard ? 'Oui' : 'Non'}`)
      
      return true
    } else {
      console.log(`❌ Page dashboard inaccessible: HTTP ${response.status}`)
      return false
    }
  } catch (error) {
    console.log('❌ Erreur lors de l\'accès à la page:', error.message)
    return false
  }
}

async function main() {
  console.log('🧪 Tests d\'authentification du Dashboard')
  console.log('=========================================')
  
  // Vérifier que l'application est accessible
  try {
    const response = await fetch(`${BASE_URL}`)
    if (!response.ok) {
      throw new Error(`Application non accessible: HTTP ${response.status}`)
    }
    console.log('✅ Application accessible')
  } catch (error) {
    console.log('❌ Application non accessible:', error.message)
    console.log('💡 Assurez-vous que l\'application est démarrée avec "npm run dev"')
    process.exit(1)
  }
  
  let totalTests = 0
  let passedTests = 0
  
  // Test de connexion admin
  totalTests++
  const adminLogin = await testLogin('<EMAIL>', 'admin123')
  if (adminLogin.success) {
    passedTests++
    
    // Test dashboard avec authentification
    totalTests++
    if (await testDashboardWithAuth(adminLogin.token)) {
      passedTests++
    }
  }
  
  // Test de connexion avec fake data
  totalTests++
  const fakeLogin = await testLogin('<EMAIL>', 'password123')
  if (fakeLogin.success) {
    passedTests++
  }
  
  // Test dashboard sans authentification (mode test)
  totalTests++
  if (await testDashboardWithoutAuth()) {
    passedTests++
  }
  
  // Test de la page dashboard
  totalTests++
  if (await testDashboardPage()) {
    passedTests++
  }
  
  // Résumé final
  console.log('\n📋 Résumé des tests d\'authentification')
  console.log('======================================')
  console.log(`✅ Tests réussis: ${passedTests}/${totalTests}`)
  console.log(`❌ Tests échoués: ${totalTests - passedTests}/${totalTests}`)
  
  if (passedTests === totalTests) {
    console.log('\n🎉 Tous les tests d\'authentification sont passés!')
    console.log('\n💡 Instructions:')
    console.log('   1. Accédez au dashboard: http://localhost:3000/dashboard')
    console.log('   2. Connectez-vous avec: <EMAIL> / admin123')
    console.log('   3. Ou utilisez les fake data: [email] / password123')
  } else {
    console.log('\n⚠️ Certains tests ont échoué.')
    console.log('💡 Vérifiez:')
    console.log('   • La base de données est-elle initialisée?')
    console.log('   • Les comptes utilisateurs sont-ils créés?')
    console.log('   • L\'application est-elle démarrée?')
    process.exit(1)
  }
}

// Vérifier si node-fetch est disponible
try {
  require('node-fetch')
} catch (error) {
  console.log('❌ node-fetch n\'est pas installé')
  console.log('💡 Il devrait être installé avec les devDependencies')
  process.exit(1)
}

main()
