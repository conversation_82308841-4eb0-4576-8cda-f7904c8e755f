#!/bin/bash

# Script de configuration et de démarrage pour Karma Com Dashboard
# Ce script automatise l'installation et la configuration du projet

set -e

echo "🚀 Configuration de Karma Com Dashboard"
echo "======================================="

# Vérifier les prérequis
echo "📋 Vérification des prérequis..."

if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé. Veuillez installer Node.js 18+ avant de continuer."
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé. Veuillez installer Docker avant de continuer."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose n'est pas installé. Veuillez installer Docker Compose avant de continuer."
    exit 1
fi

echo "✅ Prérequis vérifiés"

# Installation des dépendances
echo "📦 Installation des dépendances..."
npm install

# Configuration de l'environnement
echo "⚙️ Configuration de l'environnement..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Fichier .env créé à partir de .env.example"
    echo "⚠️  Pensez à modifier les variables d'environnement dans .env si nécessaire"
else
    echo "✅ Fichier .env déjà existant"
fi

# Démarrage des services Docker
echo "🐳 Démarrage des services Docker..."
docker-compose up -d postgres pgadmin

# Attendre que PostgreSQL soit prêt
echo "⏳ Attente du démarrage de PostgreSQL..."
sleep 10

# Génération du client Prisma
echo "🔧 Génération du client Prisma..."
npx prisma generate

# Application des migrations
echo "🗄️ Application des migrations de base de données..."
npx prisma migrate dev --name init

# Seed de la base de données (optionnel)
echo "🌱 Initialisation des données de base..."
# npx prisma db seed (à implémenter si nécessaire)

echo ""
echo "🎉 Configuration terminée avec succès !"
echo ""
echo "📋 Informations utiles :"
echo "  • Application : http://localhost:3000"
echo "  • pgAdmin : http://localhost:5050 (<EMAIL> / admin123)"
echo "  • Base de données : localhost:5432"
echo ""
echo "🚀 Pour démarrer l'application en mode développement :"
echo "  npm run dev"
echo ""
echo "🐳 Pour gérer les services Docker :"
echo "  npm run docker:up    # Démarrer tous les services"
echo "  npm run docker:down  # Arrêter tous les services"
echo ""
echo "🔧 Pour gérer la base de données :"
echo "  npm run db:studio    # Ouvrir Prisma Studio"
echo "  npm run db:migrate   # Appliquer les migrations"
echo ""
