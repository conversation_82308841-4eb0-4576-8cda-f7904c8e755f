import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { UserType } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const type = searchParams.get('type')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Construire les filtres
    const where: any = {
      userType: {
        in: [UserType.ASSOCIATION, UserType.ORGANIZATION, UserType.VOLUNTEER]
      }
    }

    if (status) {
      where.profile = {
        membershipStatus: status.toUpperCase()
      }
    }

    if (type) {
      where.userType = type.toUpperCase()
    }

    // Récupérer les candidats avec pagination
    const [candidates, total] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          profile: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.user.count({ where })
    ])

    const formattedCandidates = candidates.map(candidate => ({
      id: candidate.id,
      name: candidate.name,
      email: candidate.email,
      phone: candidate.phone,
      userType: candidate.userType,
      type: candidate.userType === 'ASSOCIATION' ? 'Association' : 
            candidate.userType === 'ORGANIZATION' ? 'Organisation' : 'Bénévole',
      organization: candidate.profile?.organizationName || null,
      status: candidate.profile?.membershipStatus?.toLowerCase() || 'pending',
      membershipStatus: candidate.profile?.membershipStatus,
      date: candidate.createdAt.toISOString().split('T')[0],
      createdAt: candidate.createdAt.toISOString(),
      profile: {
        firstName: candidate.profile?.firstName,
        lastName: candidate.profile?.lastName,
        address: candidate.profile?.address,
        city: candidate.profile?.city,
        postalCode: candidate.profile?.postalCode,
        skills: candidate.profile?.skills || [],
        motivation: candidate.profile?.motivation,
        availability: candidate.profile?.availability
      }
    }))

    return NextResponse.json({
      candidates: formattedCandidates,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Erreur lors de la récupération des candidats:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      email, 
      name, 
      phone, 
      userType, 
      profile 
    } = body

    // Validation des données
    if (!email || !name || !userType) {
      return NextResponse.json(
        { error: 'Données manquantes' },
        { status: 400 }
      )
    }

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'Un utilisateur avec cet email existe déjà' },
        { status: 409 }
      )
    }

    // Créer l'utilisateur et son profil
    const user = await prisma.user.create({
      data: {
        email,
        name,
        phone,
        userType: userType as UserType,
        password: 'temp_password', // À remplacer par un système de génération de mot de passe
        profile: {
          create: {
            firstName: profile?.firstName || '',
            lastName: profile?.lastName || '',
            address: profile?.address || '',
            city: profile?.city || '',
            postalCode: profile?.postalCode || '',
            country: profile?.country || 'France',
            organizationName: profile?.organizationName || '',
            skills: profile?.skills || [],
            motivation: profile?.motivation || '',
            availability: profile?.availability || '',
            membershipStatus: 'PENDING'
          }
        }
      },
      include: {
        profile: true
      }
    })

    const formattedUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      userType: user.userType,
      type: user.userType === 'ASSOCIATION' ? 'Association' : 
            user.userType === 'ORGANIZATION' ? 'Organisation' : 'Bénévole',
      organization: user.profile?.organizationName || null,
      status: user.profile?.membershipStatus?.toLowerCase() || 'pending',
      date: user.createdAt.toISOString().split('T')[0],
      createdAt: user.createdAt.toISOString()
    }

    return NextResponse.json(formattedUser, { status: 201 })
  } catch (error) {
    console.error('Erreur lors de la création du candidat:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
