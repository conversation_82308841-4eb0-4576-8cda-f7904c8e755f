#!/bin/bash

# Script de configuration complète pour Karma Com Dashboard
# Ce script initialise tout : base de données, constantes, fake data et tests

set -e

echo "🚀 Configuration complète de Karma Com Dashboard"
echo "================================================"

# Vérifier les prérequis
echo "📋 Vérification des prérequis..."

if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé"
    exit 1
fi

echo "✅ Prérequis OK"

# Installation des dépendances
echo "📦 Installation des dépendances..."
npm install

# Démarrage de la base de données
echo "🐳 Démarrage de PostgreSQL..."
docker-compose up -d postgres pgadmin

# Attendre que PostgreSQL soit prêt
echo "⏳ Attente du démarrage de PostgreSQL..."
sleep 15

# Configuration de la base de données
echo "🗄️ Configuration de la base de données..."

# Générer le client Prisma
echo "  🔧 Génération du client Prisma..."
npx prisma generate

# Appliquer les migrations
echo "  📊 Application des migrations..."
npx prisma migrate dev --name "initial-setup"

# Initialiser les constantes
echo "  🌱 Initialisation des constantes..."
npm run db:seed-constants

# Créer les fake data
echo "  🎭 Création des données de test..."
npm run db:seed-fake

# Créer les comptes de base
echo "  👥 Création des comptes de base..."
npm run db:init

echo ""
echo "🧪 Lancement des tests de validation..."

# Démarrer l'application en arrière-plan pour les tests
echo "  🚀 Démarrage temporaire de l'application..."
npm run dev &
APP_PID=$!

# Attendre que l'application soit prête
sleep 10

# Tester les inscriptions
echo "  📝 Test des inscriptions..."
npm run test:inscriptions

# Tester le dashboard
echo "  📊 Test du dashboard..."
npm run test:dashboard

# Arrêter l'application de test
kill $APP_PID 2>/dev/null || true

echo ""
echo "🎉 Configuration complète terminée avec succès !"
echo ""
echo "📋 Résumé de l'installation :"
echo "  ✅ Base de données PostgreSQL configurée"
echo "  ✅ Constantes initialisées"
echo "  ✅ Données de test créées"
echo "  ✅ Comptes utilisateurs créés"
echo "  ✅ Tests validés"
echo ""
echo "🌐 Services disponibles :"
echo "  • Application : http://localhost:3000"
echo "  • Dashboard RH : http://localhost:3000/dashboard"
echo "  • pgAdmin : http://localhost:5050"
echo ""
echo "🔑 Comptes de connexion :"
echo "  • Admin RH : <EMAIL> / admin123"
echo "  • Fake data : [email] / password123"
echo ""
echo "🚀 Pour démarrer l'application :"
echo "  npm run dev"
echo ""
echo "📚 Consultez GUIDE-DEMARRAGE.md pour plus d'informations"
