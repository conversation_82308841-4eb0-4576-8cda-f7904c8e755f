/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsonwebtoken";
exports.ids = ["vendor-chunks/jsonwebtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/jsonwebtoken/decode.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/decode.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nmodule.exports = function(jwt, options) {\n    options = options || {};\n    var decoded = jws.decode(jwt, options);\n    if (!decoded) {\n        return null;\n    }\n    var payload = decoded.payload;\n    //try parse the payload\n    if (typeof payload === \"string\") {\n        try {\n            var obj = JSON.parse(payload);\n            if (obj !== null && typeof obj === \"object\") {\n                payload = obj;\n            }\n        } catch (e) {}\n    }\n    //return header if `complete` option is enabled.  header includes claims\n    //such as `kid` and `alg` used to select the key within a JWKS needed to\n    //verify the signature\n    if (options.complete === true) {\n        return {\n            header: decoded.header,\n            payload: payload,\n            signature: decoded.signature\n        };\n    }\n    return payload;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/decode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/index.js":
/*!********************************************!*\
  !*** ./node_modules/jsonwebtoken/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = {\n    decode: __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\"),\n    verify: __webpack_require__(/*! ./verify */ \"(rsc)/./node_modules/jsonwebtoken/verify.js\"),\n    sign: __webpack_require__(/*! ./sign */ \"(rsc)/./node_modules/jsonwebtoken/sign.js\"),\n    JsonWebTokenError: __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\"),\n    NotBeforeError: __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\"),\n    TokenExpiredError: __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUc7SUFDZkMsUUFBUUMsbUJBQU9BLENBQUM7SUFDaEJDLFFBQVFELG1CQUFPQSxDQUFDO0lBQ2hCRSxNQUFNRixtQkFBT0EsQ0FBQztJQUNkRyxtQkFBbUJILG1CQUFPQSxDQUFDO0lBQzNCSSxnQkFBZ0JKLG1CQUFPQSxDQUFDO0lBQ3hCSyxtQkFBbUJMLG1CQUFPQSxDQUFDO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2FybWEtY29tLWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vaW5kZXguanM/OWJlOCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHtcbiAgZGVjb2RlOiByZXF1aXJlKCcuL2RlY29kZScpLFxuICB2ZXJpZnk6IHJlcXVpcmUoJy4vdmVyaWZ5JyksXG4gIHNpZ246IHJlcXVpcmUoJy4vc2lnbicpLFxuICBKc29uV2ViVG9rZW5FcnJvcjogcmVxdWlyZSgnLi9saWIvSnNvbldlYlRva2VuRXJyb3InKSxcbiAgTm90QmVmb3JlRXJyb3I6IHJlcXVpcmUoJy4vbGliL05vdEJlZm9yZUVycm9yJyksXG4gIFRva2VuRXhwaXJlZEVycm9yOiByZXF1aXJlKCcuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvcicpLFxufTtcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiZGVjb2RlIiwicmVxdWlyZSIsInZlcmlmeSIsInNpZ24iLCJKc29uV2ViVG9rZW5FcnJvciIsIk5vdEJlZm9yZUVycm9yIiwiVG9rZW5FeHBpcmVkRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/JsonWebTokenError.js ***!
  \************************************************************/
/***/ ((module) => {

eval("var JsonWebTokenError = function(message, error) {\n    Error.call(this, message);\n    if (Error.captureStackTrace) {\n        Error.captureStackTrace(this, this.constructor);\n    }\n    this.name = \"JsonWebTokenError\";\n    this.message = message;\n    if (error) this.inner = error;\n};\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\nmodule.exports = JsonWebTokenError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rYXJtYS1jb20tZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvSnNvbldlYlRva2VuRXJyb3IuanM/MTZmMiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgSnNvbldlYlRva2VuRXJyb3IgPSBmdW5jdGlvbiAobWVzc2FnZSwgZXJyb3IpIHtcbiAgRXJyb3IuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgaWYoRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UpIHtcbiAgICBFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSh0aGlzLCB0aGlzLmNvbnN0cnVjdG9yKTtcbiAgfVxuICB0aGlzLm5hbWUgPSAnSnNvbldlYlRva2VuRXJyb3InO1xuICB0aGlzLm1lc3NhZ2UgPSBtZXNzYWdlO1xuICBpZiAoZXJyb3IpIHRoaXMuaW5uZXIgPSBlcnJvcjtcbn07XG5cbkpzb25XZWJUb2tlbkVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoRXJyb3IucHJvdG90eXBlKTtcbkpzb25XZWJUb2tlbkVycm9yLnByb3RvdHlwZS5jb25zdHJ1Y3RvciA9IEpzb25XZWJUb2tlbkVycm9yO1xuXG5tb2R1bGUuZXhwb3J0cyA9IEpzb25XZWJUb2tlbkVycm9yO1xuIl0sIm5hbWVzIjpbIkpzb25XZWJUb2tlbkVycm9yIiwibWVzc2FnZSIsImVycm9yIiwiRXJyb3IiLCJjYWxsIiwiY2FwdHVyZVN0YWNrVHJhY2UiLCJjb25zdHJ1Y3RvciIsIm5hbWUiLCJpbm5lciIsInByb3RvdHlwZSIsIk9iamVjdCIsImNyZWF0ZSIsIm1vZHVsZSIsImV4cG9ydHMiXSwibWFwcGluZ3MiOiJBQUFBLElBQUlBLG9CQUFvQixTQUFVQyxPQUFPLEVBQUVDLEtBQUs7SUFDOUNDLE1BQU1DLElBQUksQ0FBQyxJQUFJLEVBQUVIO0lBQ2pCLElBQUdFLE1BQU1FLGlCQUFpQixFQUFFO1FBQzFCRixNQUFNRSxpQkFBaUIsQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDQyxXQUFXO0lBQ2hEO0lBQ0EsSUFBSSxDQUFDQyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNOLE9BQU8sR0FBR0E7SUFDZixJQUFJQyxPQUFPLElBQUksQ0FBQ00sS0FBSyxHQUFHTjtBQUMxQjtBQUVBRixrQkFBa0JTLFNBQVMsR0FBR0MsT0FBT0MsTUFBTSxDQUFDUixNQUFNTSxTQUFTO0FBQzNEVCxrQkFBa0JTLFNBQVMsQ0FBQ0gsV0FBVyxHQUFHTjtBQUUxQ1ksT0FBT0MsT0FBTyxHQUFHYiIsImZpbGUiOiIocnNjKS8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL0pzb25XZWJUb2tlbkVycm9yLmpzIiwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js":
/*!*********************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/NotBeforeError.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar NotBeforeError = function(message, date) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"NotBeforeError\";\n    this.date = date;\n};\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\nNotBeforeError.prototype.constructor = NotBeforeError;\nmodule.exports = NotBeforeError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxvQkFBb0JDLG1CQUFPQSxDQUFDO0FBRWhDLElBQUlDLGlCQUFpQixTQUFVQyxPQUFPLEVBQUVDLElBQUk7SUFDMUNKLGtCQUFrQkssSUFBSSxDQUFDLElBQUksRUFBRUY7SUFDN0IsSUFBSSxDQUFDRyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNGLElBQUksR0FBR0E7QUFDZDtBQUVBRixlQUFlSyxTQUFTLEdBQUdDLE9BQU9DLE1BQU0sQ0FBQ1Qsa0JBQWtCTyxTQUFTO0FBRXBFTCxlQUFlSyxTQUFTLENBQUNHLFdBQVcsR0FBR1I7QUFFdkNTLE9BQU9DLE9BQU8sR0FBR1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rYXJtYS1jb20tZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvTm90QmVmb3JlRXJyb3IuanM/ODY2OCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgSnNvbldlYlRva2VuRXJyb3IgPSByZXF1aXJlKCcuL0pzb25XZWJUb2tlbkVycm9yJyk7XG5cbnZhciBOb3RCZWZvcmVFcnJvciA9IGZ1bmN0aW9uIChtZXNzYWdlLCBkYXRlKSB7XG4gIEpzb25XZWJUb2tlbkVycm9yLmNhbGwodGhpcywgbWVzc2FnZSk7XG4gIHRoaXMubmFtZSA9ICdOb3RCZWZvcmVFcnJvcic7XG4gIHRoaXMuZGF0ZSA9IGRhdGU7XG59O1xuXG5Ob3RCZWZvcmVFcnJvci5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKEpzb25XZWJUb2tlbkVycm9yLnByb3RvdHlwZSk7XG5cbk5vdEJlZm9yZUVycm9yLnByb3RvdHlwZS5jb25zdHJ1Y3RvciA9IE5vdEJlZm9yZUVycm9yO1xuXG5tb2R1bGUuZXhwb3J0cyA9IE5vdEJlZm9yZUVycm9yOyJdLCJuYW1lcyI6WyJKc29uV2ViVG9rZW5FcnJvciIsInJlcXVpcmUiLCJOb3RCZWZvcmVFcnJvciIsIm1lc3NhZ2UiLCJkYXRlIiwiY2FsbCIsIm5hbWUiLCJwcm90b3R5cGUiLCJPYmplY3QiLCJjcmVhdGUiLCJjb25zdHJ1Y3RvciIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/TokenExpiredError.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar TokenExpiredError = function(message, expiredAt) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"TokenExpiredError\";\n    this.expiredAt = expiredAt;\n};\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\nTokenExpiredError.prototype.constructor = TokenExpiredError;\nmodule.exports = TokenExpiredError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxvQkFBb0JDLG1CQUFPQSxDQUFDO0FBRWhDLElBQUlDLG9CQUFvQixTQUFVQyxPQUFPLEVBQUVDLFNBQVM7SUFDbERKLGtCQUFrQkssSUFBSSxDQUFDLElBQUksRUFBRUY7SUFDN0IsSUFBSSxDQUFDRyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNGLFNBQVMsR0FBR0E7QUFDbkI7QUFFQUYsa0JBQWtCSyxTQUFTLEdBQUdDLE9BQU9DLE1BQU0sQ0FBQ1Qsa0JBQWtCTyxTQUFTO0FBRXZFTCxrQkFBa0JLLFNBQVMsQ0FBQ0csV0FBVyxHQUFHUjtBQUUxQ1MsT0FBT0MsT0FBTyxHQUFHViIsInNvdXJjZXMiOlsid2VicGFjazovL2thcm1hLWNvbS1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcz85MGVjIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IHJlcXVpcmUoJy4vSnNvbldlYlRva2VuRXJyb3InKTtcblxudmFyIFRva2VuRXhwaXJlZEVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGV4cGlyZWRBdCkge1xuICBKc29uV2ViVG9rZW5FcnJvci5jYWxsKHRoaXMsIG1lc3NhZ2UpO1xuICB0aGlzLm5hbWUgPSAnVG9rZW5FeHBpcmVkRXJyb3InO1xuICB0aGlzLmV4cGlyZWRBdCA9IGV4cGlyZWRBdDtcbn07XG5cblRva2VuRXhwaXJlZEVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlKTtcblxuVG9rZW5FeHBpcmVkRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gVG9rZW5FeHBpcmVkRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gVG9rZW5FeHBpcmVkRXJyb3I7Il0sIm5hbWVzIjpbIkpzb25XZWJUb2tlbkVycm9yIiwicmVxdWlyZSIsIlRva2VuRXhwaXJlZEVycm9yIiwibWVzc2FnZSIsImV4cGlyZWRBdCIsImNhbGwiLCJuYW1lIiwicHJvdG90eXBlIiwiT2JqZWN0IiwiY3JlYXRlIiwiY29uc3RydWN0b3IiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js":
/*!************************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=15.7.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUV2QkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2thcm1hLWNvbS1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcz83M2Q5Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHNlbXZlciA9IHJlcXVpcmUoJ3NlbXZlcicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNlbXZlci5zYXRpc2ZpZXMocHJvY2Vzcy52ZXJzaW9uLCAnPj0xNS43LjAnKTtcbiJdLCJuYW1lcyI6WyJzZW12ZXIiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInNhdGlzZmllcyIsInByb2Nlc3MiLCJ2ZXJzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js":
/*!******************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/psSupported.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \"^6.12.0 || >=8.0.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUVyQkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2thcm1hLWNvbS1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcz9jOGQ0Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBzZW12ZXIgPSByZXF1aXJlKCdzZW12ZXInKTtcblxubW9kdWxlLmV4cG9ydHMgPSBzZW12ZXIuc2F0aXNmaWVzKHByb2Nlc3MudmVyc2lvbiwgJ142LjEyLjAgfHwgPj04LjAuMCcpO1xuIl0sIm5hbWVzIjpbInNlbXZlciIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwic2F0aXNmaWVzIiwicHJvY2VzcyIsInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js":
/*!********************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=16.9.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLFNBQVNDLG1CQUFPQSxDQUFDO0FBRXZCQyxPQUFPQyxPQUFPLEdBQUdILE9BQU9JLFNBQVMsQ0FBQ0MsUUFBUUMsT0FBTyxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2FybWEtY29tLWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL3JzYVBzc0tleURldGFpbHNTdXBwb3J0ZWQuanM/ZjkwOCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzZW12ZXIgPSByZXF1aXJlKCdzZW12ZXInKTtcblxubW9kdWxlLmV4cG9ydHMgPSBzZW12ZXIuc2F0aXNmaWVzKHByb2Nlc3MudmVyc2lvbiwgJz49MTYuOS4wJyk7XG4iXSwibmFtZXMiOlsic2VtdmVyIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJzYXRpc2ZpZXMiLCJwcm9jZXNzIiwidmVyc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/timespan.js":
/*!***************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/timespan.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var ms = __webpack_require__(/*! ms */ \"(rsc)/./node_modules/ms/index.js\");\nmodule.exports = function(time, iat) {\n    var timestamp = iat || Math.floor(Date.now() / 1000);\n    if (typeof time === \"string\") {\n        var milliseconds = ms(time);\n        if (typeof milliseconds === \"undefined\") {\n            return;\n        }\n        return Math.floor(timestamp + milliseconds / 1000);\n    } else if (typeof time === \"number\") {\n        return timestamp + time;\n    } else {\n        return;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxLQUFLQyxtQkFBT0EsQ0FBQztBQUVqQkMsT0FBT0MsT0FBTyxHQUFHLFNBQVVDLElBQUksRUFBRUMsR0FBRztJQUNsQyxJQUFJQyxZQUFZRCxPQUFPRSxLQUFLQyxLQUFLLENBQUNDLEtBQUtDLEdBQUcsS0FBSztJQUUvQyxJQUFJLE9BQU9OLFNBQVMsVUFBVTtRQUM1QixJQUFJTyxlQUFlWCxHQUFHSTtRQUN0QixJQUFJLE9BQU9PLGlCQUFpQixhQUFhO1lBQ3ZDO1FBQ0Y7UUFDQSxPQUFPSixLQUFLQyxLQUFLLENBQUNGLFlBQVlLLGVBQWU7SUFDL0MsT0FBTyxJQUFJLE9BQU9QLFNBQVMsVUFBVTtRQUNuQyxPQUFPRSxZQUFZRjtJQUNyQixPQUFPO1FBQ0w7SUFDRjtBQUVGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2FybWEtY29tLWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL3RpbWVzcGFuLmpzP2NlZjAiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIG1zID0gcmVxdWlyZSgnbXMnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAodGltZSwgaWF0KSB7XG4gIHZhciB0aW1lc3RhbXAgPSBpYXQgfHwgTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCk7XG5cbiAgaWYgKHR5cGVvZiB0aW1lID09PSAnc3RyaW5nJykge1xuICAgIHZhciBtaWxsaXNlY29uZHMgPSBtcyh0aW1lKTtcbiAgICBpZiAodHlwZW9mIG1pbGxpc2Vjb25kcyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgcmV0dXJuIE1hdGguZmxvb3IodGltZXN0YW1wICsgbWlsbGlzZWNvbmRzIC8gMTAwMCk7XG4gIH0gZWxzZSBpZiAodHlwZW9mIHRpbWUgPT09ICdudW1iZXInKSB7XG4gICAgcmV0dXJuIHRpbWVzdGFtcCArIHRpbWU7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuO1xuICB9XG5cbn07Il0sIm5hbWVzIjpbIm1zIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJ0aW1lIiwiaWF0IiwidGltZXN0YW1wIiwiTWF0aCIsImZsb29yIiwiRGF0ZSIsIm5vdyIsIm1pbGxpc2Vjb25kcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ASYMMETRIC_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./asymmetricKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\");\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./rsaPssKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\");\nconst allowedAlgorithmsForKeys = {\n    \"ec\": [\n        \"ES256\",\n        \"ES384\",\n        \"ES512\"\n    ],\n    \"rsa\": [\n        \"RS256\",\n        \"PS256\",\n        \"RS384\",\n        \"PS384\",\n        \"RS512\",\n        \"PS512\"\n    ],\n    \"rsa-pss\": [\n        \"PS256\",\n        \"PS384\",\n        \"PS512\"\n    ]\n};\nconst allowedCurves = {\n    ES256: \"prime256v1\",\n    ES384: \"secp384r1\",\n    ES512: \"secp521r1\"\n};\nmodule.exports = function(algorithm, key) {\n    if (!algorithm || !key) return;\n    const keyType = key.asymmetricKeyType;\n    if (!keyType) return;\n    const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n    if (!allowedAlgorithms) {\n        throw new Error(`Unknown key type \"${keyType}\".`);\n    }\n    if (!allowedAlgorithms.includes(algorithm)) {\n        throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(\", \")}.`);\n    }\n    /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */ /* istanbul ignore next */ if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n        switch(keyType){\n            case \"ec\":\n                const keyCurve = key.asymmetricKeyDetails.namedCurve;\n                const allowedCurve = allowedCurves[algorithm];\n                if (keyCurve !== allowedCurve) {\n                    throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n                }\n                break;\n            case \"rsa-pss\":\n                if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n                    const length = parseInt(algorithm.slice(-3), 10);\n                    const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n                    if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                    if (saltLength !== undefined && saltLength > length >> 3) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                }\n                break;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/sign.js":
/*!*******************************************!*\
  !*** ./node_modules/jsonwebtoken/sign.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst includes = __webpack_require__(/*! lodash.includes */ \"(rsc)/./node_modules/lodash.includes/index.js\");\nconst isBoolean = __webpack_require__(/*! lodash.isboolean */ \"(rsc)/./node_modules/lodash.isboolean/index.js\");\nconst isInteger = __webpack_require__(/*! lodash.isinteger */ \"(rsc)/./node_modules/lodash.isinteger/index.js\");\nconst isNumber = __webpack_require__(/*! lodash.isnumber */ \"(rsc)/./node_modules/lodash.isnumber/index.js\");\nconst isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(rsc)/./node_modules/lodash.isplainobject/index.js\");\nconst isString = __webpack_require__(/*! lodash.isstring */ \"(rsc)/./node_modules/lodash.isstring/index.js\");\nconst once = __webpack_require__(/*! lodash.once */ \"(rsc)/./node_modules/lodash.once/index.js\");\nconst { KeyObject, createSecretKey, createPrivateKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst SUPPORTED_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\",\n    \"ES256\",\n    \"ES384\",\n    \"ES512\",\n    \"HS256\",\n    \"HS384\",\n    \"HS512\",\n    \"none\"\n];\nif (PS_SUPPORTED) {\n    SUPPORTED_ALGS.splice(3, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nconst sign_options_schema = {\n    expiresIn: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"expiresIn\" should be a number of seconds or string representing a timespan'\n    },\n    notBefore: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"notBefore\" should be a number of seconds or string representing a timespan'\n    },\n    audience: {\n        isValid: function(value) {\n            return isString(value) || Array.isArray(value);\n        },\n        message: '\"audience\" must be a string or array'\n    },\n    algorithm: {\n        isValid: includes.bind(null, SUPPORTED_ALGS),\n        message: '\"algorithm\" must be a valid string enum value'\n    },\n    header: {\n        isValid: isPlainObject,\n        message: '\"header\" must be an object'\n    },\n    encoding: {\n        isValid: isString,\n        message: '\"encoding\" must be a string'\n    },\n    issuer: {\n        isValid: isString,\n        message: '\"issuer\" must be a string'\n    },\n    subject: {\n        isValid: isString,\n        message: '\"subject\" must be a string'\n    },\n    jwtid: {\n        isValid: isString,\n        message: '\"jwtid\" must be a string'\n    },\n    noTimestamp: {\n        isValid: isBoolean,\n        message: '\"noTimestamp\" must be a boolean'\n    },\n    keyid: {\n        isValid: isString,\n        message: '\"keyid\" must be a string'\n    },\n    mutatePayload: {\n        isValid: isBoolean,\n        message: '\"mutatePayload\" must be a boolean'\n    },\n    allowInsecureKeySizes: {\n        isValid: isBoolean,\n        message: '\"allowInsecureKeySizes\" must be a boolean'\n    },\n    allowInvalidAsymmetricKeyTypes: {\n        isValid: isBoolean,\n        message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'\n    }\n};\nconst registered_claims_schema = {\n    iat: {\n        isValid: isNumber,\n        message: '\"iat\" should be a number of seconds'\n    },\n    exp: {\n        isValid: isNumber,\n        message: '\"exp\" should be a number of seconds'\n    },\n    nbf: {\n        isValid: isNumber,\n        message: '\"nbf\" should be a number of seconds'\n    }\n};\nfunction validate(schema, allowUnknown, object, parameterName) {\n    if (!isPlainObject(object)) {\n        throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n    }\n    Object.keys(object).forEach(function(key) {\n        const validator = schema[key];\n        if (!validator) {\n            if (!allowUnknown) {\n                throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n            }\n            return;\n        }\n        if (!validator.isValid(object[key])) {\n            throw new Error(validator.message);\n        }\n    });\n}\nfunction validateOptions(options) {\n    return validate(sign_options_schema, false, options, \"options\");\n}\nfunction validatePayload(payload) {\n    return validate(registered_claims_schema, true, payload, \"payload\");\n}\nconst options_to_payload = {\n    \"audience\": \"aud\",\n    \"issuer\": \"iss\",\n    \"subject\": \"sub\",\n    \"jwtid\": \"jti\"\n};\nconst options_for_objects = [\n    \"expiresIn\",\n    \"notBefore\",\n    \"noTimestamp\",\n    \"audience\",\n    \"issuer\",\n    \"subject\",\n    \"jwtid\"\n];\nmodule.exports = function(payload, secretOrPrivateKey, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = {};\n    } else {\n        options = options || {};\n    }\n    const isObjectPayload = typeof payload === \"object\" && !Buffer.isBuffer(payload);\n    const header = Object.assign({\n        alg: options.algorithm || \"HS256\",\n        typ: isObjectPayload ? \"JWT\" : undefined,\n        kid: options.keyid\n    }, options.header);\n    function failure(err) {\n        if (callback) {\n            return callback(err);\n        }\n        throw err;\n    }\n    if (!secretOrPrivateKey && options.algorithm !== \"none\") {\n        return failure(new Error(\"secretOrPrivateKey must have a value\"));\n    }\n    if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n        try {\n            secretOrPrivateKey = createPrivateKey(secretOrPrivateKey);\n        } catch (_) {\n            try {\n                secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === \"string\" ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey);\n            } catch (_) {\n                return failure(new Error(\"secretOrPrivateKey is not valid key material\"));\n            }\n        }\n    }\n    if (header.alg.startsWith(\"HS\") && secretOrPrivateKey.type !== \"secret\") {\n        return failure(new Error(`secretOrPrivateKey must be a symmetric key when using ${header.alg}`));\n    } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n        if (secretOrPrivateKey.type !== \"private\") {\n            return failure(new Error(`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInsecureKeySizes && !header.alg.startsWith(\"ES\") && secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n        secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n            return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n        }\n    }\n    if (typeof payload === \"undefined\") {\n        return failure(new Error(\"payload is required\"));\n    } else if (isObjectPayload) {\n        try {\n            validatePayload(payload);\n        } catch (error) {\n            return failure(error);\n        }\n        if (!options.mutatePayload) {\n            payload = Object.assign({}, payload);\n        }\n    } else {\n        const invalid_options = options_for_objects.filter(function(opt) {\n            return typeof options[opt] !== \"undefined\";\n        });\n        if (invalid_options.length > 0) {\n            return failure(new Error(\"invalid \" + invalid_options.join(\",\") + \" option for \" + typeof payload + \" payload\"));\n        }\n    }\n    if (typeof payload.exp !== \"undefined\" && typeof options.expiresIn !== \"undefined\") {\n        return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n    }\n    if (typeof payload.nbf !== \"undefined\" && typeof options.notBefore !== \"undefined\") {\n        return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n    }\n    try {\n        validateOptions(options);\n    } catch (error) {\n        return failure(error);\n    }\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n        try {\n            validateAsymmetricKey(header.alg, secretOrPrivateKey);\n        } catch (error) {\n            return failure(error);\n        }\n    }\n    const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n    if (options.noTimestamp) {\n        delete payload.iat;\n    } else if (isObjectPayload) {\n        payload.iat = timestamp;\n    }\n    if (typeof options.notBefore !== \"undefined\") {\n        try {\n            payload.nbf = timespan(options.notBefore, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.nbf === \"undefined\") {\n            return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    if (typeof options.expiresIn !== \"undefined\" && typeof payload === \"object\") {\n        try {\n            payload.exp = timespan(options.expiresIn, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.exp === \"undefined\") {\n            return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    Object.keys(options_to_payload).forEach(function(key) {\n        const claim = options_to_payload[key];\n        if (typeof options[key] !== \"undefined\") {\n            if (typeof payload[claim] !== \"undefined\") {\n                return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n            }\n            payload[claim] = options[key];\n        }\n    });\n    const encoding = options.encoding || \"utf8\";\n    if (typeof callback === \"function\") {\n        callback = callback && once(callback);\n        jws.createSign({\n            header: header,\n            privateKey: secretOrPrivateKey,\n            payload: payload,\n            encoding: encoding\n        }).once(\"error\", callback).once(\"done\", function(signature) {\n            // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n            if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n                return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n            }\n            callback(null, signature);\n        });\n    } else {\n        let signature = jws.sign({\n            header: header,\n            payload: payload,\n            secret: secretOrPrivateKey,\n            encoding: encoding\n        });\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n            throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`);\n        }\n        return signature;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/sign.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/verify.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/verify.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const JsonWebTokenError = __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nconst NotBeforeError = __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\");\nconst TokenExpiredError = __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\");\nconst decode = __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\");\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst { KeyObject, createSecretKey, createPublicKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PUB_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst EC_KEY_ALGS = [\n    \"ES256\",\n    \"ES384\",\n    \"ES512\"\n];\nconst RSA_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst HS_ALGS = [\n    \"HS256\",\n    \"HS384\",\n    \"HS512\"\n];\nif (PS_SUPPORTED) {\n    PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n    RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nmodule.exports = function(jwtString, secretOrPublicKey, options, callback) {\n    if (typeof options === \"function\" && !callback) {\n        callback = options;\n        options = {};\n    }\n    if (!options) {\n        options = {};\n    }\n    //clone this object since we are going to mutate it.\n    options = Object.assign({}, options);\n    let done;\n    if (callback) {\n        done = callback;\n    } else {\n        done = function(err, data) {\n            if (err) throw err;\n            return data;\n        };\n    }\n    if (options.clockTimestamp && typeof options.clockTimestamp !== \"number\") {\n        return done(new JsonWebTokenError(\"clockTimestamp must be a number\"));\n    }\n    if (options.nonce !== undefined && (typeof options.nonce !== \"string\" || options.nonce.trim() === \"\")) {\n        return done(new JsonWebTokenError(\"nonce must be a non-empty string\"));\n    }\n    if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== \"boolean\") {\n        return done(new JsonWebTokenError(\"allowInvalidAsymmetricKeyTypes must be a boolean\"));\n    }\n    const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n    if (!jwtString) {\n        return done(new JsonWebTokenError(\"jwt must be provided\"));\n    }\n    if (typeof jwtString !== \"string\") {\n        return done(new JsonWebTokenError(\"jwt must be a string\"));\n    }\n    const parts = jwtString.split(\".\");\n    if (parts.length !== 3) {\n        return done(new JsonWebTokenError(\"jwt malformed\"));\n    }\n    let decodedToken;\n    try {\n        decodedToken = decode(jwtString, {\n            complete: true\n        });\n    } catch (err) {\n        return done(err);\n    }\n    if (!decodedToken) {\n        return done(new JsonWebTokenError(\"invalid token\"));\n    }\n    const header = decodedToken.header;\n    let getSecret;\n    if (typeof secretOrPublicKey === \"function\") {\n        if (!callback) {\n            return done(new JsonWebTokenError(\"verify must be called asynchronous if secret or public key is provided as a callback\"));\n        }\n        getSecret = secretOrPublicKey;\n    } else {\n        getSecret = function(header, secretCallback) {\n            return secretCallback(null, secretOrPublicKey);\n        };\n    }\n    return getSecret(header, function(err, secretOrPublicKey) {\n        if (err) {\n            return done(new JsonWebTokenError(\"error in secret or public key callback: \" + err.message));\n        }\n        const hasSignature = parts[2].trim() !== \"\";\n        if (!hasSignature && secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"jwt signature is required\"));\n        }\n        if (hasSignature && !secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"secret or public key must be provided\"));\n        }\n        if (!hasSignature && !options.algorithms) {\n            return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n        }\n        if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n            try {\n                secretOrPublicKey = createPublicKey(secretOrPublicKey);\n            } catch (_) {\n                try {\n                    secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === \"string\" ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n                } catch (_) {\n                    return done(new JsonWebTokenError(\"secretOrPublicKey is not valid key material\"));\n                }\n            }\n        }\n        if (!options.algorithms) {\n            if (secretOrPublicKey.type === \"secret\") {\n                options.algorithms = HS_ALGS;\n            } else if ([\n                \"rsa\",\n                \"rsa-pss\"\n            ].includes(secretOrPublicKey.asymmetricKeyType)) {\n                options.algorithms = RSA_KEY_ALGS;\n            } else if (secretOrPublicKey.asymmetricKeyType === \"ec\") {\n                options.algorithms = EC_KEY_ALGS;\n            } else {\n                options.algorithms = PUB_KEY_ALGS;\n            }\n        }\n        if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n            return done(new JsonWebTokenError(\"invalid algorithm\"));\n        }\n        if (header.alg.startsWith(\"HS\") && secretOrPublicKey.type !== \"secret\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be a symmetric key when using ${header.alg}`));\n        } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== \"public\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInvalidAsymmetricKeyTypes) {\n            try {\n                validateAsymmetricKey(header.alg, secretOrPublicKey);\n            } catch (e) {\n                return done(e);\n            }\n        }\n        let valid;\n        try {\n            valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n        } catch (e) {\n            return done(e);\n        }\n        if (!valid) {\n            return done(new JsonWebTokenError(\"invalid signature\"));\n        }\n        const payload = decodedToken.payload;\n        if (typeof payload.nbf !== \"undefined\" && !options.ignoreNotBefore) {\n            if (typeof payload.nbf !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid nbf value\"));\n            }\n            if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n                return done(new NotBeforeError(\"jwt not active\", new Date(payload.nbf * 1000)));\n            }\n        }\n        if (typeof payload.exp !== \"undefined\" && !options.ignoreExpiration) {\n            if (typeof payload.exp !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid exp value\"));\n            }\n            if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"jwt expired\", new Date(payload.exp * 1000)));\n            }\n        }\n        if (options.audience) {\n            const audiences = Array.isArray(options.audience) ? options.audience : [\n                options.audience\n            ];\n            const target = Array.isArray(payload.aud) ? payload.aud : [\n                payload.aud\n            ];\n            const match = target.some(function(targetAudience) {\n                return audiences.some(function(audience) {\n                    return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n                });\n            });\n            if (!match) {\n                return done(new JsonWebTokenError(\"jwt audience invalid. expected: \" + audiences.join(\" or \")));\n            }\n        }\n        if (options.issuer) {\n            const invalid_issuer = typeof options.issuer === \"string\" && payload.iss !== options.issuer || Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1;\n            if (invalid_issuer) {\n                return done(new JsonWebTokenError(\"jwt issuer invalid. expected: \" + options.issuer));\n            }\n        }\n        if (options.subject) {\n            if (payload.sub !== options.subject) {\n                return done(new JsonWebTokenError(\"jwt subject invalid. expected: \" + options.subject));\n            }\n        }\n        if (options.jwtid) {\n            if (payload.jti !== options.jwtid) {\n                return done(new JsonWebTokenError(\"jwt jwtid invalid. expected: \" + options.jwtid));\n            }\n        }\n        if (options.nonce) {\n            if (payload.nonce !== options.nonce) {\n                return done(new JsonWebTokenError(\"jwt nonce invalid. expected: \" + options.nonce));\n            }\n        }\n        if (options.maxAge) {\n            if (typeof payload.iat !== \"number\") {\n                return done(new JsonWebTokenError(\"iat required when maxAge is specified\"));\n            }\n            const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n            if (typeof maxAgeTimestamp === \"undefined\") {\n                return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n            }\n            if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"maxAge exceeded\", new Date(maxAgeTimestamp * 1000)));\n            }\n        }\n        if (options.complete === true) {\n            const signature = decodedToken.signature;\n            return done(null, {\n                header: header,\n                payload: payload,\n                signature: signature\n            });\n        }\n        return done(null, payload);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/verify.js\n");

/***/ })

};
;