"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/constants/route";
exports.ids = ["app/api/constants/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconstants%2Froute&page=%2Fapi%2Fconstants%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconstants%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconstants%2Froute&page=%2Fapi%2Fconstants%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconstants%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_hamza_bedoui_Documents_mesDocs_AI_KCS_augment_kcs_src_app_api_constants_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/constants/route.ts */ \"(rsc)/./src/app/api/constants/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/constants/route\",\n        pathname: \"/api/constants\",\n        filename: \"route\",\n        bundlePath: \"app/api/constants/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\api\\\\constants\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_hamza_bedoui_Documents_mesDocs_AI_KCS_augment_kcs_src_app_api_constants_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/constants/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconstants%2Froute&page=%2Fapi%2Fconstants%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconstants%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/constants/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/constants/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function GET() {\n    try {\n        const [organizationTypes, sectors, partnershipTypes, skills, departments] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.organizationType.findMany({\n                where: {\n                    isActive: true\n                },\n                orderBy: {\n                    order: \"asc\"\n                },\n                select: {\n                    id: true,\n                    name: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.sector.findMany({\n                where: {\n                    isActive: true\n                },\n                orderBy: {\n                    order: \"asc\"\n                },\n                select: {\n                    id: true,\n                    name: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.partnershipType.findMany({\n                where: {\n                    isActive: true\n                },\n                orderBy: {\n                    order: \"asc\"\n                },\n                select: {\n                    id: true,\n                    name: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.skill.findMany({\n                where: {\n                    isActive: true\n                },\n                orderBy: {\n                    order: \"asc\"\n                },\n                select: {\n                    id: true,\n                    name: true,\n                    category: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.departmentOption.findMany({\n                where: {\n                    isActive: true\n                },\n                orderBy: {\n                    order: \"asc\"\n                },\n                select: {\n                    id: true,\n                    name: true\n                }\n            })\n        ]);\n        // Grouper les compétences par catégorie\n        const skillsByCategory = skills.reduce((acc, skill)=>{\n            const category = skill.category || \"Autre\";\n            if (!acc[category]) {\n                acc[category] = [];\n            }\n            acc[category].push({\n                id: skill.id,\n                name: skill.name\n            });\n            return acc;\n        }, {});\n        // Options de disponibilité (statiques pour l'instant)\n        const availabilityOptions = [\n            \"Quelques heures par semaine\",\n            \"Une demi-journ\\xe9e par semaine\",\n            \"Une journ\\xe9e par semaine\",\n            \"Plusieurs jours par semaine\",\n            \"Ponctuellement selon les projets\",\n            \"Disponibilit\\xe9 flexible\"\n        ];\n        // Options de taille d'entreprise (statiques)\n        const employeeCountOptions = [\n            \"1-10\",\n            \"11-50\",\n            \"51-200\",\n            \"201-500\",\n            \"500+\"\n        ];\n        // Options de budget (statiques)\n        const budgetOptions = [\n            \"< 5 000€\",\n            \"5 000€ - 15 000€\",\n            \"15 000€ - 50 000€\",\n            \"50 000€ - 100 000€\",\n            \"> 100 000€\"\n        ];\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            organizationTypes,\n            sectors,\n            partnershipTypes,\n            skills,\n            skillsByCategory,\n            departments,\n            availabilityOptions,\n            employeeCountOptions,\n            budgetOptions\n        });\n    } catch (error) {\n        console.error(\"Erreur lors de la r\\xe9cup\\xe9ration des constantes:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Erreur interne du serveur\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/constants/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rYXJtYS1jb20tZGFzaGJvYXJkLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconstants%2Froute&page=%2Fapi%2Fconstants%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconstants%2Froute.ts&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();