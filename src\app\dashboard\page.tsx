'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Logo from '@/components/ui/Logo'
import LoginForm from '@/components/dashboard/LoginForm'
import { useAuth } from '@/hooks/useAuth'
export const dynamic = 'force-dynamic'

import {
  Users,
  Building2,
  Heart,
  Calendar,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Search,
  Filter,
  Download,
  Bell,
  Settings,
  LogOut
} from 'lucide-react'

// Interface pour les candidatures récentes
interface RecentApplication {
  id: string
  name: string
  email: string
  type: string
  organization: string | null
  status: string
  date: string
  userType?: string
  membershipStatus?: string
  createdAt?: string
}

export default function DashboardPage() {
  const { isAuthenticated, loading: authLoading, login, logout, getAuthHeaders } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [loginError, setLoginError] = useState<string | null>(null)
  const [loginLoading, setLoginLoading] = useState(false)

  // Fonction de connexion
  const handleLogin = async (email: string, password: string): Promise<boolean> => {
    setLoginLoading(true)
    setLoginError(null)

    try {
      const success = await login(email, password)
      if (!success) {
        setLoginError('Email ou mot de passe incorrect')
      }
      return success
    } catch (error) {
      setLoginError('Erreur lors de la connexion')
      return false
    } finally {
      setLoginLoading(false)
    }
  }

  // Fonction de déconnexion
  const handleLogout = () => {
    logout()
  }

  // Charger les données du dashboard
  useEffect(() => {
    if (!isAuthenticated || authLoading) {
      return
    }

    const fetchDashboardData = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/dashboard/stats', {
          headers: getAuthHeaders()
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        setDashboardData(data)
      } catch (err) {
        console.error('Erreur lors du chargement des données:', err)
        setError(err instanceof Error ? err.message : 'Erreur inconnue')
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [isAuthenticated, authLoading, getAuthHeaders])

  // Données par défaut si pas encore chargées
  const stats = dashboardData?.overview || {
    totalCandidates: 0,
    pendingApplications: 0,
    scheduledInterviews: 0,
    approvedMembers: 0
  }

  // Données des candidatures récentes depuis l'API
  const recentApplications = dashboardData?.recentApplications || []

  // Données des entretiens à venir (simulées pour l'instant)
  const upcomingInterviews = [
    {
      id: 1,
      candidateName: 'Sophie Laurent',
      type: 'Découverte',
      date: '2024-01-16',
      time: '14:00',
      status: 'confirmed'
    },
    {
      id: 2,
      candidateName: 'Marc Rousseau',
      type: 'Intégration',
      date: '2024-01-16',
      time: '16:30',
      status: 'pending'
    },
    {
      id: 3,
      candidateName: 'Alice Bernard',
      type: 'Suivi',
      date: '2024-01-17',
      time: '10:00',
      status: 'confirmed'
    }
  ]

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'En attente', class: 'status-pending' },
      approved: { label: 'Approuvé', class: 'status-approved' },
      rejected: { label: 'Rejeté', class: 'status-rejected' },
      interview: { label: 'Entretien', class: 'status-active' },
      active: { label: 'Actif', class: 'status-approved' },
      confirmed: { label: 'Confirmé', class: 'status-approved' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, class: 'status-pending' }
    return <span className={`status-badge ${config.class}`}>{config.label}</span>
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Association':
        return <Heart size={16} className="text-primary-600" />
      case 'Organisation':
        return <Building2 size={16} className="text-secondary-600" />
      case 'Bénévole':
        return <Users size={16} className="text-accent-600" />
      default:
        return <Users size={16} className="text-gray-600" />
    }
  }

  // Afficher le formulaire de login si pas authentifié
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="mt-4 text-gray-600">Vérification de l'authentification...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <LoginForm
        onLogin={handleLogin}
        loading={loginLoading}
        error={loginError || undefined}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Logo size="md" showText={true} />
              </Link>
              <div className="hidden md:block">
                <h1 className="text-xl font-semibold text-gray-900">Dashboard RH</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <Bell size={20} />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <Settings size={20} />
              </button>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">RH</span>
                </div>
                <span className="hidden md:block text-sm font-medium text-gray-700">Admin RH</span>
              </div>
              <button
                onClick={handleLogout}
                className="p-2 text-gray-400 hover:text-red-600 transition-colors duration-200"
                title="Se déconnecter"
              >
                <LogOut size={20} />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Indicateur de chargement */}
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Chargement des données...</p>
          </div>
        )}

        {/* Gestion d'erreur */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex">
              <XCircle className="text-red-400 mr-3" size={20} />
              <div>
                <h3 className="text-red-800 font-medium">Erreur de chargement</h3>
                <p className="text-red-600 text-sm mt-1">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="text-red-600 hover:text-red-800 text-sm underline mt-2"
                >
                  Réessayer
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Contenu principal */}
        {!loading && !error && (
          <>
        {/* Navigation tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Vue d\'ensemble', icon: TrendingUp },
              { id: 'applications', label: 'Candidatures', icon: Users },
              { id: 'interviews', label: 'Entretiens', icon: Calendar },
              { id: 'members', label: 'Membres', icon: CheckCircle }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'bg-primary-100 text-primary-700 border-b-2 border-primary-600'
                    : 'text-gray-600 hover:text-primary-600 hover:bg-gray-100'
                }`}
              >
                <tab.icon size={18} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Vue d'ensemble */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Statistiques */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Candidats</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.totalCandidates}</p>
                  </div>
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Users className="text-primary-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">En attente</p>
                    <p className="text-3xl font-bold text-yellow-600">{stats.pendingApplications}</p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <Clock className="text-yellow-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Entretiens prévus</p>
                    <p className="text-3xl font-bold text-blue-600">{stats.scheduledInterviews}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Calendar className="text-blue-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Membres actifs</p>
                    <p className="text-3xl font-bold text-green-600">{stats.activeMembers}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <CheckCircle className="text-green-600" size={24} />
                  </div>
                </div>
              </div>
            </div>

            {/* Candidatures récentes */}
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Candidatures récentes</h2>
                <Link
                  href="/dashboard/applications"
                  className="text-primary-600 hover:text-primary-700 font-medium text-sm"
                >
                  Voir tout
                </Link>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Candidat
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Organisation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentApplications.map((application: RecentApplication) => (
                      <tr key={application.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{application.name}</div>
                            <div className="text-sm text-gray-500">{application.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            {getTypeIcon(application.type)}
                            <span className="text-sm text-gray-900">{application.type}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {application.organization || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(application.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(application.date).toLocaleDateString('fr-FR')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Entretiens à venir */}
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Entretiens à venir</h2>
                <button className="karma-button-primary flex items-center space-x-2">
                  <Plus size={16} />
                  <span>Planifier</span>
                </button>
              </div>
              
              <div className="space-y-4">
                {upcomingInterviews.map((interview) => (
                  <div key={interview.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <Calendar className="text-primary-600" size={20} />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{interview.candidateName}</h3>
                        <p className="text-sm text-gray-500">
                          {interview.type} • {new Date(interview.date).toLocaleDateString('fr-FR')} à {interview.time}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(interview.status)}
                      <button className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                        Détails
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Autres onglets - placeholder */}
        {activeTab !== 'overview' && (
          <div className="dashboard-card text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="text-gray-400" size={32} />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Section en développement
            </h3>
            <p className="text-gray-500">
              Cette section sera bientôt disponible.
            </p>
          </div>
        )}
        </>
        )}
      </div>
    </div>
  )
}
