import { z } from 'zod'

// Schéma de validation pour l'inscription d'association
export const associationRegistrationSchema = z.object({
  // Informations de connexion
  email: z.string().email('Format d\'email invalide'),
  password: z.string().min(8, 'Le mot de passe doit contenir au moins 8 caractères'),
  confirmPassword: z.string(),
  
  // Informations personnelles du représentant
  firstName: z.string().min(1, 'Le prénom est requis'),
  lastName: z.string().min(1, 'Le nom est requis'),
  phone: z.string().optional(),
  
  // Informations de l'association
  organizationName: z.string().min(1, 'Le nom de l\'association est requis'),
  siret: z.string().regex(/^\d{14}$/, 'Le SIRET doit contenir 14 chiffres').optional().or(z.literal('')),
  website: z.string().url('URL invalide').optional().or(z.literal('')),
  description: z.string().min(10, 'La description doit contenir au moins 10 caractères'),
  
  // Adresse
  address: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().default('France'),
  
  // Autres
  motivation: z.string().optional(),
  acceptTerms: z.boolean().refine(val => val === true, 'Vous devez accepter les conditions'),
  acceptRGPD: z.boolean().refine(val => val === true, 'Vous devez accepter la politique de confidentialité')
}).refine(data => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword']
})

// Schéma de validation pour l'inscription d'organisation
export const organizationRegistrationSchema = z.object({
  // Informations de connexion
  email: z.string().email('Format d\'email invalide'),
  password: z.string().min(8, 'Le mot de passe doit contenir au moins 8 caractères'),
  confirmPassword: z.string(),
  
  // Informations personnelles du représentant
  firstName: z.string().min(1, 'Le prénom est requis'),
  lastName: z.string().min(1, 'Le nom est requis'),
  phone: z.string().optional(),
  
  // Informations de l'organisation
  organizationName: z.string().min(1, 'Le nom de l\'organisation est requis'),
  organizationType: z.string().min(1, 'Le type d\'organisation est requis'),
  sector: z.string().optional(),
  siret: z.string().regex(/^\d{14}$/, 'Le SIRET doit contenir 14 chiffres').optional().or(z.literal('')),
  website: z.string().url('URL invalide').optional().or(z.literal('')),
  employeeCount: z.string().optional(),
  description: z.string().min(10, 'La description doit contenir au moins 10 caractères'),
  
  // Adresse
  address: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().default('France'),
  
  // Partenariat
  partnershipType: z.string().optional(),
  budget: z.string().optional(),
  motivation: z.string().optional(),
  
  // Conditions
  acceptTerms: z.boolean().refine(val => val === true, 'Vous devez accepter les conditions'),
  acceptRGPD: z.boolean().refine(val => val === true, 'Vous devez accepter la politique de confidentialité')
}).refine(data => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword']
})

// Schéma de validation pour l'inscription de bénévole
export const volunteerRegistrationSchema = z.object({
  // Informations de connexion
  email: z.string().email('Format d\'email invalide'),
  password: z.string().min(8, 'Le mot de passe doit contenir au moins 8 caractères'),
  confirmPassword: z.string(),
  
  // Informations personnelles
  firstName: z.string().min(1, 'Le prénom est requis'),
  lastName: z.string().min(1, 'Le nom est requis'),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  
  // Adresse
  address: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().default('France'),
  
  // Compétences et disponibilités
  skills: z.array(z.string()).default([]),
  customSkills: z.string().optional(),
  availability: z.string().optional(),
  experience: z.string().optional(),
  motivation: z.string().min(10, 'La motivation doit contenir au moins 10 caractères'),
  
  // Préférences
  preferredDepartments: z.array(z.string()).default([]),
  remoteWork: z.boolean().default(false),
  
  // Conditions
  acceptTerms: z.boolean().refine(val => val === true, 'Vous devez accepter les conditions'),
  acceptRGPD: z.boolean().refine(val => val === true, 'Vous devez accepter la politique de confidentialité')
}).refine(data => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword']
})

// Schéma de validation pour la connexion
export const loginSchema = z.object({
  email: z.string().email('Format d\'email invalide'),
  password: z.string().min(1, 'Le mot de passe est requis')
})

// Schéma de validation pour la création de rendez-vous
export const appointmentSchema = z.object({
  title: z.string().min(1, 'Le titre est requis'),
  description: z.string().optional(),
  scheduledAt: z.string().datetime('Date et heure invalides'),
  duration: z.number().min(15).max(480).default(60),
  type: z.enum(['DISCOVERY', 'INTEGRATION', 'FOLLOW_UP', 'INTERVIEW']),
  candidateId: z.string().min(1, 'Le candidat est requis'),
  notes: z.string().optional()
})

export type AssociationRegistrationData = z.infer<typeof associationRegistrationSchema>
export type OrganizationRegistrationData = z.infer<typeof organizationRegistrationSchema>
export type VolunteerRegistrationData = z.infer<typeof volunteerRegistrationSchema>
export type LoginData = z.infer<typeof loginSchema>
export type AppointmentData = z.infer<typeof appointmentSchema>
