#!/bin/bash

# Script pour migrer la base de données et initialiser les constantes
# Ce script doit être exécuté après avoir modifié le schéma Prisma

set -e

echo "🔄 Migration et initialisation de la base de données"
echo "=================================================="

# Générer le client Prisma
echo "🔧 Génération du client Prisma..."
npx prisma generate

# Appliquer les migrations
echo "🗄️ Application des migrations..."
npx prisma migrate dev --name "add-constants-tables"

# Initialiser les constantes
echo "🌱 Initialisation des constantes..."
npm run db:seed-constants

# Initialiser les données de test
echo "👥 Initialisation des données de test..."
npm run db:init

echo ""
echo "🎉 Migration et initialisation terminées avec succès !"
echo ""
echo "📋 Prochaines étapes :"
echo "  1. Démarrer l'application : npm run dev"
echo "  2. Tester les inscriptions : npm run test:inscriptions"
echo "  3. Accéder au dashboard : http://localhost:3000/dashboard"
echo ""
