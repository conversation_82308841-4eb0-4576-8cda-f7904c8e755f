// Script pour vérifier le contenu spécifique du dashboard
const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function checkDashboardContent() {
  console.log('🔍 Vérification du Contenu Dashboard')
  console.log('===================================')
  
  try {
    const response = await fetch(`${BASE_URL}/dashboard`)
    
    if (!response.ok) {
      console.log(`❌ Dashboard inaccessible: HTTP ${response.status}`)
      return false
    }
    
    const html = await response.text()
    
    // Vérifications détaillées du contenu
    const contentChecks = [
      { name: 'Titre Dashboard RH', test: html.includes('Dashboard RH') },
      { name: 'Logo Karma Com', test: html.includes('Karma Com') },
      { name: 'Mode Développement', test: html.includes('Mode Développement') },
      { name: 'Navigation tabs', test: html.includes('Vue d\'ensemble') || html.includes('overview') },
      { name: 'Statistiques', test: html.includes('Total Candidats') || html.includes('candidats') },
      { name: 'Candidatures récentes', test: html.includes('Candidatures récentes') || html.includes('récentes') },
      { name: 'Bouton déconnexion', test: html.includes('LogOut') || html.includes('déconnecter') },
      { name: 'Styles Karma', test: html.includes('karma-') },
      { name: 'Classes Tailwind', test: html.includes('bg-') && html.includes('text-') },
      { name: 'Responsive design', test: html.includes('md:') || html.includes('lg:') }
    ]
    
    console.log('📋 Vérifications du contenu:')
    let passedChecks = 0
    
    contentChecks.forEach(check => {
      if (check.test) {
        console.log(`   ✅ ${check.name}`)
        passedChecks++
      } else {
        console.log(`   ❌ ${check.name}`)
      }
    })
    
    const score = passedChecks / contentChecks.length
    console.log(`\n📊 Score contenu: ${passedChecks}/${contentChecks.length} (${Math.round(score * 100)}%)`)
    
    if (score >= 0.8) {
      console.log('✅ Contenu dashboard complet')
      return true
    } else {
      console.log('⚠️ Contenu dashboard incomplet')
      return false
    }
    
  } catch (error) {
    console.log('❌ Erreur vérification contenu:', error.message)
    return false
  }
}

async function checkDashboardStructure() {
  console.log('\n🏗️ Vérification de la Structure')
  console.log('===============================')
  
  try {
    const response = await fetch(`${BASE_URL}/dashboard`)
    const html = await response.text()
    
    // Vérifications de structure HTML
    const structureChecks = [
      { name: 'Header présent', test: html.includes('<header') },
      { name: 'Navigation présente', test: html.includes('<nav') || html.includes('navigation') },
      { name: 'Main content', test: html.includes('<main') || html.includes('main-content') },
      { name: 'Cards/sections', test: html.includes('karma-card') || html.includes('dashboard-card') },
      { name: 'Boutons interactifs', test: html.includes('<button') },
      { name: 'Liens de navigation', test: html.includes('<a') || html.includes('href') }
    ]
    
    console.log('🏗️ Structure HTML:')
    let structureScore = 0
    
    structureChecks.forEach(check => {
      if (check.test) {
        console.log(`   ✅ ${check.name}`)
        structureScore++
      } else {
        console.log(`   ❌ ${check.name}`)
      }
    })
    
    console.log(`\n📊 Score structure: ${structureScore}/${structureChecks.length}`)
    
    return structureScore >= structureChecks.length * 0.7
    
  } catch (error) {
    console.log('❌ Erreur vérification structure:', error.message)
    return false
  }
}

async function checkDashboardData() {
  console.log('\n📊 Vérification des Données')
  console.log('===========================')
  
  try {
    // Vérifier que l'API retourne des données valides
    const response = await fetch(`${BASE_URL}/api/dashboard/stats`)
    
    if (!response.ok) {
      console.log(`❌ API Stats erreur: HTTP ${response.status}`)
      return false
    }
    
    const data = await response.json()
    
    // Vérifications des données
    const dataChecks = [
      { name: 'Overview présent', test: data.overview !== undefined },
      { name: 'Total candidats', test: typeof data.overview?.totalCandidates === 'number' },
      { name: 'Applications en attente', test: typeof data.overview?.pendingApplications === 'number' },
      { name: 'Membres approuvés', test: typeof data.overview?.approvedMembers === 'number' },
      { name: 'Types d\'utilisateurs', test: data.userTypes !== undefined },
      { name: 'Statuts', test: data.statuses !== undefined },
      { name: 'Applications récentes', test: Array.isArray(data.recentApplications) }
    ]
    
    console.log('📊 Données API:')
    let dataScore = 0
    
    dataChecks.forEach(check => {
      if (check.test) {
        console.log(`   ✅ ${check.name}`)
        dataScore++
      } else {
        console.log(`   ❌ ${check.name}`)
      }
    })
    
    console.log(`\n📊 Score données: ${dataScore}/${dataChecks.length}`)
    
    // Afficher quelques statistiques
    if (data.overview) {
      console.log('\n📈 Statistiques actuelles:')
      console.log(`   Total candidats: ${data.overview.totalCandidates || 0}`)
      console.log(`   En attente: ${data.overview.pendingApplications || 0}`)
      console.log(`   Approuvés: ${data.overview.approvedMembers || 0}`)
      console.log(`   Applications récentes: ${data.recentApplications?.length || 0}`)
    }
    
    return dataScore >= dataChecks.length * 0.8
    
  } catch (error) {
    console.log('❌ Erreur vérification données:', error.message)
    return false
  }
}

async function main() {
  console.log('🔍 Vérification Complète du Dashboard')
  console.log('=====================================')
  
  // Vérifier que l'application est accessible
  try {
    const response = await fetch(`${BASE_URL}`)
    if (!response.ok) {
      throw new Error(`Application non accessible: HTTP ${response.status}`)
    }
    console.log('✅ Application accessible\n')
  } catch (error) {
    console.log('❌ Application non accessible:', error.message)
    console.log('💡 Assurez-vous que l\'application est démarrée avec "npm run dev"')
    process.exit(1)
  }
  
  // Exécuter les vérifications
  const contentOk = await checkDashboardContent()
  const structureOk = await checkDashboardStructure()
  const dataOk = await checkDashboardData()
  
  // Résumé final
  console.log('\n📋 Résumé de la Vérification')
  console.log('============================')
  
  const checks = [
    { name: 'Contenu Dashboard', passed: contentOk },
    { name: 'Structure HTML', passed: structureOk },
    { name: 'Données API', passed: dataOk }
  ]
  
  const passedCount = checks.filter(c => c.passed).length
  
  checks.forEach(check => {
    console.log(`${check.passed ? '✅' : '❌'} ${check.name}`)
  })
  
  console.log(`\n📊 Score global: ${passedCount}/${checks.length}`)
  
  if (passedCount === checks.length) {
    console.log('\n🎉 Dashboard complètement fonctionnel!')
    console.log('\n✅ Tout fonctionne correctement:')
    console.log('   • Contenu affiché correctement')
    console.log('   • Structure HTML valide')
    console.log('   • Données chargées depuis l\'API')
    console.log('   • Mode développement actif')
    console.log('\n🌐 Accédez au dashboard: http://localhost:3000/dashboard')
  } else {
    console.log('\n⚠️ Certaines vérifications ont échoué.')
    console.log('💡 Le dashboard fonctionne mais peut avoir des problèmes mineurs.')
    console.log('🌐 Vous pouvez quand même l\'utiliser: http://localhost:3000/dashboard')
  }
}

// Vérifier si node-fetch est disponible
try {
  require('node-fetch')
} catch (error) {
  console.log('❌ node-fetch n\'est pas installé')
  console.log('💡 Il devrait être installé avec les devDependencies')
  process.exit(1)
}

main()
