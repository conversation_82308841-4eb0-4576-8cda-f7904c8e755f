import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { AppointmentType, AppointmentStatus } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const interviews = await prisma.appointment.findMany({
      include: {
        candidate: {
          include: {
            profile: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        scheduledAt: 'asc'
      }
    })

    // Enrichir avec les données des bénévoles si nécessaire
    const formattedInterviews = await Promise.all(
      interviews.map(async (interview) => {
        let candidateInfo = {
          name: interview.candidate.name,
          email: interview.candidate.email,
          type: 'classic'
        }

        // Vérifier si c'est un bénévole en cherchant dans les logs d'activité
        const activityLog = await prisma.activityLog.findFirst({
          where: {
            action: 'INTERVIEW_SCHEDULED',
            metadata: {
              path: ['interviewId'],
              equals: interview.id
            }
          }
        })

        if (activityLog && activityLog.metadata && (activityLog.metadata as any).candidateType === 'volunteer') {
          candidateInfo.type = 'volunteer'
        }

        return {
          id: interview.id,
          title: interview.title,
          description: interview.description,
          candidateId: interview.candidateId,
          candidateName: candidateInfo.name,
          candidateEmail: candidateInfo.email,
          candidateType: candidateInfo.type,
          scheduledAt: interview.scheduledAt.toISOString(),
          type: interview.type,
          status: interview.status,
          notes: interview.notes,
          createdAt: interview.createdAt.toISOString(),
          createdBy: interview.createdBy
        }
      })
    )

    return NextResponse.json(formattedInterviews)
  } catch (error) {
    console.error('Erreur lors de la récupération des entretiens:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, description, candidateId, scheduledAt, type } = body

    console.log('📥 Données reçues pour création entretien:', body)

    // Validation des données
    if (!title || !candidateId || !scheduledAt || !type) {
      return NextResponse.json(
        { error: 'Données manquantes' },
        { status: 400 }
      )
    }

    // Vérifier que le candidat existe (système classique ou bénévole)
    let candidate = null
    let candidateType = 'classic'
    let candidateName = ''
    let candidateEmail = ''

    // D'abord, chercher dans les candidats classiques (User)
    candidate = await prisma.user.findUnique({
      where: { id: candidateId },
      include: { profile: true }
    })

    if (candidate) {
      candidateType = 'classic'
      candidateName = candidate.name
      candidateEmail = candidate.email
      console.log('✅ Candidat classique trouvé:', candidateName)
    } else {
      // Si pas trouvé, chercher dans les bénévoles (VolunteerApplication)
      const volunteerApplication = await prisma.volunteerApplication.findUnique({
        where: { id: candidateId }
      })

      if (volunteerApplication) {
        candidateType = 'volunteer'
        candidateName = `${volunteerApplication.firstName} ${volunteerApplication.lastName}`
        candidateEmail = volunteerApplication.email
        console.log('✅ Candidat bénévole trouvé:', candidateName)

        // Pour les bénévoles, nous devons créer un utilisateur temporaire
        // ou adapter le système pour gérer les deux types
        candidate = await prisma.user.create({
          data: {
            email: volunteerApplication.email,
            name: candidateName,
            password: 'temp_password', // Mot de passe temporaire
            userType: 'VOLUNTEER',
            profile: {
              create: {
                firstName: volunteerApplication.firstName,
                lastName: volunteerApplication.lastName,
                membershipStatus: 'INTERVIEW_SCHEDULED',
                phoneNumber: volunteerApplication.phoneNumber
              }
            }
          }
        })
        console.log('✅ Utilisateur temporaire créé pour bénévole:', candidate.id)
      }
    }

    if (!candidate) {
      console.log('❌ Candidat non trouvé avec ID:', candidateId)
      return NextResponse.json(
        { error: 'Candidat non trouvé' },
        { status: 404 }
      )
    }

    // Pour l'instant, on utilise un admin par défaut
    // En production, récupérer l'utilisateur connecté
    let adminUser = await prisma.user.findFirst({
      where: { userType: 'HR_ADMIN' }
    })

    // Créer un admin par défaut s'il n'existe pas
    if (!adminUser) {
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin Karma Com',
          password: 'admin123', // En production, utiliser un hash
          userType: 'HR_ADMIN',
          profile: {
            create: {
              firstName: 'Admin',
              lastName: 'Karma Com',
              membershipStatus: 'ACTIVE'
            }
          }
        }
      })
    }

    // Créer l'entretien
    const interview = await prisma.appointment.create({
      data: {
        title,
        description: description || '',
        candidateId: candidate.id, // Utiliser l'ID du candidat (classique ou temporaire pour bénévole)
        scheduledAt: new Date(scheduledAt),
        type: type as AppointmentType,
        status: AppointmentStatus.SCHEDULED,
        createdById: adminUser.id
      },
      include: {
        candidate: {
          include: {
            profile: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // Mettre à jour le statut du bénévole si c'est un candidat bénévole
    if (candidateType === 'volunteer') {
      await prisma.volunteerApplication.update({
        where: { id: candidateId }, // ID original du bénévole
        data: { status: 'INTERVIEW_SCHEDULED' }
      })
      console.log('✅ Statut bénévole mis à jour: INTERVIEW_SCHEDULED')
    }

    // Log de l'activité
    await prisma.activityLog.create({
      data: {
        action: 'INTERVIEW_SCHEDULED',
        description: `Entretien planifié pour ${candidateName} le ${new Date(scheduledAt).toLocaleDateString('fr-FR')}`,
        metadata: {
          interviewId: interview.id,
          originalCandidateId: candidateId, // ID original (User ou VolunteerApplication)
          finalCandidateId: candidate.id, // ID utilisé dans l'entretien (toujours User)
          candidateType,
          candidateName,
          interviewType: type,
          scheduledAt: scheduledAt
        }
      }
    })

    console.log('✅ Entretien créé avec succès:', interview.id)

    const formattedInterview = {
      id: interview.id,
      title: interview.title,
      description: interview.description,
      candidateId: interview.candidateId,
      candidateName: interview.candidate.name,
      candidateEmail: interview.candidate.email,
      scheduledAt: interview.scheduledAt.toISOString(),
      type: interview.type,
      status: interview.status,
      notes: interview.notes,
      createdAt: interview.createdAt.toISOString(),
      createdBy: interview.createdBy
    }

    return NextResponse.json(formattedInterview, { status: 201 })
  } catch (error) {
    console.error('Erreur lors de la création de l\'entretien:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
