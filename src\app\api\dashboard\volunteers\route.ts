import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // Construire les filtres
    const where: any = {}
    
    if (status) {
      where.status = status
    }
    
    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { contributionPole: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Récupérer les candidatures bénévoles
    const [volunteers, total] = await Promise.all([
      prisma.volunteerApplication.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.volunteerApplication.count({ where })
    ])

    // Formater les données pour le dashboard
    const formattedVolunteers = volunteers.map(volunteer => ({
      id: volunteer.id,
      name: `${volunteer.firstName} ${volunteer.lastName}`,
      firstName: volunteer.firstName,
      lastName: volunteer.lastName,
      email: volunteer.email,
      phoneNumber: volunteer.phoneNumber,
      type: 'Bénévole',
      organization: null,
      status: volunteer.status.toLowerCase(),
      date: volunteer.createdAt.toISOString().split('T')[0],
      userType: 'VOLUNTEER',
      membershipStatus: volunteer.status,
      createdAt: volunteer.createdAt,
      source: 'volunteer_application',
      
      // Informations spécifiques aux bénévoles
      currentStatus: volunteer.currentStatus,
      contributionPole: volunteer.contributionPole,
      specificSkills: volunteer.specificSkills,
      weeklyHours: volunteer.weeklyHours,
      availability: volunteer.availability,
      participationRhythm: volunteer.participationRhythm,
      associationExperience: volunteer.associationExperience,
      motivationReason: volunteer.motivationReason,
      objectives: volunteer.objectives,
      onlineProfile: volunteer.onlineProfile,
      howDidYouKnowUs: volunteer.howDidYouKnowUs,
      
      // Champs "Autre" si applicable
      otherStatus: volunteer.otherStatus,
      otherPole: volunteer.otherPole,
      otherSkills: volunteer.otherSkills,
      otherWeeklyHours: volunteer.otherWeeklyHours,
      otherAvailability: volunteer.otherAvailability,
      otherRhythm: volunteer.otherRhythm,
      otherSource: volunteer.otherSource,
      
      updatedAt: volunteer.updatedAt
    }))

    return NextResponse.json({
      volunteers: formattedVolunteers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Erreur lors de la récupération des bénévoles:', error)
    
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const { status } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: 'ID de candidature requis' },
        { status: 400 }
      )
    }

    if (!status) {
      return NextResponse.json(
        { error: 'Statut requis' },
        { status: 400 }
      )
    }

    // Valider le statut
    const validStatuses = ['PENDING', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'INTERVIEW_SCHEDULED']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Statut invalide' },
        { status: 400 }
      )
    }

    // Mettre à jour le statut
    const updatedVolunteer = await prisma.volunteerApplication.update({
      where: { id },
      data: { 
        status,
        updatedAt: new Date()
      }
    })

    // Log de l'activité
    await prisma.activityLog.create({
      data: {
        action: 'VOLUNTEER_STATUS_UPDATED',
        description: `Statut de candidature bénévole mis à jour: ${updatedVolunteer.firstName} ${updatedVolunteer.lastName} -> ${status}`,
        metadata: {
          volunteerId: id,
          oldStatus: updatedVolunteer.status,
          newStatus: status,
          email: updatedVolunteer.email
        }
      }
    })

    return NextResponse.json({
      success: true,
      volunteer: {
        id: updatedVolunteer.id,
        name: `${updatedVolunteer.firstName} ${updatedVolunteer.lastName}`,
        status: status.toLowerCase(),
        updatedAt: updatedVolunteer.updatedAt
      }
    })

  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut:', error)
    
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
