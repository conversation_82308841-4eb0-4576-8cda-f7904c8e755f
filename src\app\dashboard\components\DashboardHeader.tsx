import React from 'react'
import Link from 'next/link'
import { Bell, Settings } from 'lucide-react'
import Logo from '@/components/ui/Logo'

interface DashboardHeaderProps {
  title?: string
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ 
  title = "Dashboard Karma Com Solidarité" 
}) => {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Logo size="md" showText={true} />
            </Link>
            <div className="hidden md:block">
              <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors">
              <Bell size={20} />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors">
              <Settings size={20} />
            </button>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-karma-blue to-karma-pink rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">A</span>
              </div>
              <div className="hidden md:block">
                <p className="text-sm font-medium text-gray-900">Admin</p>
                <p className="text-xs text-gray-500">Karma Com Solidarité</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default DashboardHeader
