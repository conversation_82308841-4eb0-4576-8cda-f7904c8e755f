import React from 'react'
import Link from 'next/link'
import Logo from '@/components/ui/Logo'
import { Mail, Phone, MapPin, Facebook, Twitter, Linkedin } from 'lucide-react'

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container-karma py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Logo et description */}
          <div className="lg:col-span-2">
            <div className="mb-4">
              <Logo size="md" showText={true} className="text-white" />
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              Karma Com Solidarité soutient ses associations partenaires dans la réalisation 
              de leurs projets. Ensemble, nous construisons un réseau solidaire et engagé.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors duration-200"
                aria-label="Facebook"
              >
                <Facebook size={20} />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors duration-200"
                aria-label="Twitter"
              >
                <Twitter size={20} />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors duration-200"
                aria-label="LinkedIn"
              >
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          {/* Liens rapides */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Liens rapides</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/inscription/association"
                  className="text-gray-300 hover:text-primary-400 transition-colors duration-200"
                >
                  Inscription Association
                </Link>
              </li>
              <li>
                <Link
                  href="/inscription/organisation"
                  className="text-gray-300 hover:text-primary-400 transition-colors duration-200"
                >
                  Inscription Organisation
                </Link>
              </li>
              <li>
                <Link
                  href="/inscription/benevole"
                  className="text-gray-300 hover:text-primary-400 transition-colors duration-200"
                >
                  Devenir Bénévole
                </Link>
              </li>
              <li>
                <Link
                  href="/dashboard"
                  className="text-gray-300 hover:text-primary-400 transition-colors duration-200"
                >
                  Dashboard RH
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-center space-x-3">
                <Mail size={16} className="text-primary-400" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-300 hover:text-primary-400 transition-colors duration-200"
                >
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center space-x-3">
                <Phone size={16} className="text-primary-400" />
                <a
                  href="tel:+33123456789"
                  className="text-gray-300 hover:text-primary-400 transition-colors duration-200"
                >
                  +33 1 23 45 67 89
                </a>
              </li>
              <li className="flex items-start space-x-3">
                <MapPin size={16} className="text-primary-400 mt-1" />
                <span className="text-gray-300">
                  123 Rue de la Solidarité<br />
                  75001 Paris, France
                </span>
              </li>
            </ul>
          </div>
        </div>

        {/* Séparateur */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} Karma Com Solidarité. Tous droits réservés.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link
                href="/mentions-legales"
                className="text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200"
              >
                Mentions légales
              </Link>
              <Link
                href="/politique-confidentialite"
                className="text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200"
              >
                Politique de confidentialité
              </Link>
              <Link
                href="/rgpd"
                className="text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200"
              >
                RGPD
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
