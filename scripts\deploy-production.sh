#!/bin/bash

# Script de déploiement pour l'environnement production
# Usage: ./deploy-production.sh

set -e

echo "🚀 Déploiement Production - Karma Com Solidarité"
echo "==============================================="

# Configuration
DEPLOY_DIR="/opt/karma-com-production"
BACKUP_DIR="/opt/backups/production"
LOG_FILE="/var/log/karma-com-production-deploy.log"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

# Confirmation de déploiement en production
echo "⚠️  ATTENTION: Déploiement en PRODUCTION"
echo "Êtes-vous sûr de vouloir continuer? (oui/non)"
read -r confirmation

if [ "$confirmation" != "oui" ]; then
    log "Déploiement annulé par l'utilisateur"
    exit 0
fi

# Vérifications préliminaires
log "Vérification des prérequis..."

if [ ! -d "$DEPLOY_DIR" ]; then
    error "Répertoire de déploiement $DEPLOY_DIR non trouvé"
fi

if ! command -v docker &> /dev/null; then
    error "Docker n'est pas installé"
fi

if ! command -v docker-compose &> /dev/null; then
    error "Docker Compose n'est pas installé"
fi

success "Prérequis validés"

# Sauvegarde critique de la base de données
log "Sauvegarde critique de la base de données production..."
mkdir -p "$BACKUP_DIR"

if docker ps | grep -q "karma-com-postgres-prod"; then
    BACKUP_FILE="$BACKUP_DIR/production_backup_$(date +%Y%m%d_%H%M%S).sql"
    docker exec karma-com-postgres-prod pg_dump -U karma_user karma_com_db > "$BACKUP_FILE" || error "Échec critique de la sauvegarde"
    
    # Vérifier que la sauvegarde n'est pas vide
    if [ ! -s "$BACKUP_FILE" ]; then
        error "Sauvegarde vide, arrêt du déploiement"
    fi
    
    success "Sauvegarde critique créée: $BACKUP_FILE"
    
    # Copier la sauvegarde vers un emplacement sécurisé
    cp "$BACKUP_FILE" "/opt/backups/critical/production_pre_deploy_$(date +%Y%m%d_%H%M%S).sql"
else
    error "Base de données production non trouvée, déploiement impossible"
fi

# Test de l'application actuelle
log "Test de l'application actuelle..."
if ! curl -f http://localhost:3000 > /dev/null 2>&1; then
    warning "Application actuelle non accessible, déploiement risqué"
    echo "Continuer malgré tout? (oui/non)"
    read -r force_deploy
    if [ "$force_deploy" != "oui" ]; then
        error "Déploiement annulé - application actuelle non accessible"
    fi
fi

# Mise à jour du code
log "Mise à jour du code source..."
cd "$DEPLOY_DIR"

# Sauvegarder les modifications locales
git stash push -m "Auto-stash before production deployment $(date)"

# Récupérer les dernières modifications
git fetch origin
git checkout main
git pull origin main

# Vérifier qu'on est sur un tag ou commit stable
CURRENT_COMMIT=$(git rev-parse HEAD)
log "Déploiement du commit: $CURRENT_COMMIT"

success "Code source mis à jour"

# Vérification des variables d'environnement critiques
log "Vérification des variables d'environnement production..."

if [ -z "$POSTGRES_PASSWORD_PROD" ]; then
    error "POSTGRES_PASSWORD_PROD non défini"
fi

if [ -z "$NEXTAUTH_SECRET_PROD" ]; then
    error "NEXTAUTH_SECRET_PROD non défini"
fi

if [ -z "$REGISTRY_IMAGE" ]; then
    warning "REGISTRY_IMAGE non défini, utilisation de l'image locale"
    export REGISTRY_IMAGE="karma-com-app"
fi

if [ -z "$IMAGE_TAG" ]; then
    warning "IMAGE_TAG non défini, utilisation de 'latest'"
    export IMAGE_TAG="latest"
fi

success "Variables d'environnement validées"

# Récupération de la nouvelle image
log "Récupération de l'image Docker production..."
docker-compose -f docker-compose.production.yml pull app || error "Échec du pull de l'image"

# Test de l'image avant déploiement
log "Test de la nouvelle image..."
docker run --rm -d --name karma-test-prod -p 3001:3000 \
    -e NODE_ENV=production \
    -e DATABASE_URL="***********************************/dummy" \
    "$REGISTRY_IMAGE:$IMAGE_TAG" || error "Échec du test de l'image"

sleep 10

if curl -f http://localhost:3001 > /dev/null 2>&1; then
    success "Nouvelle image testée avec succès"
    docker stop karma-test-prod
else
    docker stop karma-test-prod || true
    error "Nouvelle image défaillante, arrêt du déploiement"
fi

# Déploiement avec zero-downtime
log "Déploiement zero-downtime en cours..."

# Démarrer le nouveau conteneur en parallèle
docker-compose -f docker-compose.production.yml up -d --no-deps --scale app=2 app

# Attendre que le nouveau conteneur soit prêt
log "Attente du nouveau conteneur..."
sleep 60

# Vérifier que le nouveau conteneur répond
NEW_CONTAINER=$(docker-compose -f docker-compose.production.yml ps -q app | head -1)
if docker exec "$NEW_CONTAINER" curl -f http://localhost:3000 > /dev/null 2>&1; then
    success "Nouveau conteneur opérationnel"
    
    # Arrêter l'ancien conteneur
    docker-compose -f docker-compose.production.yml up -d --no-deps --scale app=1 app
    
    success "Basculement terminé"
else
    error "Nouveau conteneur défaillant, rollback nécessaire"
fi

# Vérification post-déploiement
log "Vérification post-déploiement..."

# Vérifier PostgreSQL
if docker-compose -f docker-compose.production.yml exec -T postgres pg_isready -U karma_user; then
    success "PostgreSQL opérationnel"
else
    error "PostgreSQL non accessible"
fi

# Vérifier l'application
sleep 30
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    success "Application accessible"
else
    error "Application non accessible après déploiement"
fi

# Test de l'URL publique
if [ ! -z "$PRODUCTION_URL" ]; then
    log "Test de l'URL publique: $PRODUCTION_URL"
    if curl -f "$PRODUCTION_URL" > /dev/null 2>&1; then
        success "URL publique accessible"
    else
        warning "URL publique non accessible (vérifier la configuration Nginx/DNS)"
    fi
fi

# Nettoyage des anciennes images
log "Nettoyage des anciennes images..."
docker image prune -f || warning "Échec du nettoyage"

# Garder seulement les 3 dernières sauvegardes
log "Nettoyage des anciennes sauvegardes..."
find "$BACKUP_DIR" -name "*.sql" -type f -mtime +7 -delete || warning "Échec du nettoyage des sauvegardes"

# Affichage des logs récents
log "Logs récents de l'application:"
docker-compose -f docker-compose.production.yml logs --tail=10 app

# Affichage de l'état final
log "État final des services:"
docker-compose -f docker-compose.production.yml ps

# Test de santé final
log "Test de santé final..."
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    success "API de santé accessible"
else
    warning "API de santé non accessible (normal si pas implémentée)"
fi

# Résumé du déploiement
echo ""
echo "📊 Résumé du Déploiement Production"
echo "==================================="
success "Déploiement production terminé avec succès"
echo "🌐 Application: https://karma-com-solidarite.fr"
echo "🗄️ pgAdmin: http://localhost:5050 (accès local uniquement)"
echo "📋 Logs: docker-compose -f docker-compose.production.yml logs -f"
echo "🔄 Redémarrer: docker-compose -f docker-compose.production.yml restart"
echo "📊 Monitoring: docker-compose -f docker-compose.production.yml ps"

# Notification de succès (optionnel - webhook, email, etc.)
log "Envoi de notification de déploiement réussi..."
# curl -X POST "https://hooks.slack.com/..." -d "Déploiement production réussi"

# Log final
log "Déploiement production terminé avec succès - Commit: $CURRENT_COMMIT"

echo ""
echo "🎉 Déploiement production réussi!"
echo "⚠️  N'oubliez pas de surveiller les logs et métriques"
