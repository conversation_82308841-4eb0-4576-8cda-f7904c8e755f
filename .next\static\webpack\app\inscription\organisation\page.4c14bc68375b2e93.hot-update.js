"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inscription/organisation/page",{

/***/ "(app-pages-browser)/./src/app/inscription/organisation/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/inscription/organisation/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrganisationInscriptionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Building2,FileText,MapPin,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction OrganisationInscriptionPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Informations de contact\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        // Informations personnelles du représentant\n        firstName: \"\",\n        lastName: \"\",\n        phone: \"\",\n        // Informations de l'organisation\n        organizationName: \"\",\n        siret: \"\",\n        website: \"\",\n        description: \"\",\n        organizationType: \"\",\n        sector: \"\",\n        employeeCount: \"\",\n        // Adresse\n        address: \"\",\n        city: \"\",\n        postalCode: \"\",\n        country: \"France\",\n        // Partenariat\n        partnershipType: \"\",\n        motivation: \"\",\n        budget: \"\",\n        // Conditions\n        acceptTerms: false,\n        acceptRGPD: false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const organizationTypes = [\n        \"Entreprise priv\\xe9e\",\n        \"Institution publique\",\n        \"Collectivit\\xe9 territoriale\",\n        \"\\xc9tablissement public\",\n        \"Fondation\",\n        \"Autre\"\n    ];\n    const sectors = [\n        \"Technologie\",\n        \"Finance\",\n        \"Sant\\xe9\",\n        \"\\xc9ducation\",\n        \"Commerce\",\n        \"Industrie\",\n        \"Services\",\n        \"Administration publique\",\n        \"Autre\"\n    ];\n    const partnershipTypes = [\n        \"M\\xe9c\\xe9nat financier\",\n        \"M\\xe9c\\xe9nat de comp\\xe9tences\",\n        \"Partenariat strat\\xe9gique\",\n        \"Sponsoring \\xe9v\\xe9nementiel\",\n        \"Collaboration projet\",\n        \"Autre\"\n    ];\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === \"checkbox\" ? e.target.checked : value\n            }));\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Validation email\n        if (!formData.email) {\n            newErrors.email = \"L'email est requis\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Format d'email invalide\";\n        }\n        // Validation mot de passe\n        if (!formData.password) {\n            newErrors.password = \"Le mot de passe est requis\";\n        } else if (formData.password.length < 8) {\n            newErrors.password = \"Le mot de passe doit contenir au moins 8 caract\\xe8res\";\n        }\n        if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = \"Les mots de passe ne correspondent pas\";\n        }\n        // Validation champs requis\n        const requiredFields = [\n            \"firstName\",\n            \"lastName\",\n            \"organizationName\",\n            \"organizationType\",\n            \"description\"\n        ];\n        requiredFields.forEach((field)=>{\n            if (!formData[field]) {\n                newErrors[field] = \"Ce champ est requis\";\n            }\n        });\n        // Validation SIRET\n        if (formData.siret && !/^\\d{14}$/.test(formData.siret)) {\n            newErrors.siret = \"Le SIRET doit contenir 14 chiffres\";\n        }\n        // Validation acceptation des conditions\n        if (!formData.acceptTerms) {\n            newErrors.acceptTerms = \"Vous devez accepter les conditions d'utilisation\";\n        }\n        if (!formData.acceptRGPD) {\n            newErrors.acceptRGPD = \"Vous devez accepter la politique de confidentialit\\xe9\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"/api/auth/register/organization\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (response.ok) {\n                alert(\"Inscription r\\xe9ussie ! Vous recevrez un email de confirmation.\");\n                // Réinitialiser le formulaire\n                setFormData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    firstName: \"\",\n                    lastName: \"\",\n                    phone: \"\",\n                    organizationName: \"\",\n                    siret: \"\",\n                    website: \"\",\n                    description: \"\",\n                    organizationType: \"\",\n                    sector: \"\",\n                    employeeCount: \"\",\n                    address: \"\",\n                    city: \"\",\n                    postalCode: \"\",\n                    country: \"France\",\n                    partnershipType: \"\",\n                    motivation: \"\",\n                    budget: \"\",\n                    acceptTerms: false,\n                    acceptRGPD: false\n                });\n            } else {\n                // Afficher les erreurs de validation\n                if (data.details) {\n                    const newErrors = {};\n                    data.details.forEach((error)=>{\n                        if (error.path && error.path.length > 0) {\n                            newErrors[error.path[0]] = error.message;\n                        }\n                    });\n                    setErrors(newErrors);\n                } else {\n                    alert(data.error || \"Une erreur est survenue lors de l'inscription.\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Erreur lors de l'inscription:\", error);\n            alert(\"Une erreur est survenue. Veuillez r\\xe9essayer.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-karma py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"text-white\",\n                                        size: 32\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Inscription Organisation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Entreprises et institutions, rejoignez notre r\\xe9seau de partenaires engag\\xe9s\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"mr-3 text-secondary-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Informations de connexion\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Email *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            name: \"email\",\n                                                            value: formData.email,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.email ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 36\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"T\\xe9l\\xe9phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            name: \"phone\",\n                                                            value: formData.phone,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"+33 1 23 45 67 89\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Mot de passe *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            name: \"password\",\n                                                            value: formData.password,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.password ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"Minimum 8 caract\\xe8res\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.password\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 39\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Confirmer le mot de passe *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            name: \"confirmPassword\",\n                                                            value: formData.confirmPassword,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.confirmPassword ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"R\\xe9p\\xe9tez le mot de passe\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.confirmPassword\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 46\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"mr-3 text-secondary-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Repr\\xe9sentant de l'organisation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Pr\\xe9nom *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"firstName\",\n                                                            value: formData.firstName,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.firstName ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"Votre pr\\xe9nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.firstName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 40\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Nom *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"lastName\",\n                                                            value: formData.lastName,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.lastName ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"Votre nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.lastName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 39\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"mr-3 text-secondary-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Informations de l'organisation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Nom de l'organisation *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"organizationName\",\n                                                            value: formData.organizationName,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input \".concat(errors.organizationName ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"Nom officiel de votre organisation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.organizationName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.organizationName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 47\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Type d'organisation *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    name: \"organizationType\",\n                                                                    value: formData.organizationType,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input \".concat(errors.organizationType ? \"border-red-500\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"S\\xe9lectionnez un type\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        organizationTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: type,\n                                                                                children: type\n                                                                            }, type, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                errors.organizationType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-500 text-sm mt-1\",\n                                                                    children: errors.organizationType\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Secteur d'activit\\xe9\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    name: \"sector\",\n                                                                    value: formData.sector,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"S\\xe9lectionnez un secteur\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        sectors.map((sector)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: sector,\n                                                                                children: sector\n                                                                            }, sector, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                                lineNumber: 367,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"SIRET\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"siret\",\n                                                                    value: formData.siret,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input \".concat(errors.siret ? \"border-red-500\" : \"\"),\n                                                                    placeholder: \"14 chiffres\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                errors.siret && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-500 text-sm mt-1\",\n                                                                    children: errors.siret\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 38\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Site web\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"url\",\n                                                                    name: \"website\",\n                                                                    value: formData.website,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    placeholder: \"https://votre-site.com\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Nombre d'employ\\xe9s\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    name: \"employeeCount\",\n                                                                    value: formData.employeeCount,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"S\\xe9lectionnez\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"1-10\",\n                                                                            children: \"1-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 406,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"11-50\",\n                                                                            children: \"11-50\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"51-200\",\n                                                                            children: \"51-200\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 408,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"201-500\",\n                                                                            children: \"201-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"500+\",\n                                                                            children: \"500+\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Description de l'organisation *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"description\",\n                                                            value: formData.description,\n                                                            onChange: handleInputChange,\n                                                            rows: 4,\n                                                            className: \"karma-input \".concat(errors.description ? \"border-red-500\" : \"\"),\n                                                            placeholder: \"D\\xe9crivez les activit\\xe9s et missions de votre organisation...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 42\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"mr-3 text-secondary-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Adresse\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Adresse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"address\",\n                                                            value: formData.address,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"Num\\xe9ro et nom de rue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Ville\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"city\",\n                                                                    value: formData.city,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    placeholder: \"Ville\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Code postal\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"postalCode\",\n                                                                    value: formData.postalCode,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    placeholder: \"75001\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Pays\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    name: \"country\",\n                                                                    value: formData.country,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"France\",\n                                                                            children: \"France\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Belgique\",\n                                                                            children: \"Belgique\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Suisse\",\n                                                                            children: \"Suisse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Canada\",\n                                                                            children: \"Canada\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Autre\",\n                                                                            children: \"Autre\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Building2_FileText_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-3 text-secondary-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Partenariat souhait\\xe9\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Type de partenariat\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"partnershipType\",\n                                                            value: formData.partnershipType,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"S\\xe9lectionnez un type\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                partnershipTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: type,\n                                                                        children: type\n                                                                    }, type, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                        lineNumber: 507,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Budget envisag\\xe9 (optionnel)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"budget\",\n                                                            value: formData.budget,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"S\\xe9lectionnez une fourchette\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"< 5 000€\",\n                                                                    children: \"Moins de 5 000€\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"5 000€ - 15 000€\",\n                                                                    children: \"5 000€ - 15 000€\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"15 000€ - 50 000€\",\n                                                                    children: \"15 000€ - 50 000€\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"50 000€ - 100 000€\",\n                                                                    children: \"50 000€ - 100 000€\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"> 100 000€\",\n                                                                    children: \"Plus de 100 000€\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Motivation et objectifs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"motivation\",\n                                                            value: formData.motivation,\n                                                            onChange: handleInputChange,\n                                                            rows: 4,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"Expliquez vos motivations et objectifs pour ce partenariat...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        name: \"acceptTerms\",\n                                                        checked: formData.acceptTerms,\n                                                        onChange: handleInputChange,\n                                                        className: \"mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: [\n                                                            \"J'accepte les\",\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/conditions\",\n                                                                className: \"text-secondary-600 hover:underline\",\n                                                                children: \"conditions d'utilisation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"de Karma Com Solidarit\\xe9 *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.acceptTerms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: errors.acceptTerms\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 40\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        name: \"acceptRGPD\",\n                                                        checked: formData.acceptRGPD,\n                                                        onChange: handleInputChange,\n                                                        className: \"mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: [\n                                                            \"J'accepte la\",\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/politique-confidentialite\",\n                                                                className: \"text-secondary-600 hover:underline\",\n                                                                children: \"politique de confidentialit\\xe9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"et le traitement de mes donn\\xe9es personnelles selon le RGPD *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.acceptRGPD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: errors.acceptRGPD\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 39\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"bg-gradient-to-r from-secondary-600 to-secondary-700 text-white font-medium py-3 px-12 rounded-lg hover:from-secondary-700 hover:to-secondary-800 transition-all duration-200 transform hover:scale-105 text-lg \".concat(isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                                        children: isSubmitting ? \"Inscription en cours...\" : \"Soumettre ma candidature\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\organisation\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(OrganisationInscriptionPage, \"MPtX2sRBfl2f/dMue8zmEWCt2lE=\");\n_c = OrganisationInscriptionPage;\nvar _c;\n$RefreshReg$(_c, \"OrganisationInscriptionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvaW5zY3JpcHRpb24vb3JnYW5pc2F0aW9uL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFdUM7QUFDUTtBQUNBO0FBQytDO0FBRS9FLFNBQVNTOztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR1YsK0NBQVFBLENBQUM7UUFDdkMsMEJBQTBCO1FBQzFCVyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsaUJBQWlCO1FBRWpCLDRDQUE0QztRQUM1Q0MsV0FBVztRQUNYQyxVQUFVO1FBQ1ZDLE9BQU87UUFFUCxpQ0FBaUM7UUFDakNDLGtCQUFrQjtRQUNsQkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLGFBQWE7UUFDYkMsa0JBQWtCO1FBQ2xCQyxRQUFRO1FBQ1JDLGVBQWU7UUFFZixVQUFVO1FBQ1ZDLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxZQUFZO1FBQ1pDLFNBQVM7UUFFVCxjQUFjO1FBQ2RDLGlCQUFpQjtRQUNqQkMsWUFBWTtRQUNaQyxRQUFRO1FBRVIsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7SUFDZDtJQUVBLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHbEMsK0NBQVFBLENBQXlCLENBQUM7SUFDOUQsTUFBTSxDQUFDbUMsY0FBY0MsZ0JBQWdCLEdBQUdwQywrQ0FBUUEsQ0FBQztJQUVqRCxNQUFNcUMsb0JBQW9CO1FBQ3hCO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRUQsTUFBTUMsVUFBVTtRQUNkO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRUQsTUFBTUMsbUJBQW1CO1FBQ3ZCO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRUQsTUFBTUMsb0JBQW9CLENBQUNDO1FBQ3pCLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUVDLElBQUksRUFBRSxHQUFHSCxFQUFFSSxNQUFNO1FBQ3RDbkMsWUFBWW9DLENBQUFBLE9BQVM7Z0JBQ25CLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0osS0FBSyxFQUFFRSxTQUFTLGFBQWEsRUFBR0MsTUFBTSxDQUFzQkUsT0FBTyxHQUFHSjtZQUN6RTtRQUVBLElBQUlWLE1BQU0sQ0FBQ1MsS0FBSyxFQUFFO1lBQ2hCUixVQUFVWSxDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUUsQ0FBQ0osS0FBSyxFQUFFO2dCQUFHO1FBQzNDO0lBQ0Y7SUFFQSxNQUFNTSxlQUFlO1FBQ25CLE1BQU1DLFlBQW9DLENBQUM7UUFFM0MsbUJBQW1CO1FBQ25CLElBQUksQ0FBQ3hDLFNBQVNFLEtBQUssRUFBRTtZQUNuQnNDLFVBQVV0QyxLQUFLLEdBQUc7UUFDcEIsT0FBTyxJQUFJLENBQUMsZUFBZXVDLElBQUksQ0FBQ3pDLFNBQVNFLEtBQUssR0FBRztZQUMvQ3NDLFVBQVV0QyxLQUFLLEdBQUc7UUFDcEI7UUFFQSwwQkFBMEI7UUFDMUIsSUFBSSxDQUFDRixTQUFTRyxRQUFRLEVBQUU7WUFDdEJxQyxVQUFVckMsUUFBUSxHQUFHO1FBQ3ZCLE9BQU8sSUFBSUgsU0FBU0csUUFBUSxDQUFDdUMsTUFBTSxHQUFHLEdBQUc7WUFDdkNGLFVBQVVyQyxRQUFRLEdBQUc7UUFDdkI7UUFFQSxJQUFJSCxTQUFTRyxRQUFRLEtBQUtILFNBQVNJLGVBQWUsRUFBRTtZQUNsRG9DLFVBQVVwQyxlQUFlLEdBQUc7UUFDOUI7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTXVDLGlCQUFpQjtZQUFDO1lBQWE7WUFBWTtZQUFvQjtZQUFvQjtTQUFjO1FBQ3ZHQSxlQUFlQyxPQUFPLENBQUNDLENBQUFBO1lBQ3JCLElBQUksQ0FBQzdDLFFBQVEsQ0FBQzZDLE1BQStCLEVBQUU7Z0JBQzdDTCxTQUFTLENBQUNLLE1BQU0sR0FBRztZQUNyQjtRQUNGO1FBRUEsbUJBQW1CO1FBQ25CLElBQUk3QyxTQUFTUyxLQUFLLElBQUksQ0FBQyxXQUFXZ0MsSUFBSSxDQUFDekMsU0FBU1MsS0FBSyxHQUFHO1lBQ3REK0IsVUFBVS9CLEtBQUssR0FBRztRQUNwQjtRQUVBLHdDQUF3QztRQUN4QyxJQUFJLENBQUNULFNBQVNzQixXQUFXLEVBQUU7WUFDekJrQixVQUFVbEIsV0FBVyxHQUFHO1FBQzFCO1FBRUEsSUFBSSxDQUFDdEIsU0FBU3VCLFVBQVUsRUFBRTtZQUN4QmlCLFVBQVVqQixVQUFVLEdBQUc7UUFDekI7UUFFQUUsVUFBVWU7UUFDVixPQUFPTSxPQUFPQyxJQUFJLENBQUNQLFdBQVdFLE1BQU0sS0FBSztJQUMzQztJQUVBLE1BQU1NLGVBQWUsT0FBT2hCO1FBQzFCQSxFQUFFaUIsY0FBYztRQUVoQixJQUFJLENBQUNWLGdCQUFnQjtZQUNuQjtRQUNGO1FBRUFaLGdCQUFnQjtRQUVoQixJQUFJO1lBQ0YsTUFBTXVCLFdBQVcsTUFBTUMsTUFBTSxtQ0FBbUM7Z0JBQzlEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ3hEO1lBQ3ZCO1lBRUEsTUFBTXlELE9BQU8sTUFBTVAsU0FBU1EsSUFBSTtZQUVoQyxJQUFJUixTQUFTUyxFQUFFLEVBQUU7Z0JBQ2ZDLE1BQU07Z0JBQ04sOEJBQThCO2dCQUM5QjNELFlBQVk7b0JBQ1ZDLE9BQU87b0JBQ1BDLFVBQVU7b0JBQ1ZDLGlCQUFpQjtvQkFDakJDLFdBQVc7b0JBQ1hDLFVBQVU7b0JBQ1ZDLE9BQU87b0JBQ1BDLGtCQUFrQjtvQkFDbEJDLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLGFBQWE7b0JBQ2JDLGtCQUFrQjtvQkFDbEJDLFFBQVE7b0JBQ1JDLGVBQWU7b0JBQ2ZDLFNBQVM7b0JBQ1RDLE1BQU07b0JBQ05DLFlBQVk7b0JBQ1pDLFNBQVM7b0JBQ1RDLGlCQUFpQjtvQkFDakJDLFlBQVk7b0JBQ1pDLFFBQVE7b0JBQ1JDLGFBQWE7b0JBQ2JDLFlBQVk7Z0JBQ2Q7WUFDRixPQUFPO2dCQUNMLHFDQUFxQztnQkFDckMsSUFBSWtDLEtBQUtJLE9BQU8sRUFBRTtvQkFDaEIsTUFBTXJCLFlBQW9DLENBQUM7b0JBQzNDaUIsS0FBS0ksT0FBTyxDQUFDakIsT0FBTyxDQUFDLENBQUNrQjt3QkFDcEIsSUFBSUEsTUFBTUMsSUFBSSxJQUFJRCxNQUFNQyxJQUFJLENBQUNyQixNQUFNLEdBQUcsR0FBRzs0QkFDdkNGLFNBQVMsQ0FBQ3NCLE1BQU1DLElBQUksQ0FBQyxFQUFFLENBQUMsR0FBR0QsTUFBTUUsT0FBTzt3QkFDMUM7b0JBQ0Y7b0JBQ0F2QyxVQUFVZTtnQkFDWixPQUFPO29CQUNMb0IsTUFBTUgsS0FBS0ssS0FBSyxJQUFJO2dCQUN0QjtZQUNGO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RHLFFBQVFILEtBQUssQ0FBQyxpQ0FBa0NBO1lBQ2hERixNQUFNO1FBQ1IsU0FBVTtZQUNSakMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3VDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDM0UsaUVBQU1BOzs7OzswQkFFUCw4REFBQzBFO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUN6RSxtSEFBU0E7d0NBQUN5RSxXQUFVO3dDQUFhQyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7OzBDQUc1Qyw4REFBQ0M7Z0NBQUdGLFdBQVU7MENBQW9EOzs7Ozs7MENBR2xFLDhEQUFDRztnQ0FBRUgsV0FBVTswQ0FBMEM7Ozs7Ozs7Ozs7OztrQ0FNekQsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDSTs0QkFBS0MsVUFBVXhCOzRCQUFjbUIsV0FBVTs7OENBR3RDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNNOzRDQUFHTixXQUFVOzs4REFDWiw4REFBQ3JFLG1IQUFJQTtvREFBQ3FFLFdBQVU7b0RBQTBCQyxNQUFNOzs7Ozs7Z0RBQU07Ozs7Ozs7c0RBR3hELDhEQUFDRjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQ1E7NERBQU1QLFdBQVU7c0VBQWM7Ozs7OztzRUFDL0IsOERBQUNROzREQUNDeEMsTUFBSzs0REFDTEYsTUFBSzs0REFDTEMsT0FBT2xDLFNBQVNFLEtBQUs7NERBQ3JCMEUsVUFBVTdDOzREQUNWb0MsV0FBVyxlQUFvRCxPQUFyQzNDLE9BQU90QixLQUFLLEdBQUcsbUJBQW1COzREQUM1RDJFLGFBQVk7Ozs7Ozt3REFFYnJELE9BQU90QixLQUFLLGtCQUFJLDhEQUFDb0U7NERBQUVILFdBQVU7c0VBQTZCM0MsT0FBT3RCLEtBQUs7Ozs7Ozs7Ozs7Ozs4REFFekUsOERBQUNnRTs7c0VBQ0MsOERBQUNROzREQUFNUCxXQUFVO3NFQUFjOzs7Ozs7c0VBQy9CLDhEQUFDUTs0REFDQ3hDLE1BQUs7NERBQ0xGLE1BQUs7NERBQ0xDLE9BQU9sQyxTQUFTTyxLQUFLOzREQUNyQnFFLFVBQVU3Qzs0REFDVm9DLFdBQVU7NERBQ1ZVLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFHaEIsOERBQUNYOztzRUFDQyw4REFBQ1E7NERBQU1QLFdBQVU7c0VBQWM7Ozs7OztzRUFDL0IsOERBQUNROzREQUNDeEMsTUFBSzs0REFDTEYsTUFBSzs0REFDTEMsT0FBT2xDLFNBQVNHLFFBQVE7NERBQ3hCeUUsVUFBVTdDOzREQUNWb0MsV0FBVyxlQUF1RCxPQUF4QzNDLE9BQU9yQixRQUFRLEdBQUcsbUJBQW1COzREQUMvRDBFLGFBQVk7Ozs7Ozt3REFFYnJELE9BQU9yQixRQUFRLGtCQUFJLDhEQUFDbUU7NERBQUVILFdBQVU7c0VBQTZCM0MsT0FBT3JCLFFBQVE7Ozs7Ozs7Ozs7Ozs4REFFL0UsOERBQUMrRDs7c0VBQ0MsOERBQUNROzREQUFNUCxXQUFVO3NFQUFjOzs7Ozs7c0VBQy9CLDhEQUFDUTs0REFDQ3hDLE1BQUs7NERBQ0xGLE1BQUs7NERBQ0xDLE9BQU9sQyxTQUFTSSxlQUFlOzREQUMvQndFLFVBQVU3Qzs0REFDVm9DLFdBQVcsZUFBOEQsT0FBL0MzQyxPQUFPcEIsZUFBZSxHQUFHLG1CQUFtQjs0REFDdEV5RSxhQUFZOzs7Ozs7d0RBRWJyRCxPQUFPcEIsZUFBZSxrQkFBSSw4REFBQ2tFOzREQUFFSCxXQUFVO3NFQUE2QjNDLE9BQU9wQixlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTWpHLDhEQUFDOEQ7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBR04sV0FBVTs7OERBQ1osOERBQUNyRSxtSEFBSUE7b0RBQUNxRSxXQUFVO29EQUEwQkMsTUFBTTs7Ozs7O2dEQUFNOzs7Ozs7O3NEQUd4RCw4REFBQ0Y7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUNROzREQUFNUCxXQUFVO3NFQUFjOzs7Ozs7c0VBQy9CLDhEQUFDUTs0REFDQ3hDLE1BQUs7NERBQ0xGLE1BQUs7NERBQ0xDLE9BQU9sQyxTQUFTSyxTQUFTOzREQUN6QnVFLFVBQVU3Qzs0REFDVm9DLFdBQVcsZUFBd0QsT0FBekMzQyxPQUFPbkIsU0FBUyxHQUFHLG1CQUFtQjs0REFDaEV3RSxhQUFZOzs7Ozs7d0RBRWJyRCxPQUFPbkIsU0FBUyxrQkFBSSw4REFBQ2lFOzREQUFFSCxXQUFVO3NFQUE2QjNDLE9BQU9uQixTQUFTOzs7Ozs7Ozs7Ozs7OERBRWpGLDhEQUFDNkQ7O3NFQUNDLDhEQUFDUTs0REFBTVAsV0FBVTtzRUFBYzs7Ozs7O3NFQUMvQiw4REFBQ1E7NERBQ0N4QyxNQUFLOzREQUNMRixNQUFLOzREQUNMQyxPQUFPbEMsU0FBU00sUUFBUTs0REFDeEJzRSxVQUFVN0M7NERBQ1ZvQyxXQUFXLGVBQXVELE9BQXhDM0MsT0FBT2xCLFFBQVEsR0FBRyxtQkFBbUI7NERBQy9EdUUsYUFBWTs7Ozs7O3dEQUVickQsT0FBT2xCLFFBQVEsa0JBQUksOERBQUNnRTs0REFBRUgsV0FBVTtzRUFBNkIzQyxPQUFPbEIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU1uRiw4REFBQzREO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ007NENBQUdOLFdBQVU7OzhEQUNaLDhEQUFDeEUsbUhBQVFBO29EQUFDd0UsV0FBVTtvREFBMEJDLE1BQU07Ozs7OztnREFBTTs7Ozs7OztzREFHNUQsOERBQUNGOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDUTs0REFBTVAsV0FBVTtzRUFBYzs7Ozs7O3NFQUMvQiw4REFBQ1E7NERBQ0N4QyxNQUFLOzREQUNMRixNQUFLOzREQUNMQyxPQUFPbEMsU0FBU1EsZ0JBQWdCOzREQUNoQ29FLFVBQVU3Qzs0REFDVm9DLFdBQVcsZUFBK0QsT0FBaEQzQyxPQUFPaEIsZ0JBQWdCLEdBQUcsbUJBQW1COzREQUN2RXFFLGFBQVk7Ozs7Ozt3REFFYnJELE9BQU9oQixnQkFBZ0Isa0JBQUksOERBQUM4RDs0REFBRUgsV0FBVTtzRUFBNkIzQyxPQUFPaEIsZ0JBQWdCOzs7Ozs7Ozs7Ozs7OERBRy9GLDhEQUFDMEQ7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs7OEVBQ0MsOERBQUNRO29FQUFNUCxXQUFVOzhFQUFjOzs7Ozs7OEVBQy9CLDhEQUFDVztvRUFDQzdDLE1BQUs7b0VBQ0xDLE9BQU9sQyxTQUFTWSxnQkFBZ0I7b0VBQ2hDZ0UsVUFBVTdDO29FQUNWb0MsV0FBVyxlQUErRCxPQUFoRDNDLE9BQU9aLGdCQUFnQixHQUFHLG1CQUFtQjs7c0ZBRXZFLDhEQUFDbUU7NEVBQU83QyxPQUFNO3NGQUFHOzs7Ozs7d0VBQ2hCTixrQkFBa0JvRCxHQUFHLENBQUM3QyxDQUFBQSxxQkFDckIsOERBQUM0QztnRkFBa0I3QyxPQUFPQzswRkFBT0E7K0VBQXBCQTs7Ozs7Ozs7Ozs7Z0VBR2hCWCxPQUFPWixnQkFBZ0Isa0JBQUksOERBQUMwRDtvRUFBRUgsV0FBVTs4RUFBNkIzQyxPQUFPWixnQkFBZ0I7Ozs7Ozs7Ozs7OztzRUFFL0YsOERBQUNzRDs7OEVBQ0MsOERBQUNRO29FQUFNUCxXQUFVOzhFQUFjOzs7Ozs7OEVBQy9CLDhEQUFDVztvRUFDQzdDLE1BQUs7b0VBQ0xDLE9BQU9sQyxTQUFTYSxNQUFNO29FQUN0QitELFVBQVU3QztvRUFDVm9DLFdBQVU7O3NGQUVWLDhEQUFDWTs0RUFBTzdDLE9BQU07c0ZBQUc7Ozs7Ozt3RUFDaEJMLFFBQVFtRCxHQUFHLENBQUNuRSxDQUFBQSx1QkFDWCw4REFBQ2tFO2dGQUFvQjdDLE9BQU9yQjswRkFBU0E7K0VBQXhCQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTXJCLDhEQUFDcUQ7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs7OEVBQ0MsOERBQUNRO29FQUFNUCxXQUFVOzhFQUFjOzs7Ozs7OEVBQy9CLDhEQUFDUTtvRUFDQ3hDLE1BQUs7b0VBQ0xGLE1BQUs7b0VBQ0xDLE9BQU9sQyxTQUFTUyxLQUFLO29FQUNyQm1FLFVBQVU3QztvRUFDVm9DLFdBQVcsZUFBb0QsT0FBckMzQyxPQUFPZixLQUFLLEdBQUcsbUJBQW1CO29FQUM1RG9FLGFBQVk7Ozs7OztnRUFFYnJELE9BQU9mLEtBQUssa0JBQUksOERBQUM2RDtvRUFBRUgsV0FBVTs4RUFBNkIzQyxPQUFPZixLQUFLOzs7Ozs7Ozs7Ozs7c0VBRXpFLDhEQUFDeUQ7OzhFQUNDLDhEQUFDUTtvRUFBTVAsV0FBVTs4RUFBYzs7Ozs7OzhFQUMvQiw4REFBQ1E7b0VBQ0N4QyxNQUFLO29FQUNMRixNQUFLO29FQUNMQyxPQUFPbEMsU0FBU1UsT0FBTztvRUFDdkJrRSxVQUFVN0M7b0VBQ1ZvQyxXQUFVO29FQUNWVSxhQUFZOzs7Ozs7Ozs7Ozs7c0VBR2hCLDhEQUFDWDs7OEVBQ0MsOERBQUNRO29FQUFNUCxXQUFVOzhFQUFjOzs7Ozs7OEVBQy9CLDhEQUFDVztvRUFDQzdDLE1BQUs7b0VBQ0xDLE9BQU9sQyxTQUFTYyxhQUFhO29FQUM3QjhELFVBQVU3QztvRUFDVm9DLFdBQVU7O3NGQUVWLDhEQUFDWTs0RUFBTzdDLE9BQU07c0ZBQUc7Ozs7OztzRkFDakIsOERBQUM2Qzs0RUFBTzdDLE9BQU07c0ZBQU87Ozs7OztzRkFDckIsOERBQUM2Qzs0RUFBTzdDLE9BQU07c0ZBQVE7Ozs7OztzRkFDdEIsOERBQUM2Qzs0RUFBTzdDLE9BQU07c0ZBQVM7Ozs7OztzRkFDdkIsOERBQUM2Qzs0RUFBTzdDLE9BQU07c0ZBQVU7Ozs7OztzRkFDeEIsOERBQUM2Qzs0RUFBTzdDLE9BQU07c0ZBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLM0IsOERBQUNnQzs7c0VBQ0MsOERBQUNROzREQUFNUCxXQUFVO3NFQUFjOzs7Ozs7c0VBQy9CLDhEQUFDYzs0REFDQ2hELE1BQUs7NERBQ0xDLE9BQU9sQyxTQUFTVyxXQUFXOzREQUMzQmlFLFVBQVU3Qzs0REFDVm1ELE1BQU07NERBQ05mLFdBQVcsZUFBMEQsT0FBM0MzQyxPQUFPYixXQUFXLEdBQUcsbUJBQW1COzREQUNsRWtFLGFBQVk7Ozs7Ozt3REFFYnJELE9BQU9iLFdBQVcsa0JBQUksOERBQUMyRDs0REFBRUgsV0FBVTtzRUFBNkIzQyxPQUFPYixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTXpGLDhEQUFDdUQ7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBR04sV0FBVTs7OERBQ1osOERBQUN2RSxtSEFBTUE7b0RBQUN1RSxXQUFVO29EQUEwQkMsTUFBTTs7Ozs7O2dEQUFNOzs7Ozs7O3NEQUcxRCw4REFBQ0Y7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUNROzREQUFNUCxXQUFVO3NFQUFjOzs7Ozs7c0VBQy9CLDhEQUFDUTs0REFDQ3hDLE1BQUs7NERBQ0xGLE1BQUs7NERBQ0xDLE9BQU9sQyxTQUFTZSxPQUFPOzREQUN2QjZELFVBQVU3Qzs0REFDVm9DLFdBQVU7NERBQ1ZVLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFHaEIsOERBQUNYO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7OzhFQUNDLDhEQUFDUTtvRUFBTVAsV0FBVTs4RUFBYzs7Ozs7OzhFQUMvQiw4REFBQ1E7b0VBQ0N4QyxNQUFLO29FQUNMRixNQUFLO29FQUNMQyxPQUFPbEMsU0FBU2dCLElBQUk7b0VBQ3BCNEQsVUFBVTdDO29FQUNWb0MsV0FBVTtvRUFDVlUsYUFBWTs7Ozs7Ozs7Ozs7O3NFQUdoQiw4REFBQ1g7OzhFQUNDLDhEQUFDUTtvRUFBTVAsV0FBVTs4RUFBYzs7Ozs7OzhFQUMvQiw4REFBQ1E7b0VBQ0N4QyxNQUFLO29FQUNMRixNQUFLO29FQUNMQyxPQUFPbEMsU0FBU2lCLFVBQVU7b0VBQzFCMkQsVUFBVTdDO29FQUNWb0MsV0FBVTtvRUFDVlUsYUFBWTs7Ozs7Ozs7Ozs7O3NFQUdoQiw4REFBQ1g7OzhFQUNDLDhEQUFDUTtvRUFBTVAsV0FBVTs4RUFBYzs7Ozs7OzhFQUMvQiw4REFBQ1c7b0VBQ0M3QyxNQUFLO29FQUNMQyxPQUFPbEMsU0FBU2tCLE9BQU87b0VBQ3ZCMEQsVUFBVTdDO29FQUNWb0MsV0FBVTs7c0ZBRVYsOERBQUNZOzRFQUFPN0MsT0FBTTtzRkFBUzs7Ozs7O3NGQUN2Qiw4REFBQzZDOzRFQUFPN0MsT0FBTTtzRkFBVzs7Ozs7O3NGQUN6Qiw4REFBQzZDOzRFQUFPN0MsT0FBTTtzRkFBUzs7Ozs7O3NGQUN2Qiw4REFBQzZDOzRFQUFPN0MsT0FBTTtzRkFBUzs7Ozs7O3NGQUN2Qiw4REFBQzZDOzRFQUFPN0MsT0FBTTtzRkFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVFoQyw4REFBQ2dDO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ007NENBQUdOLFdBQVU7OzhEQUNaLDhEQUFDdEUsbUhBQVFBO29EQUFDc0UsV0FBVTtvREFBMEJDLE1BQU07Ozs7OztnREFBTTs7Ozs7OztzREFHNUQsOERBQUNGOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDUTs0REFBTVAsV0FBVTtzRUFBYzs7Ozs7O3NFQUMvQiw4REFBQ1c7NERBQ0M3QyxNQUFLOzREQUNMQyxPQUFPbEMsU0FBU21CLGVBQWU7NERBQy9CeUQsVUFBVTdDOzREQUNWb0MsV0FBVTs7OEVBRVYsOERBQUNZO29FQUFPN0MsT0FBTTs4RUFBRzs7Ozs7O2dFQUNoQkosaUJBQWlCa0QsR0FBRyxDQUFDN0MsQ0FBQUEscUJBQ3BCLDhEQUFDNEM7d0VBQWtCN0MsT0FBT0M7a0ZBQU9BO3VFQUFwQkE7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUtuQiw4REFBQytCOztzRUFDQyw4REFBQ1E7NERBQU1QLFdBQVU7c0VBQWM7Ozs7OztzRUFDL0IsOERBQUNXOzREQUNDN0MsTUFBSzs0REFDTEMsT0FBT2xDLFNBQVNxQixNQUFNOzREQUN0QnVELFVBQVU3Qzs0REFDVm9DLFdBQVU7OzhFQUVWLDhEQUFDWTtvRUFBTzdDLE9BQU07OEVBQUc7Ozs7Ozs4RUFDakIsOERBQUM2QztvRUFBTzdDLE9BQU07OEVBQVc7Ozs7Ozs4RUFDekIsOERBQUM2QztvRUFBTzdDLE9BQU07OEVBQW1COzs7Ozs7OEVBQ2pDLDhEQUFDNkM7b0VBQU83QyxPQUFNOzhFQUFvQjs7Ozs7OzhFQUNsQyw4REFBQzZDO29FQUFPN0MsT0FBTTs4RUFBcUI7Ozs7Ozs4RUFDbkMsOERBQUM2QztvRUFBTzdDLE9BQU07OEVBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJL0IsOERBQUNnQzs7c0VBQ0MsOERBQUNROzREQUFNUCxXQUFVO3NFQUFjOzs7Ozs7c0VBQy9CLDhEQUFDYzs0REFDQ2hELE1BQUs7NERBQ0xDLE9BQU9sQyxTQUFTb0IsVUFBVTs0REFDMUJ3RCxVQUFVN0M7NERBQ1ZtRCxNQUFNOzREQUNOZixXQUFVOzREQUNWVSxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT3BCLDhEQUFDWDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNRO3dEQUNDeEMsTUFBSzt3REFDTEYsTUFBSzt3REFDTEssU0FBU3RDLFNBQVNzQixXQUFXO3dEQUM3QnNELFVBQVU3Qzt3REFDVm9DLFdBQVU7Ozs7OztrRUFFWiw4REFBQ087d0RBQU1QLFdBQVU7OzREQUF3Qjs0REFDekI7MEVBQ2QsOERBQUNnQjtnRUFBRUMsTUFBSztnRUFBY2pCLFdBQVU7MEVBQXFDOzs7Ozs7NERBRWhFOzREQUFJOzs7Ozs7Ozs7Ozs7OzRDQUlaM0MsT0FBT0YsV0FBVyxrQkFBSSw4REFBQ2dEO2dEQUFFSCxXQUFVOzBEQUF3QjNDLE9BQU9GLFdBQVc7Ozs7OzswREFFOUUsOERBQUM0QztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNRO3dEQUNDeEMsTUFBSzt3REFDTEYsTUFBSzt3REFDTEssU0FBU3RDLFNBQVN1QixVQUFVO3dEQUM1QnFELFVBQVU3Qzt3REFDVm9DLFdBQVU7Ozs7OztrRUFFWiw4REFBQ087d0RBQU1QLFdBQVU7OzREQUF3Qjs0REFDMUI7MEVBQ2IsOERBQUNnQjtnRUFBRUMsTUFBSztnRUFBNkJqQixXQUFVOzBFQUFxQzs7Ozs7OzREQUUvRTs0REFBSTs7Ozs7Ozs7Ozs7Ozs0Q0FJWjNDLE9BQU9ELFVBQVUsa0JBQUksOERBQUMrQztnREFBRUgsV0FBVTswREFBd0IzQyxPQUFPRCxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLaEYsOERBQUMyQztvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ2tCO3dDQUNDbEQsTUFBSzt3Q0FDTG1ELFVBQVU1RDt3Q0FDVnlDLFdBQVcsbU5BRVYsT0FEQ3pDLGVBQWUsa0NBQWtDO2tEQUdsREEsZUFBZSw0QkFBNEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3RELDhEQUFDakMsaUVBQU1BOzs7Ozs7Ozs7OztBQUdiO0dBbmxCd0JNO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvaW5zY3JpcHRpb24vb3JnYW5pc2F0aW9uL3BhZ2UudHN4PzU4OTkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L0hlYWRlcidcbmltcG9ydCBGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9Gb290ZXInXG5pbXBvcnQgeyBCdWlsZGluZzIsIEJ1aWxkaW5nLCBNYWlsLCBQaG9uZSwgTWFwUGluLCBHbG9iZSwgRmlsZVRleHQsIFVzZXIgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE9yZ2FuaXNhdGlvbkluc2NyaXB0aW9uUGFnZSgpIHtcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgLy8gSW5mb3JtYXRpb25zIGRlIGNvbnRhY3RcbiAgICBlbWFpbDogJycsXG4gICAgcGFzc3dvcmQ6ICcnLFxuICAgIGNvbmZpcm1QYXNzd29yZDogJycsXG4gICAgXG4gICAgLy8gSW5mb3JtYXRpb25zIHBlcnNvbm5lbGxlcyBkdSByZXByw6lzZW50YW50XG4gICAgZmlyc3ROYW1lOiAnJyxcbiAgICBsYXN0TmFtZTogJycsXG4gICAgcGhvbmU6ICcnLFxuICAgIFxuICAgIC8vIEluZm9ybWF0aW9ucyBkZSBsJ29yZ2FuaXNhdGlvblxuICAgIG9yZ2FuaXphdGlvbk5hbWU6ICcnLFxuICAgIHNpcmV0OiAnJyxcbiAgICB3ZWJzaXRlOiAnJyxcbiAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgb3JnYW5pemF0aW9uVHlwZTogJycsXG4gICAgc2VjdG9yOiAnJyxcbiAgICBlbXBsb3llZUNvdW50OiAnJyxcbiAgICBcbiAgICAvLyBBZHJlc3NlXG4gICAgYWRkcmVzczogJycsXG4gICAgY2l0eTogJycsXG4gICAgcG9zdGFsQ29kZTogJycsXG4gICAgY291bnRyeTogJ0ZyYW5jZScsXG4gICAgXG4gICAgLy8gUGFydGVuYXJpYXRcbiAgICBwYXJ0bmVyc2hpcFR5cGU6ICcnLFxuICAgIG1vdGl2YXRpb246ICcnLFxuICAgIGJ1ZGdldDogJycsXG4gICAgXG4gICAgLy8gQ29uZGl0aW9uc1xuICAgIGFjY2VwdFRlcm1zOiBmYWxzZSxcbiAgICBhY2NlcHRSR1BEOiBmYWxzZVxuICB9KVxuXG4gIGNvbnN0IFtlcnJvcnMsIHNldEVycm9yc10gPSB1c2VTdGF0ZTxSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+Pih7fSlcbiAgY29uc3QgW2lzU3VibWl0dGluZywgc2V0SXNTdWJtaXR0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIGNvbnN0IG9yZ2FuaXphdGlvblR5cGVzID0gW1xuICAgICdFbnRyZXByaXNlIHByaXbDqWUnLFxuICAgICdJbnN0aXR1dGlvbiBwdWJsaXF1ZScsXG4gICAgJ0NvbGxlY3Rpdml0w6kgdGVycml0b3JpYWxlJyxcbiAgICAnw4l0YWJsaXNzZW1lbnQgcHVibGljJyxcbiAgICAnRm9uZGF0aW9uJyxcbiAgICAnQXV0cmUnXG4gIF1cblxuICBjb25zdCBzZWN0b3JzID0gW1xuICAgICdUZWNobm9sb2dpZScsXG4gICAgJ0ZpbmFuY2UnLFxuICAgICdTYW50w6knLFxuICAgICfDiWR1Y2F0aW9uJyxcbiAgICAnQ29tbWVyY2UnLFxuICAgICdJbmR1c3RyaWUnLFxuICAgICdTZXJ2aWNlcycsXG4gICAgJ0FkbWluaXN0cmF0aW9uIHB1YmxpcXVlJyxcbiAgICAnQXV0cmUnXG4gIF1cblxuICBjb25zdCBwYXJ0bmVyc2hpcFR5cGVzID0gW1xuICAgICdNw6ljw6luYXQgZmluYW5jaWVyJyxcbiAgICAnTcOpY8OpbmF0IGRlIGNvbXDDqXRlbmNlcycsXG4gICAgJ1BhcnRlbmFyaWF0IHN0cmF0w6lnaXF1ZScsXG4gICAgJ1Nwb25zb3Jpbmcgw6l2w6luZW1lbnRpZWwnLFxuICAgICdDb2xsYWJvcmF0aW9uIHByb2pldCcsXG4gICAgJ0F1dHJlJ1xuICBdXG5cbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnQgfCBIVE1MU2VsZWN0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCB7IG5hbWUsIHZhbHVlLCB0eXBlIH0gPSBlLnRhcmdldFxuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbbmFtZV06IHR5cGUgPT09ICdjaGVja2JveCcgPyAoZS50YXJnZXQgYXMgSFRNTElucHV0RWxlbWVudCkuY2hlY2tlZCA6IHZhbHVlXG4gICAgfSkpXG4gICAgXG4gICAgaWYgKGVycm9yc1tuYW1lXSkge1xuICAgICAgc2V0RXJyb3JzKHByZXYgPT4gKHsgLi4ucHJldiwgW25hbWVdOiAnJyB9KSlcbiAgICB9XG4gIH1cblxuICBjb25zdCB2YWxpZGF0ZUZvcm0gPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3RXJyb3JzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge31cblxuICAgIC8vIFZhbGlkYXRpb24gZW1haWxcbiAgICBpZiAoIWZvcm1EYXRhLmVtYWlsKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAnTFxcJ2VtYWlsIGVzdCByZXF1aXMnXG4gICAgfSBlbHNlIGlmICghL1xcUytAXFxTK1xcLlxcUysvLnRlc3QoZm9ybURhdGEuZW1haWwpKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAnRm9ybWF0IGRcXCdlbWFpbCBpbnZhbGlkZSdcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0aW9uIG1vdCBkZSBwYXNzZVxuICAgIGlmICghZm9ybURhdGEucGFzc3dvcmQpIHtcbiAgICAgIG5ld0Vycm9ycy5wYXNzd29yZCA9ICdMZSBtb3QgZGUgcGFzc2UgZXN0IHJlcXVpcydcbiAgICB9IGVsc2UgaWYgKGZvcm1EYXRhLnBhc3N3b3JkLmxlbmd0aCA8IDgpIHtcbiAgICAgIG5ld0Vycm9ycy5wYXNzd29yZCA9ICdMZSBtb3QgZGUgcGFzc2UgZG9pdCBjb250ZW5pciBhdSBtb2lucyA4IGNhcmFjdMOocmVzJ1xuICAgIH1cblxuICAgIGlmIChmb3JtRGF0YS5wYXNzd29yZCAhPT0gZm9ybURhdGEuY29uZmlybVBhc3N3b3JkKSB7XG4gICAgICBuZXdFcnJvcnMuY29uZmlybVBhc3N3b3JkID0gJ0xlcyBtb3RzIGRlIHBhc3NlIG5lIGNvcnJlc3BvbmRlbnQgcGFzJ1xuICAgIH1cblxuICAgIC8vIFZhbGlkYXRpb24gY2hhbXBzIHJlcXVpc1xuICAgIGNvbnN0IHJlcXVpcmVkRmllbGRzID0gWydmaXJzdE5hbWUnLCAnbGFzdE5hbWUnLCAnb3JnYW5pemF0aW9uTmFtZScsICdvcmdhbml6YXRpb25UeXBlJywgJ2Rlc2NyaXB0aW9uJ11cbiAgICByZXF1aXJlZEZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHtcbiAgICAgIGlmICghZm9ybURhdGFbZmllbGQgYXMga2V5b2YgdHlwZW9mIGZvcm1EYXRhXSkge1xuICAgICAgICBuZXdFcnJvcnNbZmllbGRdID0gJ0NlIGNoYW1wIGVzdCByZXF1aXMnXG4gICAgICB9XG4gICAgfSlcblxuICAgIC8vIFZhbGlkYXRpb24gU0lSRVRcbiAgICBpZiAoZm9ybURhdGEuc2lyZXQgJiYgIS9eXFxkezE0fSQvLnRlc3QoZm9ybURhdGEuc2lyZXQpKSB7XG4gICAgICBuZXdFcnJvcnMuc2lyZXQgPSAnTGUgU0lSRVQgZG9pdCBjb250ZW5pciAxNCBjaGlmZnJlcydcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0aW9uIGFjY2VwdGF0aW9uIGRlcyBjb25kaXRpb25zXG4gICAgaWYgKCFmb3JtRGF0YS5hY2NlcHRUZXJtcykge1xuICAgICAgbmV3RXJyb3JzLmFjY2VwdFRlcm1zID0gJ1ZvdXMgZGV2ZXogYWNjZXB0ZXIgbGVzIGNvbmRpdGlvbnMgZFxcJ3V0aWxpc2F0aW9uJ1xuICAgIH1cblxuICAgIGlmICghZm9ybURhdGEuYWNjZXB0UkdQRCkge1xuICAgICAgbmV3RXJyb3JzLmFjY2VwdFJHUEQgPSAnVm91cyBkZXZleiBhY2NlcHRlciBsYSBwb2xpdGlxdWUgZGUgY29uZmlkZW50aWFsaXTDqSdcbiAgICB9XG5cbiAgICBzZXRFcnJvcnMobmV3RXJyb3JzKVxuICAgIHJldHVybiBPYmplY3Qua2V5cyhuZXdFcnJvcnMpLmxlbmd0aCA9PT0gMFxuICB9XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKVxuXG4gICAgaWYgKCF2YWxpZGF0ZUZvcm0oKSkge1xuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgc2V0SXNTdWJtaXR0aW5nKHRydWUpXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL3JlZ2lzdGVyL29yZ2FuaXphdGlvbicsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShmb3JtRGF0YSksXG4gICAgICB9KVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBhbGVydCgnSW5zY3JpcHRpb24gcsOpdXNzaWUgISBWb3VzIHJlY2V2cmV6IHVuIGVtYWlsIGRlIGNvbmZpcm1hdGlvbi4nKVxuICAgICAgICAvLyBSw6lpbml0aWFsaXNlciBsZSBmb3JtdWxhaXJlXG4gICAgICAgIHNldEZvcm1EYXRhKHtcbiAgICAgICAgICBlbWFpbDogJycsXG4gICAgICAgICAgcGFzc3dvcmQ6ICcnLFxuICAgICAgICAgIGNvbmZpcm1QYXNzd29yZDogJycsXG4gICAgICAgICAgZmlyc3ROYW1lOiAnJyxcbiAgICAgICAgICBsYXN0TmFtZTogJycsXG4gICAgICAgICAgcGhvbmU6ICcnLFxuICAgICAgICAgIG9yZ2FuaXphdGlvbk5hbWU6ICcnLFxuICAgICAgICAgIHNpcmV0OiAnJyxcbiAgICAgICAgICB3ZWJzaXRlOiAnJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgICAgICAgb3JnYW5pemF0aW9uVHlwZTogJycsXG4gICAgICAgICAgc2VjdG9yOiAnJyxcbiAgICAgICAgICBlbXBsb3llZUNvdW50OiAnJyxcbiAgICAgICAgICBhZGRyZXNzOiAnJyxcbiAgICAgICAgICBjaXR5OiAnJyxcbiAgICAgICAgICBwb3N0YWxDb2RlOiAnJyxcbiAgICAgICAgICBjb3VudHJ5OiAnRnJhbmNlJyxcbiAgICAgICAgICBwYXJ0bmVyc2hpcFR5cGU6ICcnLFxuICAgICAgICAgIG1vdGl2YXRpb246ICcnLFxuICAgICAgICAgIGJ1ZGdldDogJycsXG4gICAgICAgICAgYWNjZXB0VGVybXM6IGZhbHNlLFxuICAgICAgICAgIGFjY2VwdFJHUEQ6IGZhbHNlXG4gICAgICAgIH0pXG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBBZmZpY2hlciBsZXMgZXJyZXVycyBkZSB2YWxpZGF0aW9uXG4gICAgICAgIGlmIChkYXRhLmRldGFpbHMpIHtcbiAgICAgICAgICBjb25zdCBuZXdFcnJvcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7fVxuICAgICAgICAgIGRhdGEuZGV0YWlscy5mb3JFYWNoKChlcnJvcjogYW55KSA9PiB7XG4gICAgICAgICAgICBpZiAoZXJyb3IucGF0aCAmJiBlcnJvci5wYXRoLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgbmV3RXJyb3JzW2Vycm9yLnBhdGhbMF1dID0gZXJyb3IubWVzc2FnZVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pXG4gICAgICAgICAgc2V0RXJyb3JzKG5ld0Vycm9ycylcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBhbGVydChkYXRhLmVycm9yIHx8ICdVbmUgZXJyZXVyIGVzdCBzdXJ2ZW51ZSBsb3JzIGRlIGxcXCdpbnNjcmlwdGlvbi4nKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0VycmV1ciBsb3JzIGRlIGxcXCdpbnNjcmlwdGlvbjonLCBlcnJvcilcbiAgICAgIGFsZXJ0KCdVbmUgZXJyZXVyIGVzdCBzdXJ2ZW51ZS4gVmV1aWxsZXogcsOpZXNzYXllci4nKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1N1Ym1pdHRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICA8SGVhZGVyIC8+XG4gICAgICBcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLWthcm1hIHB5LTEyXCI+XG4gICAgICAgIHsvKiBFbi10w6p0ZSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1yIGZyb20tc2Vjb25kYXJ5LTUwMCB0by1zZWNvbmRhcnktNjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8QnVpbGRpbmcyIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIiBzaXplPXszMn0gLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBsZzp0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICBJbnNjcmlwdGlvbiBPcmdhbmlzYXRpb25cbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTYwMCBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgRW50cmVwcmlzZXMgZXQgaW5zdGl0dXRpb25zLCByZWpvaWduZXogbm90cmUgcsOpc2VhdSBkZSBwYXJ0ZW5haXJlcyBlbmdhZ8Opc1xuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZvcm11bGFpcmUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIFNlY3Rpb24gQ29tcHRlICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb3JtLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZvcm0tdGl0bGUgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJtci0zIHRleHQtc2Vjb25kYXJ5LTYwMFwiIHNpemU9ezI0fSAvPlxuICAgICAgICAgICAgICAgIEluZm9ybWF0aW9ucyBkZSBjb25uZXhpb25cbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJrYXJtYS1sYWJlbFwiPkVtYWlsICo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbWFpbH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BrYXJtYS1pbnB1dCAke2Vycm9ycy5lbWFpbCA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cInZvdHJlQGVtYWlsLmNvbVwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAge2Vycm9ycy5lbWFpbCAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC1zbSBtdC0xXCI+e2Vycm9ycy5lbWFpbH08L3A+fVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwia2FybWEtbGFiZWxcIj5Uw6lsw6lwaG9uZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRlbFwiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwaG9uZVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5waG9uZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJrYXJtYS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiKzMzIDEgMjMgNDUgNjcgODlcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImthcm1hLWxhYmVsXCI+TW90IGRlIHBhc3NlICo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wYXNzd29yZH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BrYXJtYS1pbnB1dCAke2Vycm9ycy5wYXNzd29yZCA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk1pbmltdW0gOCBjYXJhY3TDqHJlc1wiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAge2Vycm9ycy5wYXNzd29yZCAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC1zbSBtdC0xXCI+e2Vycm9ycy5wYXNzd29yZH08L3A+fVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwia2FybWEtbGFiZWxcIj5Db25maXJtZXIgbGUgbW90IGRlIHBhc3NlICo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJjb25maXJtUGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlybVBhc3N3b3JkfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGthcm1hLWlucHV0ICR7ZXJyb3JzLmNvbmZpcm1QYXNzd29yZCA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlLDqXDDqXRleiBsZSBtb3QgZGUgcGFzc2VcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIHtlcnJvcnMuY29uZmlybVBhc3N3b3JkICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtIG10LTFcIj57ZXJyb3JzLmNvbmZpcm1QYXNzd29yZH08L3A+fVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogU2VjdGlvbiBSZXByw6lzZW50YW50ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb3JtLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZvcm0tdGl0bGUgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJtci0zIHRleHQtc2Vjb25kYXJ5LTYwMFwiIHNpemU9ezI0fSAvPlxuICAgICAgICAgICAgICAgIFJlcHLDqXNlbnRhbnQgZGUgbCdvcmdhbmlzYXRpb25cbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJrYXJtYS1sYWJlbFwiPlByw6lub20gKjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwiZmlyc3ROYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmZpcnN0TmFtZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BrYXJtYS1pbnB1dCAke2Vycm9ycy5maXJzdE5hbWUgPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJWb3RyZSBwcsOpbm9tXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICB7ZXJyb3JzLmZpcnN0TmFtZSAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC1zbSBtdC0xXCI+e2Vycm9ycy5maXJzdE5hbWV9PC9wPn1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImthcm1hLWxhYmVsXCI+Tm9tICo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgbmFtZT1cImxhc3ROYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmxhc3ROYW1lfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGthcm1hLWlucHV0ICR7ZXJyb3JzLmxhc3ROYW1lID8gJ2JvcmRlci1yZWQtNTAwJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVm90cmUgbm9tXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICB7ZXJyb3JzLmxhc3ROYW1lICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtIG10LTFcIj57ZXJyb3JzLmxhc3ROYW1lfTwvcD59XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBTZWN0aW9uIE9yZ2FuaXNhdGlvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb3JtLXRpdGxlIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEJ1aWxkaW5nIGNsYXNzTmFtZT1cIm1yLTMgdGV4dC1zZWNvbmRhcnktNjAwXCIgc2l6ZT17MjR9IC8+XG4gICAgICAgICAgICAgICAgSW5mb3JtYXRpb25zIGRlIGwnb3JnYW5pc2F0aW9uXG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJrYXJtYS1sYWJlbFwiPk5vbSBkZSBsJ29yZ2FuaXNhdGlvbiAqPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJvcmdhbml6YXRpb25OYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLm9yZ2FuaXphdGlvbk5hbWV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtga2FybWEtaW5wdXQgJHtlcnJvcnMub3JnYW5pemF0aW9uTmFtZSA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk5vbSBvZmZpY2llbCBkZSB2b3RyZSBvcmdhbmlzYXRpb25cIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIHtlcnJvcnMub3JnYW5pemF0aW9uTmFtZSAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC1zbSBtdC0xXCI+e2Vycm9ycy5vcmdhbml6YXRpb25OYW1lfTwvcD59XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwia2FybWEtbGFiZWxcIj5UeXBlIGQnb3JnYW5pc2F0aW9uICo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgbmFtZT1cIm9yZ2FuaXphdGlvblR5cGVcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5vcmdhbml6YXRpb25UeXBlfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BrYXJtYS1pbnB1dCAke2Vycm9ycy5vcmdhbml6YXRpb25UeXBlID8gJ2JvcmRlci1yZWQtNTAwJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+U8OpbGVjdGlvbm5leiB1biB0eXBlPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAge29yZ2FuaXphdGlvblR5cGVzLm1hcCh0eXBlID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXt0eXBlfSB2YWx1ZT17dHlwZX0+e3R5cGV9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICB7ZXJyb3JzLm9yZ2FuaXphdGlvblR5cGUgJiYgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIHRleHQtc20gbXQtMVwiPntlcnJvcnMub3JnYW5pemF0aW9uVHlwZX08L3A+fVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwia2FybWEtbGFiZWxcIj5TZWN0ZXVyIGQnYWN0aXZpdMOpPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJzZWN0b3JcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5zZWN0b3J9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImthcm1hLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5Tw6lsZWN0aW9ubmV6IHVuIHNlY3RldXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICB7c2VjdG9ycy5tYXAoc2VjdG9yID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtzZWN0b3J9IHZhbHVlPXtzZWN0b3J9PntzZWN0b3J9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJrYXJtYS1sYWJlbFwiPlNJUkVUPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJzaXJldFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnNpcmV0fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BrYXJtYS1pbnB1dCAke2Vycm9ycy5zaXJldCA/ICdib3JkZXItcmVkLTUwMCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMTQgY2hpZmZyZXNcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICB7ZXJyb3JzLnNpcmV0ICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtIG10LTFcIj57ZXJyb3JzLnNpcmV0fTwvcD59XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJrYXJtYS1sYWJlbFwiPlNpdGUgd2ViPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInVybFwiXG4gICAgICAgICAgICAgICAgICAgICAgbmFtZT1cIndlYnNpdGVcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS53ZWJzaXRlfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJrYXJtYS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJodHRwczovL3ZvdHJlLXNpdGUuY29tXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImthcm1hLWxhYmVsXCI+Tm9tYnJlIGQnZW1wbG95w6lzPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJlbXBsb3llZUNvdW50XCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZW1wbG95ZWVDb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwia2FybWEtaW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlPDqWxlY3Rpb25uZXo8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiMS0xMFwiPjEtMTA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiMTEtNTBcIj4xMS01MDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCI1MS0yMDBcIj41MS0yMDA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiMjAxLTUwMFwiPjIwMS01MDA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiNTAwK1wiPjUwMCs8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwia2FybWEtbGFiZWxcIj5EZXNjcmlwdGlvbiBkZSBsJ29yZ2FuaXNhdGlvbiAqPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtga2FybWEtaW5wdXQgJHtlcnJvcnMuZGVzY3JpcHRpb24gPyAnYm9yZGVyLXJlZC01MDAnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEw6ljcml2ZXogbGVzIGFjdGl2aXTDqXMgZXQgbWlzc2lvbnMgZGUgdm90cmUgb3JnYW5pc2F0aW9uLi4uXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICB7ZXJyb3JzLmRlc2NyaXB0aW9uICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtIG10LTFcIj57ZXJyb3JzLmRlc2NyaXB0aW9ufTwvcD59XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBTZWN0aW9uIEFkcmVzc2UgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvcm0tc2VjdGlvblwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwiZm9ybS10aXRsZSBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxNYXBQaW4gY2xhc3NOYW1lPVwibXItMyB0ZXh0LXNlY29uZGFyeS02MDBcIiBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgICAgICBBZHJlc3NlXG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJrYXJtYS1sYWJlbFwiPkFkcmVzc2U8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgbmFtZT1cImFkZHJlc3NcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYWRkcmVzc31cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJrYXJtYS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTnVtw6lybyBldCBub20gZGUgcnVlXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwia2FybWEtbGFiZWxcIj5WaWxsZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwiY2l0eVwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNpdHl9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImthcm1hLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlZpbGxlXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImthcm1hLWxhYmVsXCI+Q29kZSBwb3N0YWw8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInBvc3RhbENvZGVcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wb3N0YWxDb2RlfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJrYXJtYS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCI3NTAwMVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJrYXJtYS1sYWJlbFwiPlBheXM8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImNvdW50cnlcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb3VudHJ5fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJrYXJtYS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRnJhbmNlXCI+RnJhbmNlPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkJlbGdpcXVlXCI+QmVsZ2lxdWU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiU3Vpc3NlXCI+U3Vpc3NlPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkNhbmFkYVwiPkNhbmFkYTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJBdXRyZVwiPkF1dHJlPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBTZWN0aW9uIFBhcnRlbmFyaWF0ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb3JtLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZvcm0tdGl0bGUgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwibXItMyB0ZXh0LXNlY29uZGFyeS02MDBcIiBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgICAgICBQYXJ0ZW5hcmlhdCBzb3VoYWl0w6lcbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImthcm1hLWxhYmVsXCI+VHlwZSBkZSBwYXJ0ZW5hcmlhdDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwYXJ0bmVyc2hpcFR5cGVcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGFydG5lcnNoaXBUeXBlfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImthcm1hLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlPDqWxlY3Rpb25uZXogdW4gdHlwZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICB7cGFydG5lcnNoaXBUeXBlcy5tYXAodHlwZSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e3R5cGV9IHZhbHVlPXt0eXBlfT57dHlwZX08L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImthcm1hLWxhYmVsXCI+QnVkZ2V0IGVudmlzYWfDqSAob3B0aW9ubmVsKTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJidWRnZXRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYnVkZ2V0fVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImthcm1hLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlPDqWxlY3Rpb25uZXogdW5lIGZvdXJjaGV0dGU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjwgNSAwMDDigqxcIj5Nb2lucyBkZSA1IDAwMOKCrDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiNSAwMDDigqwgLSAxNSAwMDDigqxcIj41IDAwMOKCrCAtIDE1IDAwMOKCrDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiMTUgMDAw4oKsIC0gNTAgMDAw4oKsXCI+MTUgMDAw4oKsIC0gNTAgMDAw4oKsPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCI1MCAwMDDigqwgLSAxMDAgMDAw4oKsXCI+NTAgMDAw4oKsIC0gMTAwIDAwMOKCrDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiPiAxMDAgMDAw4oKsXCI+UGx1cyBkZSAxMDAgMDAw4oKsPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwia2FybWEtbGFiZWxcIj5Nb3RpdmF0aW9uIGV0IG9iamVjdGlmczwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgbmFtZT1cIm1vdGl2YXRpb25cIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubW90aXZhdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJrYXJtYS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRXhwbGlxdWV6IHZvcyBtb3RpdmF0aW9ucyBldCBvYmplY3RpZnMgcG91ciBjZSBwYXJ0ZW5hcmlhdC4uLlwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQ29uZGl0aW9ucyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgbmFtZT1cImFjY2VwdFRlcm1zXCJcbiAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Zm9ybURhdGEuYWNjZXB0VGVybXN9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMSBtci0zXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIEonYWNjZXB0ZSBsZXN7JyAnfVxuICAgICAgICAgICAgICAgICAgICA8YSBocmVmPVwiL2NvbmRpdGlvbnNcIiBjbGFzc05hbWU9XCJ0ZXh0LXNlY29uZGFyeS02MDAgaG92ZXI6dW5kZXJsaW5lXCI+XG4gICAgICAgICAgICAgICAgICAgICAgY29uZGl0aW9ucyBkJ3V0aWxpc2F0aW9uXG4gICAgICAgICAgICAgICAgICAgIDwvYT57JyAnfVxuICAgICAgICAgICAgICAgICAgICBkZSBLYXJtYSBDb20gU29saWRhcml0w6kgKlxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLmFjY2VwdFRlcm1zICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtXCI+e2Vycm9ycy5hY2NlcHRUZXJtc308L3A+fVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydFwiPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJhY2NlcHRSR1BEXCJcbiAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Zm9ybURhdGEuYWNjZXB0UkdQRH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIG1yLTNcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgSidhY2NlcHRlIGxheycgJ31cbiAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIi9wb2xpdGlxdWUtY29uZmlkZW50aWFsaXRlXCIgY2xhc3NOYW1lPVwidGV4dC1zZWNvbmRhcnktNjAwIGhvdmVyOnVuZGVybGluZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHBvbGl0aXF1ZSBkZSBjb25maWRlbnRpYWxpdMOpXG4gICAgICAgICAgICAgICAgICAgIDwvYT57JyAnfVxuICAgICAgICAgICAgICAgICAgICBldCBsZSB0cmFpdGVtZW50IGRlIG1lcyBkb25uw6llcyBwZXJzb25uZWxsZXMgc2Vsb24gbGUgUkdQRCAqXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIHtlcnJvcnMuYWNjZXB0UkdQRCAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC1zbVwiPntlcnJvcnMuYWNjZXB0UkdQRH08L3A+fVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQm91dG9uIGRlIHNvdW1pc3Npb24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGJnLWdyYWRpZW50LXRvLXIgZnJvbS1zZWNvbmRhcnktNjAwIHRvLXNlY29uZGFyeS03MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSBweS0zIHB4LTEyIHJvdW5kZWQtbGcgaG92ZXI6ZnJvbS1zZWNvbmRhcnktNzAwIGhvdmVyOnRvLXNlY29uZGFyeS04MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDUgdGV4dC1sZyAke1xuICAgICAgICAgICAgICAgICAgaXNTdWJtaXR0aW5nID8gJ29wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkJyA6ICcnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNTdWJtaXR0aW5nID8gJ0luc2NyaXB0aW9uIGVuIGNvdXJzLi4uJyA6ICdTb3VtZXR0cmUgbWEgY2FuZGlkYXR1cmUnfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZm9ybT5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkhlYWRlciIsIkZvb3RlciIsIkJ1aWxkaW5nMiIsIkJ1aWxkaW5nIiwiTWFwUGluIiwiRmlsZVRleHQiLCJVc2VyIiwiT3JnYW5pc2F0aW9uSW5zY3JpcHRpb25QYWdlIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsImVtYWlsIiwicGFzc3dvcmQiLCJjb25maXJtUGFzc3dvcmQiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsInBob25lIiwib3JnYW5pemF0aW9uTmFtZSIsInNpcmV0Iiwid2Vic2l0ZSIsImRlc2NyaXB0aW9uIiwib3JnYW5pemF0aW9uVHlwZSIsInNlY3RvciIsImVtcGxveWVlQ291bnQiLCJhZGRyZXNzIiwiY2l0eSIsInBvc3RhbENvZGUiLCJjb3VudHJ5IiwicGFydG5lcnNoaXBUeXBlIiwibW90aXZhdGlvbiIsImJ1ZGdldCIsImFjY2VwdFRlcm1zIiwiYWNjZXB0UkdQRCIsImVycm9ycyIsInNldEVycm9ycyIsImlzU3VibWl0dGluZyIsInNldElzU3VibWl0dGluZyIsIm9yZ2FuaXphdGlvblR5cGVzIiwic2VjdG9ycyIsInBhcnRuZXJzaGlwVHlwZXMiLCJoYW5kbGVJbnB1dENoYW5nZSIsImUiLCJuYW1lIiwidmFsdWUiLCJ0eXBlIiwidGFyZ2V0IiwicHJldiIsImNoZWNrZWQiLCJ2YWxpZGF0ZUZvcm0iLCJuZXdFcnJvcnMiLCJ0ZXN0IiwibGVuZ3RoIiwicmVxdWlyZWRGaWVsZHMiLCJmb3JFYWNoIiwiZmllbGQiLCJPYmplY3QiLCJrZXlzIiwiaGFuZGxlU3VibWl0IiwicHJldmVudERlZmF1bHQiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiZGF0YSIsImpzb24iLCJvayIsImFsZXJ0IiwiZGV0YWlscyIsImVycm9yIiwicGF0aCIsIm1lc3NhZ2UiLCJjb25zb2xlIiwiZGl2IiwiY2xhc3NOYW1lIiwic2l6ZSIsImgxIiwicCIsImZvcm0iLCJvblN1Ym1pdCIsImgyIiwibGFiZWwiLCJpbnB1dCIsIm9uQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJzZWxlY3QiLCJvcHRpb24iLCJtYXAiLCJ0ZXh0YXJlYSIsInJvd3MiLCJhIiwiaHJlZiIsImJ1dHRvbiIsImRpc2FibGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inscription/organisation/page.tsx\n"));

/***/ })

});