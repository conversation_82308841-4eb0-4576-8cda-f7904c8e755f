// Script de test pour vérifier le dashboard avec les vraies données
const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function testDashboardAPI() {
  console.log('🧪 Test de l\'API Dashboard')
  console.log('==========================')
  
  try {
    // Test de l'API stats
    console.log('📊 Test de l\'API /api/dashboard/stats...')
    const response = await fetch(`${BASE_URL}/api/dashboard/stats`)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    
    console.log('✅ API Dashboard Stats - Réponse reçue')
    console.log('\n📈 Statistiques générales:')
    console.log(`  Total candidats: ${data.overview.totalCandidates}`)
    console.log(`  En attente: ${data.overview.pendingApplications}`)
    console.log(`  Entretiens prévus: ${data.overview.scheduledInterviews}`)
    console.log(`  Membres approuvés: ${data.overview.approvedMembers}`)
    
    console.log('\n👥 Répartition par type:')
    Object.entries(data.userTypes).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`)
    })
    
    console.log('\n📋 Répartition par statut:')
    Object.entries(data.statuses).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}`)
    })
    
    console.log('\n📝 Candidatures récentes:')
    data.recentApplications.slice(0, 5).forEach(app => {
      console.log(`  • ${app.name} (${app.type}) - ${app.status}`)
    })
    
    return true
    
  } catch (error) {
    console.log('❌ Erreur lors du test de l\'API Dashboard:', error.message)
    return false
  }
}

async function testDashboardPage() {
  console.log('\n🌐 Test de la page Dashboard')
  console.log('=============================')
  
  try {
    const response = await fetch(`${BASE_URL}/dashboard`)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const html = await response.text()
    
    // Vérifications basiques
    const checks = [
      { name: 'Titre Dashboard', test: html.includes('Dashboard RH') },
      { name: 'Logo Karma Com', test: html.includes('Karma Com') },
      { name: 'Navigation', test: html.includes('Vue d\'ensemble') },
      { name: 'Statistiques', test: html.includes('Total Candidats') },
      { name: 'Candidatures récentes', test: html.includes('Candidatures récentes') }
    ]
    
    console.log('🔍 Vérifications de la page:')
    checks.forEach(check => {
      console.log(`  ${check.test ? '✅' : '❌'} ${check.name}`)
    })
    
    const allPassed = checks.every(check => check.test)
    
    if (allPassed) {
      console.log('✅ Page Dashboard - Tous les éléments sont présents')
      return true
    } else {
      console.log('⚠️ Page Dashboard - Certains éléments manquent')
      return false
    }
    
  } catch (error) {
    console.log('❌ Erreur lors du test de la page Dashboard:', error.message)
    return false
  }
}

async function testConstants() {
  console.log('\n🔧 Test des API Constants')
  console.log('==========================')
  
  const endpoints = [
    '/api/constants',
    '/api/constants/organization-types',
    '/api/constants/sectors',
    '/api/constants/skills',
    '/api/constants/departments'
  ]
  
  let successCount = 0
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`)
      
      if (response.ok) {
        const data = await response.json()
        const count = Array.isArray(data) ? data.length : Object.keys(data).length
        console.log(`✅ ${endpoint} - ${count} éléments`)
        successCount++
      } else {
        console.log(`❌ ${endpoint} - HTTP ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Erreur: ${error.message}`)
    }
  }
  
  console.log(`\n📊 Résultat: ${successCount}/${endpoints.length} APIs fonctionnelles`)
  return successCount === endpoints.length
}

async function main() {
  console.log('🚀 Tests du Dashboard avec données réelles')
  console.log('==========================================')
  
  // Vérifier que l'application est accessible
  try {
    const response = await fetch(`${BASE_URL}`)
    if (!response.ok) {
      throw new Error(`Application non accessible: HTTP ${response.status}`)
    }
    console.log('✅ Application accessible')
  } catch (error) {
    console.log('❌ Application non accessible:', error.message)
    console.log('💡 Assurez-vous que l\'application est démarrée avec "npm run dev"')
    process.exit(1)
  }
  
  let totalTests = 0
  let passedTests = 0
  
  // Test API Dashboard
  totalTests++
  if (await testDashboardAPI()) {
    passedTests++
  }
  
  // Test page Dashboard
  totalTests++
  if (await testDashboardPage()) {
    passedTests++
  }
  
  // Test APIs Constants
  totalTests++
  if (await testConstants()) {
    passedTests++
  }
  
  // Résumé final
  console.log('\n📋 Résumé des tests')
  console.log('===================')
  console.log(`✅ Tests réussis: ${passedTests}/${totalTests}`)
  console.log(`❌ Tests échoués: ${totalTests - passedTests}/${totalTests}`)
  
  if (passedTests === totalTests) {
    console.log('\n🎉 Tous les tests sont passés!')
    console.log('\n💡 Le dashboard affiche maintenant les vraies données de la base!')
    console.log('   • Accédez au dashboard: http://localhost:3000/dashboard')
    console.log('   • Connectez-vous avec: <EMAIL> / admin123')
  } else {
    console.log('\n⚠️ Certains tests ont échoué.')
    console.log('💡 Vérifiez:')
    console.log('   • La base de données est-elle initialisée?')
    console.log('   • Les fake data sont-elles créées?')
    console.log('   • L\'application est-elle démarrée?')
    process.exit(1)
  }
}

// Vérifier si node-fetch est disponible
try {
  require('node-fetch')
} catch (error) {
  console.log('❌ node-fetch n\'est pas installé')
  console.log('💡 Il devrait être installé avec les devDependencies')
  process.exit(1)
}

main()
