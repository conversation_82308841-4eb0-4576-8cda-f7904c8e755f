'use client'

import React, { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import Logo from '@/components/ui/Logo'
import LoginForm from '@/components/dashboard/LoginForm'
import { useAuth } from '@/hooks/useAuth'
export const dynamic = 'force-dynamic'

import {
  Users,
  Building2,
  Heart,
  Calendar,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Search,
  Filter,
  Download,
  Bell,
  Settings,
  LogOut
} from 'lucide-react'

// Interface pour les candidatures récentes
interface RecentApplication {
  id: string
  name: string
  email: string
  type: string
  organization: string | null
  status: string
  date: string
  userType?: string
  membershipStatus?: string
  createdAt?: string
}

// Interface pour les entretiens
interface Interview {
  id: string
  title: string
  description: string
  candidateId: string
  candidateName: string
  candidateEmail: string
  scheduledAt: string
  type: 'DISCOVERY' | 'FOLLOW_UP' | 'FINAL'
  status: 'SCHEDULED' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED'
  notes?: string
}

// Interface pour les formulaires
interface InterviewFormData {
  title: string
  description: string
  candidateId: string
  scheduledAt: string
  type: 'DISCOVERY' | 'FOLLOW_UP' | 'FINAL'
}

export default function DashboardPage() {
  const { isAuthenticated, loading: authLoading, login, logout } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [loading, setLoading] = useState(false) // Commencer sans chargement
  const [error, setError] = useState<string | null>(null)
  const [loginError, setLoginError] = useState<string | null>(null)
  const [loginLoading, setLoginLoading] = useState(false)

  // États pour les entretiens et candidatures
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [interviewsLoading, setInterviewsLoading] = useState(false)
  const [showInterviewForm, setShowInterviewForm] = useState(false)
  const [interviewFormData, setInterviewFormData] = useState<InterviewFormData>({
    title: '',
    description: '',
    candidateId: '',
    scheduledAt: '',
    type: 'DISCOVERY'
  })
  const [candidates, setCandidates] = useState<RecentApplication[]>([])
  const [selectedCandidate, setSelectedCandidate] = useState<string>('')
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  // Fonction de connexion
  const handleLogin = async (email: string, password: string): Promise<boolean> => {
    setLoginLoading(true)
    setLoginError(null)

    try {
      const success = await login(email, password)
      if (!success) {
        setLoginError('Email ou mot de passe incorrect')
      }
      return success
    } catch (error) {
      setLoginError('Erreur lors de la connexion')
      return false
    } finally {
      setLoginLoading(false)
    }
  }

  // Fonction de déconnexion
  const handleLogout = () => {
    logout()
  }

  // Fonctions utilitaires
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Association':
        return <Heart className="text-red-500" size={16} />
      case 'Organisation':
        return <Building2 className="text-blue-500" size={16} />
      case 'Bénévole':
        return <Users className="text-green-500" size={16} />
      default:
        return <Users className="text-gray-500" size={16} />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'En attente', color: 'bg-yellow-100 text-yellow-800' },
      approved: { label: 'Approuvé', color: 'bg-green-100 text-green-800' },
      rejected: { label: 'Rejeté', color: 'bg-red-100 text-red-800' },
      confirmed: { label: 'Confirmé', color: 'bg-blue-100 text-blue-800' },
      completed: { label: 'Terminé', color: 'bg-gray-100 text-gray-800' },
      cancelled: { label: 'Annulé', color: 'bg-red-100 text-red-800' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  // Charger les entretiens
  const loadInterviews = useCallback(async () => {
    try {
      setInterviewsLoading(true)
      console.log('📅 Chargement des entretiens...')
      const response = await fetch('/api/interviews')
      if (response.ok) {
        const data = await response.json()
        console.log('📅 Entretiens chargés:', data.length, 'entretiens')
        setInterviews(data)
      } else {
        console.error('❌ Erreur chargement entretiens:', response.status)
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des entretiens:', error)
    } finally {
      setInterviewsLoading(false)
    }
  }, [])

  // Charger les candidats
  const loadCandidates = useCallback(async () => {
    try {
      console.log('👥 Chargement des candidats...')
      const response = await fetch('/api/candidates')
      if (response.ok) {
        const data = await response.json()
        console.log('👥 Candidats chargés:', data.candidates.length, 'candidats')
        setCandidates(data.candidates)
      } else {
        console.error('❌ Erreur chargement candidats:', response.status)
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des candidats:', error)
    }
  }, [])

  // Fonction de chargement des données stabilisée avec useCallback
  const fetchDashboardData = useCallback(async () => {
    const isDevelopment = process.env.NODE_ENV === 'development'

    // En développement, permettre le chargement sans authentification
    if (!isDevelopment && (!isAuthenticated || authLoading)) {
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Récupérer les headers d'authentification directement
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      const response = await fetch('/api/dashboard/stats', { headers })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setDashboardData(data)
    } catch (err) {
      console.error('Erreur lors du chargement des données:', err)
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
    } finally {
      setLoading(false)
    }
  }, [isAuthenticated, authLoading])

  // Charger les données du dashboard
  useEffect(() => {
    fetchDashboardData()
    loadInterviews()
    loadCandidates()
  }, [fetchDashboardData, loadInterviews, loadCandidates])

  // Données par défaut si pas encore chargées
  const stats = dashboardData?.overview || {
    totalCandidates: 0,
    pendingApplications: 0,
    scheduledInterviews: 0,
    approvedMembers: 0
  }

  // Données des candidatures récentes depuis l'API
  const recentApplications = dashboardData?.recentApplications || []

  // Données des entretiens à venir (simulées pour l'instant)
  const upcomingInterviews = [
    {
      id: 1,
      candidateName: 'Sophie Laurent',
      type: 'Découverte',
      date: '2024-01-16',
      time: '14:00',
      status: 'confirmed'
    },
    {
      id: 2,
      candidateName: 'Marc Rousseau',
      type: 'Intégration',
      date: '2024-01-16',
      time: '16:30',
      status: 'pending'
    },
    {
      id: 3,
      candidateName: 'Alice Bernard',
      type: 'Suivi',
      date: '2024-01-17',
      time: '10:00',
      status: 'confirmed'
    }
  ]

  // Fonctions pour gérer les entretiens
  const handleCreateInterview = async (formData: InterviewFormData) => {
    try {
      console.log('📝 Création entretien avec données:', formData)

      const response = await fetch('/api/interviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      console.log('📡 Réponse API:', response.status, response.statusText)

      if (response.ok) {
        const newInterview = await response.json()
        console.log('✅ Entretien créé:', newInterview)

        setInterviews(prev => [...prev, newInterview])
        setShowInterviewForm(false)
        setInterviewFormData({
          title: '',
          description: '',
          candidateId: '',
          scheduledAt: '',
          type: 'DISCOVERY'
        })

        // Recharger les données
        await loadInterviews()
        await fetchDashboardData()

        // Afficher un message de succès
        setSuccessMessage(`Entretien "${newInterview.title}" créé avec succès`)
        setTimeout(() => setSuccessMessage(null), 5000)

        console.log('🔄 Données rechargées')
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ Erreur API:', errorData)
        throw new Error(errorData.error || 'Erreur lors de la création de l\'entretien')
      }
    } catch (error) {
      console.error('❌ Erreur création entretien:', error)
      setError(error instanceof Error ? error.message : 'Impossible de créer l\'entretien')
    }
  }

  const handleUpdateCandidateStatus = async (candidateId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/candidates/${candidateId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        // Recharger les données
        fetchDashboardData()
        loadCandidates()
      } else {
        throw new Error('Erreur lors de la mise à jour du statut')
      }
    } catch (error) {
      console.error('Erreur:', error)
      setError('Impossible de mettre à jour le statut')
    }
  }



  // Mode développement : permettre l'accès sans authentification
  const isDevelopment = process.env.NODE_ENV === 'development'

  // Afficher le formulaire de login si pas authentifié (sauf en développement)
  if (authLoading && !isDevelopment) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="mt-4 text-gray-600">Vérification de l'authentification...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated && !isDevelopment) {
    return (
      <LoginForm
        onLogin={handleLogin}
        loading={loginLoading}
        error={loginError || undefined}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Logo size="md" showText={true} />
              </Link>
              <div className="hidden md:block">
                <h1 className="text-xl font-semibold text-gray-900">Dashboard RH</h1>
                {process.env.NODE_ENV === 'development' && (
                  <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                    Mode Développement
                  </span>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <Bell size={20} />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <Settings size={20} />
              </button>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">RH</span>
                </div>
                <span className="hidden md:block text-sm font-medium text-gray-700">Admin RH</span>
              </div>
              <button
                onClick={handleLogout}
                className="p-2 text-gray-400 hover:text-red-600 transition-colors duration-200"
                title="Se déconnecter"
              >
                <LogOut size={20} />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Indicateur de chargement */}
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Chargement des données...</p>
          </div>
        )}

        {/* Gestion d'erreur */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex">
              <XCircle className="text-red-400 mr-3" size={20} />
              <div>
                <h3 className="text-red-800 font-medium">Erreur de chargement</h3>
                <p className="text-red-600 text-sm mt-1">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="text-red-600 hover:text-red-800 text-sm underline mt-2"
                >
                  Réessayer
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Contenu principal */}
        {!error && (
          <>
        {/* Navigation tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Vue d\'ensemble', icon: TrendingUp },
              { id: 'applications', label: 'Candidatures', icon: Users },
              { id: 'interviews', label: 'Entretiens', icon: Calendar },
              { id: 'members', label: 'Membres', icon: CheckCircle }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'bg-primary-100 text-primary-700 border-b-2 border-primary-600'
                    : 'text-gray-600 hover:text-primary-600 hover:bg-gray-100'
                }`}
              >
                <tab.icon size={18} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Vue d'ensemble */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Statistiques */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Candidats</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {loading ? (
                        <span className="inline-block animate-pulse bg-gray-200 rounded w-16 h-8"></span>
                      ) : (
                        stats.totalCandidates
                      )}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Users className="text-primary-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">En attente</p>
                    <p className="text-3xl font-bold text-yellow-600">{stats.pendingApplications}</p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <Clock className="text-yellow-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Entretiens prévus</p>
                    <p className="text-3xl font-bold text-blue-600">{stats.scheduledInterviews}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Calendar className="text-blue-600" size={24} />
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Membres actifs</p>
                    <p className="text-3xl font-bold text-green-600">{stats.activeMembers}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <CheckCircle className="text-green-600" size={24} />
                  </div>
                </div>
              </div>
            </div>

            {/* Candidatures récentes */}
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Candidatures récentes</h2>
                <Link
                  href="/dashboard/applications"
                  className="text-primary-600 hover:text-primary-700 font-medium text-sm"
                >
                  Voir tout
                </Link>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Candidat
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Organisation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentApplications.map((application: RecentApplication) => (
                      <tr key={application.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{application.name}</div>
                            <div className="text-sm text-gray-500">{application.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            {getTypeIcon(application.type)}
                            <span className="text-sm text-gray-900">{application.type}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {application.organization || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(application.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(application.date).toLocaleDateString('fr-FR')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Entretiens à venir */}
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Entretiens à venir</h2>
                <button className="karma-button-primary flex items-center space-x-2">
                  <Plus size={16} />
                  <span>Planifier</span>
                </button>
              </div>
              
              <div className="space-y-4">
                {upcomingInterviews.map((interview) => (
                  <div key={interview.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <Calendar className="text-primary-600" size={20} />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{interview.candidateName}</h3>
                        <p className="text-sm text-gray-500">
                          {interview.type} • {new Date(interview.date).toLocaleDateString('fr-FR')} à {interview.time}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(interview.status)}
                      <button className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                        Détails
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Onglet Candidatures */}
        {activeTab === 'applications' && (
          <div className="space-y-6">
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Gestion des Candidatures</h2>
                <div className="flex items-center space-x-3">
                  <select
                    value={selectedCandidate}
                    onChange={(e) => setSelectedCandidate(e.target.value)}
                    className="karma-input"
                  >
                    <option value="">Filtrer par statut</option>
                    <option value="pending">En attente</option>
                    <option value="approved">Approuvé</option>
                    <option value="rejected">Rejeté</option>
                  </select>
                  <button className="karma-button-primary flex items-center space-x-2">
                    <Download size={16} />
                    <span>Exporter</span>
                  </button>
                </div>
              </div>

              {/* Tableau des candidatures */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Candidat
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Organisation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentApplications.map((application: RecentApplication) => (
                      <tr key={application.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{application.name}</div>
                            <div className="text-sm text-gray-500">{application.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            {getTypeIcon(application.type)}
                            <span className="text-sm text-gray-900">{application.type}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {application.organization || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            value={application.status}
                            onChange={(e) => handleUpdateCandidateStatus(application.id, e.target.value)}
                            className="text-sm border-0 bg-transparent focus:ring-0 font-medium"
                          >
                            <option value="pending">En attente</option>
                            <option value="approved">Approuvé</option>
                            <option value="rejected">Rejeté</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(application.date).toLocaleDateString('fr-FR')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {
                              setInterviewFormData(prev => ({
                                ...prev,
                                candidateId: application.id,
                                title: `Entretien - ${application.name}`
                              }))
                              setShowInterviewForm(true)
                            }}
                            className="text-primary-600 hover:text-primary-900 mr-3"
                          >
                            Planifier entretien
                          </button>
                          <button className="text-gray-600 hover:text-gray-900">
                            Voir détails
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Onglet Entretiens */}
        {activeTab === 'interviews' && (
          <div className="space-y-6">
            {/* Message de succès */}
            {successMessage && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex">
                  <CheckCircle className="text-green-400 mr-3" size={20} />
                  <div>
                    <h3 className="text-green-800 font-medium">Succès</h3>
                    <p className="text-green-600 text-sm mt-1">{successMessage}</p>
                  </div>
                </div>
              </div>
            )}
            {/* Formulaire de création d'entretien */}
            {showInterviewForm && (
              <div className="dashboard-card">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Planifier un Entretien</h3>
                  <button
                    onClick={() => setShowInterviewForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XCircle size={20} />
                  </button>
                </div>

                <form onSubmit={(e) => {
                  e.preventDefault()
                  handleCreateInterview(interviewFormData)
                }} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="karma-label">Titre de l'entretien</label>
                      <input
                        type="text"
                        value={interviewFormData.title}
                        onChange={(e) => setInterviewFormData(prev => ({
                          ...prev,
                          title: e.target.value
                        }))}
                        className="karma-input"
                        placeholder="Ex: Entretien découverte"
                        required
                      />
                    </div>

                    <div>
                      <label className="karma-label">Type d'entretien</label>
                      <select
                        value={interviewFormData.type}
                        onChange={(e) => setInterviewFormData(prev => ({
                          ...prev,
                          type: e.target.value as 'DISCOVERY' | 'FOLLOW_UP' | 'FINAL'
                        }))}
                        className="karma-input"
                        required
                      >
                        <option value="DISCOVERY">Découverte</option>
                        <option value="FOLLOW_UP">Suivi</option>
                        <option value="FINAL">Final</option>
                      </select>
                    </div>

                    <div>
                      <label className="karma-label">Candidat</label>
                      <select
                        value={interviewFormData.candidateId}
                        onChange={(e) => setInterviewFormData(prev => ({
                          ...prev,
                          candidateId: e.target.value
                        }))}
                        className="karma-input"
                        required
                      >
                        <option value="">Sélectionner un candidat</option>
                        {recentApplications.map((candidate: RecentApplication) => (
                          <option key={candidate.id} value={candidate.id}>
                            {candidate.name} - {candidate.type}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="karma-label">Date et heure</label>
                      <input
                        type="datetime-local"
                        value={interviewFormData.scheduledAt}
                        onChange={(e) => setInterviewFormData(prev => ({
                          ...prev,
                          scheduledAt: e.target.value
                        }))}
                        className="karma-input"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="karma-label">Description</label>
                    <textarea
                      value={interviewFormData.description}
                      onChange={(e) => setInterviewFormData(prev => ({
                        ...prev,
                        description: e.target.value
                      }))}
                      className="karma-input"
                      rows={3}
                      placeholder="Objectifs et points à aborder lors de l'entretien..."
                    />
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowInterviewForm(false)}
                      className="karma-button-outline"
                    >
                      Annuler
                    </button>
                    <button
                      type="submit"
                      className="karma-button-primary"
                    >
                      Planifier l'entretien
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Liste des entretiens */}
            <div className="dashboard-card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Entretiens Planifiés
                  <span className="ml-2 text-sm text-gray-500">({interviews.length})</span>
                </h2>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={loadInterviews}
                    disabled={interviewsLoading}
                    className="karma-button-outline flex items-center space-x-2"
                  >
                    <Clock size={16} />
                    <span>Actualiser</span>
                  </button>
                  <button
                    onClick={() => setShowInterviewForm(true)}
                    className="karma-button-primary flex items-center space-x-2"
                  >
                    <Plus size={16} />
                    <span>Nouvel entretien</span>
                  </button>
                </div>
              </div>

              <div className="space-y-4">
                {interviewsLoading && (
                  <div className="text-center py-8">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <p className="mt-2 text-gray-600">Chargement des entretiens...</p>
                  </div>
                )}

                {!interviewsLoading && interviews.map((interview: Interview) => (
                  <div key={interview.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                          <Calendar className="text-primary-600" size={20} />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{interview.candidateName}</h3>
                          <p className="text-sm text-gray-600">
                            {interview.type} • {new Date(interview.scheduledAt).toLocaleDateString('fr-FR')} à {new Date(interview.scheduledAt).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                          </p>
                          <div className="mt-1">
                            {getStatusBadge(interview.status)}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button className="karma-button-outline text-sm">
                          Modifier
                        </button>
                        <button className="karma-button-primary text-sm">
                          Démarrer
                        </button>
                      </div>
                    </div>
                  </div>
                ))}

                {!interviewsLoading && interviews.length === 0 && (
                  <div className="text-center py-12">
                    <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun entretien planifié</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Commencez par planifier votre premier entretien.
                    </p>
                    <div className="mt-6">
                      <button
                        onClick={() => setShowInterviewForm(true)}
                        className="karma-button-primary"
                      >
                        Planifier un entretien
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Onglet Membres */}
        {activeTab === 'members' && (
          <div className="dashboard-card text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="text-gray-400" size={32} />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Gestion des Membres
            </h3>
            <p className="text-gray-500">
              Cette section sera bientôt disponible.
            </p>
          </div>
        )}
        </>
        )}
      </div>
    </div>
  )
}
