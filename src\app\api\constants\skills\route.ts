import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const skills = await prisma.skill.findMany({
      where: { isActive: true },
      orderBy: { order: 'asc' },
      select: {
        id: true,
        name: true,
        category: true
      }
    })

    // Grouper par catégorie
    const skillsByCategory = skills.reduce((acc, skill) => {
      const category = skill.category || 'Autre'
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push({
        id: skill.id,
        name: skill.name
      })
      return acc
    }, {} as Record<string, Array<{id: string, name: string}>>)

    return NextResponse.json({
      skills,
      skillsByCategory
    })
  } catch (error) {
    console.error('Erreur lors de la récupération des compétences:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
