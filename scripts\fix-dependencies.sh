#!/bin/bash

# Script pour corriger les dépendances Next.js
echo "🔧 Correction des Dépendances - Karma Com Solidarité"
echo "=================================================="

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
log "Vérification des prérequis..."

if [ ! -f "package.json" ]; then
    error "Ce script doit être exécuté depuis la racine du projet"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    error "npm n'est pas installé"
    exit 1
fi

success "Prérequis validés"

# Sauvegarde du package-lock.json
log "Sauvegarde du package-lock.json..."
if [ -f "package-lock.json" ]; then
    cp package-lock.json package-lock.json.backup
    success "Sauvegarde créée"
fi

# Nettoyage du cache npm
log "Nettoyage du cache npm..."
npm cache clean --force
success "Cache npm nettoyé"

# Suppression de node_modules
log "Suppression de node_modules..."
rm -rf node_modules
success "node_modules supprimé"

# Suppression du package-lock.json
log "Suppression du package-lock.json..."
rm -f package-lock.json
success "package-lock.json supprimé"

# Installation des dépendances avec options de compatibilité
log "Installation des dépendances avec options de compatibilité..."
if npm install --legacy-peer-deps; then
    success "Installation réussie avec --legacy-peer-deps"
else
    warning "Échec avec --legacy-peer-deps, essai avec --force..."
    if npm install --force; then
        success "Installation réussie avec --force"
    else
        error "Échec de l'installation des dépendances"
        
        # Restaurer la sauvegarde si elle existe
        if [ -f "package-lock.json.backup" ]; then
            log "Restauration de la sauvegarde..."
            cp package-lock.json.backup package-lock.json
            npm ci
        fi
        exit 1
    fi
fi

# Vérification des versions
log "Vérification des versions..."
NEXT_VERSION=$(npm list next --depth=0 2>/dev/null | grep next@ | awk -F@ '{print $2}')
echo "Version Next.js installée: $NEXT_VERSION"

# Test de build local
log "Test de build local..."
export DATABASE_URL="***********************************/dummy"
export NODE_ENV="production"
export NEXT_TELEMETRY_DISABLED=1

if npm run build; then
    success "Build local réussi"
else
    warning "Build local échoué, mais les dépendances sont corrigées"
    echo "Le build peut échouer à cause de la base de données factice"
fi

# Génération Prisma
log "Génération du client Prisma..."
if npx prisma generate; then
    success "Client Prisma généré"
else
    warning "Échec de la génération Prisma (normal sans base de données)"
fi

# Nettoyage
log "Nettoyage..."
rm -f package-lock.json.backup

# Résumé
echo ""
log "Résumé de la correction"
echo "======================="

echo ""
echo "✅ Actions effectuées:"
echo "   - Cache npm nettoyé"
echo "   - node_modules supprimé et réinstallé"
echo "   - Dépendances mises à jour avec options de compatibilité"
echo "   - Versions Next.js synchronisées"
echo ""
echo "📊 Versions installées:"
npm list next react react-dom --depth=0 2>/dev/null || echo "Versions à vérifier manuellement"
echo ""
echo "🚀 Prochaines étapes:"
echo "   1. Tester le build local: npm run build"
echo "   2. Tester le déploiement: npm run deploy:vps-no-sudo"
echo ""

success "Correction des dépendances terminée"
