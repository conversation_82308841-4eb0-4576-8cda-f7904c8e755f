import React, { useState } from 'react'
import { Calendar, Clock, Users, History, Plus, Search, Filter } from 'lucide-react'
import InterviewPlanning from './InterviewPlanning'
import InterviewNotifications from './InterviewNotifications'
import InterviewH<PERSON><PERSON> from './InterviewHistory'
import { Interview } from '../../utils/constants'

interface InterviewsTabProps {
  interviews: Interview[]
  loading: boolean
  onCreateInterview: (formData: any) => Promise<void>
  onUpdateInterviewStatus: (interviewId: string, newStatus: string) => Promise<void>
  onEditInterview: (interview: Interview) => void
  onStartInterview: (interview: Interview) => void
  candidates: any[]
}

const InterviewsTab: React.FC<InterviewsTabProps> = ({
  interviews,
  loading,
  onCreateInterview,
  onUpdateInterviewStatus,
  onEditInterview,
  onStartInterview,
  candidates
}) => {
  const [activeSection, setActiveSection] = useState<'planning' | 'notifications' | 'history'>('planning')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')

  // Filtrer les entretiens selon les critères
  const filteredInterviews = interviews.filter(interview => {
    const matchesSearch = interview.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         interview.candidateName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         interview.candidateEmail.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || interview.status === statusFilter
    const matchesType = typeFilter === 'all' || interview.type === typeFilter

    return matchesSearch && matchesStatus && matchesType
  })

  // Statistiques des entretiens
  const stats = {
    total: interviews.length,
    scheduled: interviews.filter(i => i.status === 'SCHEDULED').length,
    confirmed: interviews.filter(i => i.status === 'CONFIRMED').length,
    completed: interviews.filter(i => i.status === 'COMPLETED').length,
    cancelled: interviews.filter(i => i.status === 'CANCELLED').length
  }

  const sections = [
    {
      id: 'planning',
      label: 'Planification des rendez-vous',
      icon: Calendar,
      description: 'Planifier et gérer les entretiens avec les membres'
    },
    {
      id: 'notifications',
      label: 'Notifications de rappel',
      icon: Clock,
      description: 'Gérer les rappels automatiques par email'
    },
    {
      id: 'history',
      label: 'Historique des rendez-vous',
      icon: History,
      description: 'Consulter l\'historique de tous les entretiens'
    }
  ]

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="dashboard-card bg-gradient-to-br from-white to-blue-50 border-l-4 border-l-karma-blue">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-karma-blue flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-karma-blue to-karma-pink rounded-full flex items-center justify-center mr-3">
                <Calendar className="text-white" size={20} />
              </div>
              Gestion des Entretiens
            </h2>
            <p className="text-gray-600 mt-1 ml-13">Planification, notifications et historique des rendez-vous</p>
          </div>
          <div className="flex items-center space-x-6">
            <div className="text-center p-3 bg-white rounded-lg shadow-sm border border-karma-blue/20">
              <div className="text-2xl font-bold text-karma-blue">{stats.total}</div>
              <div className="text-xs text-gray-500 font-medium">Total</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg shadow-sm border border-yellow-200">
              <div className="text-2xl font-bold text-yellow-600">{stats.scheduled}</div>
              <div className="text-xs text-gray-500 font-medium">Programmés</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg shadow-sm border border-green-200">
              <div className="text-2xl font-bold text-green-600">{stats.confirmed}</div>
              <div className="text-xs text-gray-500 font-medium">Confirmés</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg shadow-sm border border-karma-pink/20">
              <div className="text-2xl font-bold text-karma-pink">{stats.completed}</div>
              <div className="text-xs text-gray-500 font-medium">Terminés</div>
            </div>
          </div>
        </div>

        {/* Navigation des sections */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {sections.map((section) => {
              const IconComponent = section.icon
              const isActive = activeSection === section.id

              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id as any)}
                  className={`
                    flex items-center space-x-2 py-4 px-3 border-b-2 font-medium text-sm transition-all duration-200 rounded-t-lg
                    ${isActive
                      ? 'border-karma-pink text-karma-pink bg-gradient-to-t from-pink-50 to-transparent'
                      : 'border-transparent text-gray-500 hover:text-karma-blue hover:border-karma-blue hover:bg-gradient-to-t hover:from-blue-50 hover:to-transparent'
                    }
                  `}
                >
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    isActive
                      ? 'bg-gradient-to-r from-karma-blue to-karma-pink text-white'
                      : 'bg-gray-100 text-gray-500'
                  }`}>
                    <IconComponent size={14} />
                  </div>
                  <span>{section.label}</span>
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="dashboard-card bg-gradient-to-r from-gray-50 to-blue-50 border border-karma-blue/20">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-karma-pink" size={20} />
              <input
                type="text"
                placeholder="Rechercher par nom, email ou titre..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-karma-pink focus:border-karma-pink bg-white shadow-sm transition-all duration-200"
              />
            </div>
          </div>
          
          <div className="flex gap-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-karma-pink focus:border-karma-pink bg-white shadow-sm transition-all duration-200"
            >
              <option value="all">Tous les statuts</option>
              <option value="SCHEDULED">Programmé</option>
              <option value="CONFIRMED">Confirmé</option>
              <option value="COMPLETED">Terminé</option>
              <option value="CANCELLED">Annulé</option>
            </select>

            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-karma-pink focus:border-karma-pink bg-white shadow-sm transition-all duration-200"
            >
              <option value="all">Tous les types</option>
              <option value="DISCOVERY">Découverte</option>
              <option value="INTEGRATION">Intégration</option>
              <option value="FOLLOW_UP">Suivi</option>
              <option value="INTERVIEW">Entretien</option>
            </select>
          </div>
        </div>

        {filteredInterviews.length !== interviews.length && (
          <div className="mt-4 p-3 bg-karma-blue/10 rounded-lg border border-karma-blue/20">
            <div className="flex items-center text-sm text-karma-blue">
              <Filter className="mr-2" size={16} />
              <span className="font-medium">
                {filteredInterviews.length} entretien(s) trouvé(s) sur {interviews.length}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Contenu des sections */}
      {activeSection === 'planning' && (
        <InterviewPlanning
          interviews={filteredInterviews}
          loading={loading}
          onCreateInterview={onCreateInterview}
          onUpdateInterviewStatus={onUpdateInterviewStatus}
          onEditInterview={onEditInterview}
          onStartInterview={onStartInterview}
          candidates={candidates}
        />
      )}

      {activeSection === 'notifications' && (
        <InterviewNotifications
          interviews={filteredInterviews}
          loading={loading}
        />
      )}

      {activeSection === 'history' && (
        <InterviewHistory
          interviews={filteredInterviews}
          loading={loading}
        />
      )}
    </div>
  )
}

export default InterviewsTab
