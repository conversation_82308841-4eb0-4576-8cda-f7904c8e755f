#!/bin/bash

# Script de test de connexion et permissions VPS
echo "🔍 Test Connexion VPS - Karma Com Solidarité"
echo "==========================================="

# Configuration
VPS_IP="*************"
VPS_USER="vpsadmin"
DEPLOY_DIR="/home/<USER>/kcs"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test de connexion SSH
log "Test de connexion SSH..."
if ssh -o ConnectTimeout=10 -o BatchMode=yes $VPS_USER@$VPS_IP exit 2>/dev/null; then
    success "Connexion SSH réussie"
else
    error "Impossible de se connecter au VPS"
    echo "Vérifiez :"
    echo "  - Votre clé SSH est configurée"
    echo "  - Le VPS est accessible"
    echo "  - L'utilisateur $VPS_USER existe"
    exit 1
fi

# Test des permissions utilisateur
log "Test des permissions utilisateur..."
USER_INFO=$(ssh $VPS_USER@$VPS_IP "whoami && id" 2>/dev/null)
if [ $? -eq 0 ]; then
    success "Utilisateur connecté:"
    echo "$USER_INFO"
else
    error "Impossible de récupérer les informations utilisateur"
fi

# Test de création de répertoire
log "Test de création du répertoire de déploiement..."
ssh $VPS_USER@$VPS_IP << 'EOF'
    # Créer le répertoire de test
    mkdir -p /home/<USER>/kcs-test
    
    # Tester les permissions d'écriture
    echo "test" > /home/<USER>/kcs-test/test.txt
    
    # Vérifier que le fichier a été créé
    if [ -f /home/<USER>/kcs-test/test.txt ]; then
        echo "✅ Permissions d'écriture OK"
    else
        echo "❌ Problème de permissions d'écriture"
        exit 1
    fi
    
    # Nettoyer
    rm -rf /home/<USER>/kcs-test
    
    echo "✅ Test de répertoire réussi"
EOF

if [ $? -eq 0 ]; then
    success "Permissions de répertoire OK"
else
    error "Problème avec les permissions de répertoire"
fi

# Test de copie SCP
log "Test de copie SCP..."

# Créer un fichier de test temporaire
TEST_FILE=$(mktemp)
echo "Test SCP $(date)" > "$TEST_FILE"

# Tester SCP
if scp "$TEST_FILE" $VPS_USER@$VPS_IP:/home/<USER>/ 2>/dev/null; then
    success "SCP fonctionne"
    
    # Vérifier que le fichier a été copié
    REMOTE_FILE=$(basename "$TEST_FILE")
    if ssh $VPS_USER@$VPS_IP "test -f /home/<USER>/$REMOTE_FILE"; then
        success "Fichier copié avec succès"
        
        # Nettoyer le fichier distant
        ssh $VPS_USER@$VPS_IP "rm -f /home/<USER>/$REMOTE_FILE"
    else
        warning "Fichier non trouvé après copie"
    fi
else
    error "Échec de la copie SCP"
fi

# Nettoyer le fichier local
rm -f "$TEST_FILE"

# Test des services Docker
log "Test des services Docker sur le VPS..."
DOCKER_STATUS=$(ssh $VPS_USER@$VPS_IP "docker --version 2>/dev/null" || echo "Docker non disponible")
echo "Docker: $DOCKER_STATUS"

COMPOSE_STATUS=$(ssh $VPS_USER@$VPS_IP "docker-compose --version 2>/dev/null" || echo "Docker Compose non disponible")
echo "Docker Compose: $COMPOSE_STATUS"

# Test de Nginx
log "Test de Nginx sur le VPS..."
NGINX_STATUS=$(ssh $VPS_USER@$VPS_IP "sudo nginx -t 2>/dev/null && echo 'Nginx OK'" || echo "Nginx non configuré")
echo "Nginx: $NGINX_STATUS"

# Test de l'espace disque
log "Vérification de l'espace disque..."
DISK_INFO=$(ssh $VPS_USER@$VPS_IP "df -h /home" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "Espace disque /home:"
    echo "$DISK_INFO"
else
    warning "Impossible de vérifier l'espace disque"
fi

# Test de création du répertoire final
log "Test de création du répertoire de déploiement final..."
ssh $VPS_USER@$VPS_IP << EOF
    # Créer le répertoire de déploiement
    mkdir -p $DEPLOY_DIR/{backups,logs,ssl}
    
    # Vérifier la structure
    if [ -d "$DEPLOY_DIR" ]; then
        echo "✅ Répertoire $DEPLOY_DIR créé"
        ls -la $DEPLOY_DIR
    else
        echo "❌ Échec de création du répertoire $DEPLOY_DIR"
        exit 1
    fi
    
    # Tester les permissions
    touch $DEPLOY_DIR/test-permissions.txt
    if [ -f "$DEPLOY_DIR/test-permissions.txt" ]; then
        echo "✅ Permissions d'écriture OK dans $DEPLOY_DIR"
        rm -f $DEPLOY_DIR/test-permissions.txt
    else
        echo "❌ Problème de permissions dans $DEPLOY_DIR"
        exit 1
    fi
EOF

if [ $? -eq 0 ]; then
    success "Répertoire de déploiement prêt"
else
    error "Problème avec le répertoire de déploiement"
fi

# Résumé
echo ""
log "Résumé du test de connexion VPS"
echo "==============================="

echo ""
echo "📊 Informations VPS:"
echo "   IP: $VPS_IP"
echo "   Utilisateur: $VPS_USER"
echo "   Répertoire: $DEPLOY_DIR"
echo ""
echo "✅ Tests réussis:"
echo "   - Connexion SSH"
echo "   - Permissions utilisateur"
echo "   - Création de répertoires"
echo "   - Copie SCP"
echo "   - Répertoire de déploiement"
echo ""
echo "🚀 Le VPS est prêt pour le déploiement!"
echo ""
echo "Commande de déploiement:"
echo "   npm run deploy:vps"

success "Test de connexion VPS terminé avec succès"
