import React, { useState } from 'react'
import { History, Calendar, User, FileText, Download, Eye, Filter, TrendingUp } from 'lucide-react'
import { getStatusBadge, getInterviewTypeLabel } from '../../utils/statusHelpers'
import { formatDateFR, formatTimeFR, sortByDate } from '../../utils/dateHelpers'
import { Interview } from '../../utils/constants'

interface InterviewHistoryProps {
  interviews: Interview[]
  loading: boolean
}

const InterviewHistory: React.FC<InterviewHistoryProps> = ({
  interviews,
  loading
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedType, setSelectedType] = useState('all')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showDetails, setShowDetails] = useState<string | null>(null)

  // Filtrer les entretiens selon la période
  const getFilteredInterviews = () => {
    let filtered = [...interviews]

    // Filtre par période
    if (selectedPeriod !== 'all') {
      const now = new Date()
      let startDate = new Date()

      switch (selectedPeriod) {
        case 'week':
          startDate.setDate(now.getDate() - 7)
          break
        case 'month':
          startDate.setMonth(now.getMonth() - 1)
          break
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3)
          break
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1)
          break
      }

      filtered = filtered.filter(interview => 
        new Date(interview.scheduledAt) >= startDate
      )
    }

    // Filtre par statut
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(interview => interview.status === selectedStatus)
    }

    // Filtre par type
    if (selectedType !== 'all') {
      filtered = filtered.filter(interview => interview.type === selectedType)
    }

    // Tri par date
    return filtered.sort((a, b) => 
      sortByDate(a.scheduledAt, b.scheduledAt, sortOrder === 'asc')
    )
  }

  const filteredInterviews = getFilteredInterviews()

  // Statistiques de l'historique
  const stats = {
    total: interviews.length,
    completed: interviews.filter(i => i.status === 'COMPLETED').length,
    cancelled: interviews.filter(i => i.status === 'CANCELLED').length,
    thisMonth: interviews.filter(i => {
      const interviewDate = new Date(i.scheduledAt)
      const now = new Date()
      return interviewDate.getMonth() === now.getMonth() && 
             interviewDate.getFullYear() === now.getFullYear()
    }).length
  }

  // Statistiques par type
  const typeStats = interviews.reduce((acc, interview) => {
    acc[interview.type] = (acc[interview.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Statistiques par mois (derniers 6 mois)
  const monthlyStats = Array.from({ length: 6 }, (_, i) => {
    const date = new Date()
    date.setMonth(date.getMonth() - i)
    const monthInterviews = interviews.filter(interview => {
      const interviewDate = new Date(interview.scheduledAt)
      return interviewDate.getMonth() === date.getMonth() && 
             interviewDate.getFullYear() === date.getFullYear()
    })
    return {
      month: date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' }),
      count: monthInterviews.length,
      completed: monthInterviews.filter(i => i.status === 'COMPLETED').length
    }
  }).reverse()

  const handleExportHistory = () => {
    // Simulation d'export
    const csvContent = [
      'Date,Heure,Titre,Candidat,Email,Type,Statut,Description',
      ...filteredInterviews.map(interview => [
        formatDateFR(interview.scheduledAt),
        formatTimeFR(interview.scheduledAt),
        interview.title,
        interview.candidateName,
        interview.candidateEmail,
        getInterviewTypeLabel(interview.type),
        interview.status,
        interview.description || ''
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `historique-entretiens-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      {/* Statistiques générales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="dashboard-card text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
          <div className="text-sm text-gray-600">Total entretiens</div>
        </div>
        <div className="dashboard-card text-center">
          <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          <div className="text-sm text-gray-600">Terminés</div>
        </div>
        <div className="dashboard-card text-center">
          <div className="text-2xl font-bold text-red-600">{stats.cancelled}</div>
          <div className="text-sm text-gray-600">Annulés</div>
        </div>
        <div className="dashboard-card text-center">
          <div className="text-2xl font-bold text-karma-pink">{stats.thisMonth}</div>
          <div className="text-sm text-gray-600">Ce mois</div>
        </div>
      </div>

      {/* Graphique mensuel */}
      <div className="dashboard-card">
        <div className="flex items-center mb-6">
          <TrendingUp className="mr-2 text-karma-pink" size={20} />
          <h3 className="text-lg font-semibold text-gray-900">Évolution mensuelle</h3>
        </div>
        
        <div className="grid grid-cols-6 gap-4">
          {monthlyStats.map((month, index) => (
            <div key={index} className="text-center">
              <div className="mb-2">
                <div 
                  className="bg-karma-pink rounded-t"
                  style={{ 
                    height: `${Math.max(month.count * 10, 10)}px`,
                    maxHeight: '100px'
                  }}
                ></div>
                <div 
                  className="bg-green-500 rounded-b"
                  style={{ 
                    height: `${Math.max(month.completed * 8, 5)}px`,
                    maxHeight: '80px'
                  }}
                ></div>
              </div>
              <div className="text-xs text-gray-600">{month.month}</div>
              <div className="text-sm font-medium">{month.count}</div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 flex justify-center space-x-6 text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-karma-pink rounded mr-2"></div>
            <span>Total</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
            <span>Terminés</span>
          </div>
        </div>
      </div>

      {/* Filtres et contrôles */}
      <div className="dashboard-card">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex items-center">
            <History className="mr-2 text-karma-pink" size={20} />
            <h3 className="text-lg font-semibold text-gray-900">
              Historique des entretiens
              <span className="ml-2 text-sm text-gray-500">({filteredInterviews.length})</span>
            </h3>
          </div>
          
          <button
            onClick={handleExportHistory}
            className="btn-secondary flex items-center space-x-2"
          >
            <Download size={16} />
            <span>Exporter</span>
          </button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-karma-pink focus:border-karma-pink"
          >
            <option value="all">Toutes les périodes</option>
            <option value="week">7 derniers jours</option>
            <option value="month">Dernier mois</option>
            <option value="quarter">3 derniers mois</option>
            <option value="year">Dernière année</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-karma-pink focus:border-karma-pink"
          >
            <option value="all">Tous les statuts</option>
            <option value="SCHEDULED">Programmé</option>
            <option value="CONFIRMED">Confirmé</option>
            <option value="COMPLETED">Terminé</option>
            <option value="CANCELLED">Annulé</option>
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-karma-pink focus:border-karma-pink"
          >
            <option value="all">Tous les types</option>
            <option value="DISCOVERY">Découverte</option>
            <option value="INTEGRATION">Intégration</option>
            <option value="FOLLOW_UP">Suivi</option>
            <option value="INTERVIEW">Entretien</option>
          </select>

          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-karma-pink focus:border-karma-pink"
          >
            <option value="desc">Plus récent d'abord</option>
            <option value="asc">Plus ancien d'abord</option>
          </select>
        </div>
      </div>

      {/* Liste des entretiens */}
      <div className="dashboard-card">
        {filteredInterviews.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Entretien
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Candidat
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Date & Heure
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredInterviews.map((interview) => (
                  <React.Fragment key={interview.id}>
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{interview.title}</div>
                        {interview.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {interview.description}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-gradient-to-r from-karma-blue to-karma-pink rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-xs font-medium">
                              {interview.candidateName.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <div className="text-sm text-gray-900">{interview.candidateName}</div>
                            <div className="text-sm text-gray-500">{interview.candidateEmail}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{formatDateFR(interview.scheduledAt)}</div>
                        <div className="text-gray-500">{formatTimeFR(interview.scheduledAt)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {getInterviewTypeLabel(interview.type)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(interview.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => setShowDetails(showDetails === interview.id ? null : interview.id)}
                          className="text-karma-pink hover:text-karma-pink-dark flex items-center"
                        >
                          <Eye size={16} className="mr-1" />
                          Détails
                        </button>
                      </td>
                    </tr>
                    
                    {/* Détails expandables */}
                    {showDetails === interview.id && (
                      <tr>
                        <td colSpan={6} className="px-6 py-4 bg-gray-50">
                          <div className="space-y-3">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <h4 className="font-medium text-gray-900 mb-2">Informations générales</h4>
                                <div className="space-y-1 text-sm">
                                  <div><span className="font-medium">Créé le:</span> {formatDateFR(interview.createdAt)}</div>
                                  <div><span className="font-medium">Type:</span> {getInterviewTypeLabel(interview.type)}</div>
                                  <div><span className="font-medium">Statut:</span> {interview.status}</div>
                                </div>
                              </div>
                              
                              {interview.description && (
                                <div>
                                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                                  <p className="text-sm text-gray-600">{interview.description}</p>
                                </div>
                              )}
                            </div>
                            
                            {interview.notes && (
                              <div>
                                <h4 className="font-medium text-gray-900 mb-2">Notes</h4>
                                <p className="text-sm text-gray-600">{interview.notes}</p>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <History size={48} className="mx-auto mb-4 text-gray-300" />
            <p>Aucun entretien trouvé pour les critères sélectionnés</p>
          </div>
        )}
      </div>

      {/* Statistiques par type */}
      <div className="dashboard-card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition par type d'entretien</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(typeStats).map(([type, count]) => (
            <div key={type} className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-xl font-bold text-karma-pink">{count}</div>
              <div className="text-sm text-gray-600">{getInterviewTypeLabel(type)}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default InterviewHistory
