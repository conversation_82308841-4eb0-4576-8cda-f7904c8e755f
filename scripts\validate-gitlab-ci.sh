#!/bin/bash

# Script de validation de la configuration GitLab CI/CD
echo "🔍 Validation GitLab CI/CD - Karma Com Solidarité"
echo "================================================="

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des fichiers requis
print_status "Vérification des fichiers de configuration..."

required_files=(
    ".gitlab-ci.yml"
    "docker-compose.staging.yml"
    "docker-compose.production.yml"
    "Dockerfile.simple"
    "scripts/deploy-staging.sh"
    "scripts/deploy-production.sh"
    "nginx/staging.conf"
    "nginx/production.conf"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "$file présent"
    else
        print_error "$file manquant"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    print_error "Fichiers manquants: ${missing_files[*]}"
    exit 1
fi

# Validation du fichier .gitlab-ci.yml
print_status "Validation du fichier .gitlab-ci.yml..."

if command -v gitlab-ci-multi-runner &> /dev/null; then
    if gitlab-ci-multi-runner verify; then
        print_success "Configuration GitLab CI valide"
    else
        print_warning "Impossible de valider la configuration (runner non configuré)"
    fi
else
    print_warning "gitlab-ci-multi-runner non installé, validation manuelle"
fi

# Vérification de la syntaxe YAML
if command -v yamllint &> /dev/null; then
    if yamllint .gitlab-ci.yml; then
        print_success "Syntaxe YAML valide"
    else
        print_error "Erreur de syntaxe YAML"
    fi
else
    print_warning "yamllint non installé, vérification manuelle de la syntaxe"
fi

# Vérification des stages
print_status "Vérification des stages du pipeline..."
stages=("test" "build" "deploy" "cleanup")
for stage in "${stages[@]}"; do
    if grep -q "stage: $stage" .gitlab-ci.yml; then
        print_success "Stage '$stage' configuré"
    else
        print_warning "Stage '$stage' non trouvé"
    fi
done

# Vérification des jobs critiques
print_status "Vérification des jobs critiques..."
critical_jobs=("lint" "test" "build_docker" "deploy_staging" "deploy_production")
for job in "${critical_jobs[@]}"; do
    if grep -q "^$job:" .gitlab-ci.yml; then
        print_success "Job '$job' configuré"
    else
        print_error "Job '$job' manquant"
    fi
done

# Vérification des variables d'environnement
print_status "Vérification des variables d'environnement..."
required_vars=(
    "STAGING_HOST"
    "STAGING_USER"
    "STAGING_SSH_PRIVATE_KEY"
    "PRODUCTION_HOST"
    "PRODUCTION_USER"
    "PRODUCTION_SSH_PRIVATE_KEY"
    "POSTGRES_PASSWORD_STAGING"
    "POSTGRES_PASSWORD_PROD"
    "NEXTAUTH_SECRET_STAGING"
    "NEXTAUTH_SECRET_PROD"
)

print_warning "Variables à configurer dans GitLab:"
for var in "${required_vars[@]}"; do
    echo "   - $var"
done

# Vérification des Dockerfiles
print_status "Vérification des Dockerfiles..."

if [ -f "Dockerfile.simple" ]; then
    if docker build -f Dockerfile.simple -t karma-ci-test . &> /dev/null; then
        print_success "Dockerfile.simple valide"
        docker rmi karma-ci-test &> /dev/null
    else
        print_error "Dockerfile.simple invalide"
    fi
else
    print_error "Dockerfile.simple manquant"
fi

# Vérification des configurations Docker Compose
print_status "Vérification des configurations Docker Compose..."

compose_files=("docker-compose.staging.yml" "docker-compose.production.yml")
for compose_file in "${compose_files[@]}"; do
    if command -v docker-compose &> /dev/null; then
        if docker-compose -f "$compose_file" config &> /dev/null; then
            print_success "$compose_file valide"
        else
            print_error "$compose_file invalide"
        fi
    else
        print_warning "docker-compose non installé, validation manuelle"
    fi
done

# Vérification des scripts de déploiement
print_status "Vérification des scripts de déploiement..."

deploy_scripts=("scripts/deploy-staging.sh" "scripts/deploy-production.sh")
for script in "${deploy_scripts[@]}"; do
    if [ -x "$script" ]; then
        print_success "$script exécutable"
    else
        print_warning "$script non exécutable"
        chmod +x "$script"
        print_success "$script rendu exécutable"
    fi
    
    # Vérification de la syntaxe bash
    if bash -n "$script"; then
        print_success "$script syntaxe valide"
    else
        print_error "$script erreur de syntaxe"
    fi
done

# Vérification des configurations Nginx
print_status "Vérification des configurations Nginx..."

nginx_configs=("nginx/staging.conf" "nginx/production.conf")
for config in "${nginx_configs[@]}"; do
    if command -v nginx &> /dev/null; then
        if nginx -t -c "$config" &> /dev/null; then
            print_success "$config valide"
        else
            print_warning "$config à vérifier manuellement"
        fi
    else
        print_warning "nginx non installé, validation manuelle"
    fi
done

# Vérification de la structure des répertoires
print_status "Vérification de la structure des répertoires..."

required_dirs=("scripts" "nginx")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_success "Répertoire '$dir' présent"
    else
        print_warning "Répertoire '$dir' manquant"
        mkdir -p "$dir"
        print_success "Répertoire '$dir' créé"
    fi
done

# Test de connectivité (si possible)
print_status "Test de connectivité..."

if command -v curl &> /dev/null; then
    if curl -s --connect-timeout 5 https://gitlab.com > /dev/null; then
        print_success "Connectivité GitLab OK"
    else
        print_warning "Connectivité GitLab limitée"
    fi
    
    if curl -s --connect-timeout 5 https://registry.gitlab.com > /dev/null; then
        print_success "Connectivité Registry GitLab OK"
    else
        print_warning "Connectivité Registry GitLab limitée"
    fi
else
    print_warning "curl non disponible, tests de connectivité ignorés"
fi

# Résumé et recommandations
echo ""
print_status "Résumé de la validation"
echo "========================"

print_success "Configuration GitLab CI/CD prête"
echo ""
echo "📋 Étapes suivantes:"
echo "1. Pousser les fichiers vers GitLab"
echo "2. Configurer les variables d'environnement dans GitLab"
echo "3. Configurer les runners GitLab"
echo "4. Préparer les serveurs de staging et production"
echo "5. Tester le pipeline sur une branche de développement"
echo ""
echo "🔧 Configuration GitLab requise:"
echo "   - Settings > CI/CD > Variables"
echo "   - Settings > CI/CD > Runners"
echo "   - Settings > Repository > Deploy Keys"
echo ""
echo "🌐 URLs de test:"
echo "   - Staging: https://staging.karma-com-solidarite.fr"
echo "   - Production: https://karma-com-solidarite.fr"
echo ""
echo "📚 Documentation:"
echo "   - Voir GUIDE-GITLAB-CI.md pour les détails"
echo "   - Voir les scripts dans le dossier scripts/"

print_success "Validation terminée avec succès!"
