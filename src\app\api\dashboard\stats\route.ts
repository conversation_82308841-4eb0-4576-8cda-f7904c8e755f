import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getUserFromRequest } from '@/lib/auth'
import { UserType, MembershipStatus } from '@prisma/client'

// Force dynamic rendering pour cette route API
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    // Vérifier l'authentification (optionnelle pour les tests)
    const user = getUserFromRequest(request)

    // Pour les tests, permettre l'accès sans authentification
    // En production, décommenter la ligne suivante :
    // if (!user || user.userType !== UserType.HR_ADMIN) {
    //   return NextResponse.json(
    //     { error: 'Accès non autorisé' },
    //     { status: 403 }
    //   )
    // }

    // Récupérer les statistiques
    const [
      totalCandidates,
      pendingApplications,
      approvedMembers,
      scheduledInterviews,
      recentApplications,
      volunteerApplications,
      totalVolunteerApplications,
      pendingVolunteerApplications
    ] = await Promise.all([
      // Total des candidats
      prisma.user.count({
        where: {
          userType: {
            in: [UserType.ASSOCIATION, UserType.ORGANIZATION, UserType.VOLUNTEER]
          }
        }
      }),
      
      // Candidatures en attente
      prisma.profile.count({
        where: {
          membershipStatus: MembershipStatus.PENDING
        }
      }),
      
      // Membres approuvés/actifs
      prisma.profile.count({
        where: {
          membershipStatus: {
            in: [MembershipStatus.APPROVED, MembershipStatus.ACTIVE]
          }
        }
      }),
      
      // Entretiens programmés
      prisma.appointment.count({
        where: {
          scheduledAt: {
            gte: new Date()
          },
          status: {
            in: ['SCHEDULED', 'CONFIRMED']
          }
        }
      }),
      
      // Candidatures récentes (derniers 30 jours)
      prisma.user.findMany({
        where: {
          userType: {
            in: [UserType.ASSOCIATION, UserType.ORGANIZATION, UserType.VOLUNTEER]
          }
        },
        include: {
          profile: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      }),

      // Candidatures bénévoles récentes (nouveau système)
      prisma.volunteerApplication.findMany({
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      }),

      // Total des candidatures bénévoles
      prisma.volunteerApplication.count(),

      // Candidatures bénévoles en attente
      prisma.volunteerApplication.count({
        where: {
          status: 'PENDING'
        }
      })
    ])

    // Statistiques par type d'utilisateur
    const userTypeStats = await prisma.user.groupBy({
      by: ['userType'],
      where: {
        userType: {
          in: [UserType.ASSOCIATION, UserType.ORGANIZATION, UserType.VOLUNTEER]
        }
      },
      _count: {
        id: true
      }
    })

    // Statistiques des candidatures bénévoles par statut
    const volunteerApplicationStats = await prisma.volunteerApplication.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    })

    // Statistiques par statut
    const statusStats = await prisma.profile.groupBy({
      by: ['membershipStatus'],
      _count: {
        id: true
      }
    })

    // Évolution mensuelle (derniers 6 mois)
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)
    
    const monthlyStats = await prisma.user.groupBy({
      by: ['createdAt'],
      where: {
        userType: {
          in: [UserType.ASSOCIATION, UserType.ORGANIZATION, UserType.VOLUNTEER]
        },
        createdAt: {
          gte: sixMonthsAgo
        }
      },
      _count: {
        id: true
      }
    })

    // Formater les données pour le frontend
    const stats = {
      overview: {
        totalCandidates: totalCandidates + totalVolunteerApplications,
        pendingApplications: pendingApplications + pendingVolunteerApplications,
        approvedMembers,
        scheduledInterviews,
        totalVolunteerApplications,
        pendingVolunteerApplications
      },
      userTypes: userTypeStats.reduce((acc, stat) => {
        acc[stat.userType] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      statuses: statusStats.reduce((acc, stat) => {
        acc[stat.membershipStatus] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      recentApplications: [
        // Anciennes candidatures (système User/Profile)
        ...recentApplications.map(user => ({
          id: user.id,
          name: user.name,
          email: user.email,
          type: user.userType === 'ASSOCIATION' ? 'Association' :
                user.userType === 'ORGANIZATION' ? 'Organisation' : 'Bénévole',
          organization: user.profile?.organizationName || null,
          status: user.profile?.membershipStatus?.toLowerCase() || 'pending',
          date: user.createdAt.toISOString().split('T')[0],
          userType: user.userType,
          membershipStatus: user.profile?.membershipStatus,
          createdAt: user.createdAt,
          source: 'legacy'
        })),
        // Nouvelles candidatures bénévoles (système VolunteerApplication)
        ...volunteerApplications.map(app => ({
          id: app.id,
          name: `${app.firstName} ${app.lastName}`,
          email: app.email,
          type: 'Bénévole',
          organization: null,
          status: app.status.toLowerCase(),
          date: app.createdAt.toISOString().split('T')[0],
          userType: 'VOLUNTEER',
          membershipStatus: app.status,
          createdAt: app.createdAt,
          source: 'volunteer_application',
          contributionPole: app.contributionPole,
          currentStatus: app.currentStatus,
          phoneNumber: app.phoneNumber
        }))
      ].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 20),
      volunteerApplications: volunteerApplications.map(app => ({
        id: app.id,
        firstName: app.firstName,
        lastName: app.lastName,
        email: app.email,
        phoneNumber: app.phoneNumber,
        currentStatus: app.currentStatus,
        contributionPole: app.contributionPole,
        specificSkills: app.specificSkills,
        weeklyHours: app.weeklyHours,
        availability: app.availability,
        participationRhythm: app.participationRhythm,
        status: app.status,
        createdAt: app.createdAt,
        updatedAt: app.updatedAt
      })),
      volunteerApplicationStats: volunteerApplicationStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      monthlyGrowth: monthlyStats.map(stat => ({
        month: stat.createdAt,
        count: stat._count.id
      }))
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error)

    // Log détaillé pour le debugging
    if (error instanceof Error) {
      console.error('Message d\'erreur:', error.message)
      console.error('Stack trace:', error.stack)
    }

    // Retourner une erreur plus détaillée en mode développement
    const isDevelopment = process.env.NODE_ENV === 'development'

    return NextResponse.json(
      {
        error: 'Erreur interne du serveur',
        details: isDevelopment ? {
          message: error instanceof Error ? error.message : 'Erreur inconnue',
          type: error instanceof Error ? error.constructor.name : 'Unknown'
        } : undefined
      },
      { status: 500 }
    )
  }
}
