/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/inscription/benevole/page";
exports.ids = ["app/inscription/benevole/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finscription%2Fbenevole%2Fpage&page=%2Finscription%2Fbenevole%2Fpage&appPaths=%2Finscription%2Fbenevole%2Fpage&pagePath=private-next-app-dir%2Finscription%2Fbenevole%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finscription%2Fbenevole%2Fpage&page=%2Finscription%2Fbenevole%2Fpage&appPaths=%2Finscription%2Fbenevole%2Fpage&pagePath=private-next-app-dir%2Finscription%2Fbenevole%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'inscription',\n        {\n        children: [\n        'benevole',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/inscription/benevole/page.tsx */ \"(rsc)/./src/app/inscription/benevole/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/inscription/benevole/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/inscription/benevole/page\",\n        pathname: \"/inscription/benevole\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finscription%2Fbenevole%2Fpage&page=%2Finscription%2Fbenevole%2Fpage&appPaths=%2Finscription%2Fbenevole%2Fpage&pagePath=private-next-app-dir%2Finscription%2Fbenevole%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cinscription%5Cbenevole%5Cpage.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cinscription%5Cbenevole%5Cpage.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/inscription/benevole/page.tsx */ \"(ssr)/./src/app/inscription/benevole/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDaGFtemEuYmVkb3VpJTVDRG9jdW1lbnRzJTVDbWVzRG9jcyU1Q0FJJTVDS0NTJTVDYXVnbWVudC1rY3MlNUNzcmMlNUNhcHAlNUNpbnNjcmlwdGlvbiU1Q2JlbmV2b2xlJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2FybWEtY29tLWRhc2hib2FyZC8/OGI1OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhhbXphLmJlZG91aVxcXFxEb2N1bWVudHNcXFxcbWVzRG9jc1xcXFxBSVxcXFxLQ1NcXFxcYXVnbWVudC1rY3NcXFxcc3JjXFxcXGFwcFxcXFxpbnNjcmlwdGlvblxcXFxiZW5ldm9sZVxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp%5Cinscription%5Cbenevole%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/inscription/benevole/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/inscription/benevole/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BenevoleInscriptionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,MapPin,Star,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction BenevoleInscriptionPage() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Informations de contact\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        // Informations personnelles\n        firstName: \"\",\n        lastName: \"\",\n        phone: \"\",\n        dateOfBirth: \"\",\n        // Adresse\n        address: \"\",\n        city: \"\",\n        postalCode: \"\",\n        country: \"France\",\n        // Compétences et disponibilités\n        skills: [],\n        customSkills: \"\",\n        availability: \"\",\n        experience: \"\",\n        motivation: \"\",\n        // Préférences\n        preferredDepartments: [],\n        remoteWork: false,\n        // Conditions\n        acceptTerms: false,\n        acceptRGPD: false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const availableSkills = [\n        \"Communication\",\n        \"Marketing digital\",\n        \"D\\xe9veloppement web\",\n        \"Design graphique\",\n        \"Gestion de projet\",\n        \"Comptabilit\\xe9\",\n        \"Juridique\",\n        \"Traduction\",\n        \"R\\xe9daction\",\n        \"Photographie\",\n        \"Vid\\xe9o\",\n        \"Formation\",\n        \"\\xc9v\\xe9nementiel\",\n        \"Logistique\",\n        \"Autre\"\n    ];\n    const departments = [\n        \"RH/Asso\",\n        \"Communication\",\n        \"Partenariats & RSE\",\n        \"IT\",\n        \"Design\",\n        \"Juridique\",\n        \"Comptabilit\\xe9\",\n        \"Auto & AI\"\n    ];\n    const availabilityOptions = [\n        \"Quelques heures par semaine\",\n        \"Une demi-journ\\xe9e par semaine\",\n        \"Une journ\\xe9e par semaine\",\n        \"Plusieurs jours par semaine\",\n        \"Ponctuellement selon les projets\",\n        \"Disponibilit\\xe9 flexible\"\n    ];\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === \"checkbox\" ? e.target.checked : value\n            }));\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n    };\n    const handleSkillChange = (skill)=>{\n        setFormData((prev)=>({\n                ...prev,\n                skills: prev.skills.includes(skill) ? prev.skills.filter((s)=>s !== skill) : [\n                    ...prev.skills,\n                    skill\n                ]\n            }));\n    };\n    const handleDepartmentChange = (department)=>{\n        setFormData((prev)=>({\n                ...prev,\n                preferredDepartments: prev.preferredDepartments.includes(department) ? prev.preferredDepartments.filter((d)=>d !== department) : [\n                    ...prev.preferredDepartments,\n                    department\n                ]\n            }));\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Validation email\n        if (!formData.email) {\n            newErrors.email = \"L'email est requis\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Format d'email invalide\";\n        }\n        // Validation mot de passe\n        if (!formData.password) {\n            newErrors.password = \"Le mot de passe est requis\";\n        } else if (formData.password.length < 8) {\n            newErrors.password = \"Le mot de passe doit contenir au moins 8 caract\\xe8res\";\n        }\n        if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = \"Les mots de passe ne correspondent pas\";\n        }\n        // Validation champs requis\n        const requiredFields = [\n            \"firstName\",\n            \"lastName\",\n            \"motivation\"\n        ];\n        requiredFields.forEach((field)=>{\n            if (!formData[field]) {\n                newErrors[field] = \"Ce champ est requis\";\n            }\n        });\n        // Validation date de naissance\n        if (formData.dateOfBirth) {\n            const birthDate = new Date(formData.dateOfBirth);\n            const today = new Date();\n            const age = today.getFullYear() - birthDate.getFullYear();\n            if (age < 16) {\n                newErrors.dateOfBirth = \"Vous devez avoir au moins 16 ans\";\n            }\n        }\n        // Validation acceptation des conditions\n        if (!formData.acceptTerms) {\n            newErrors.acceptTerms = \"Vous devez accepter les conditions d'utilisation\";\n        }\n        if (!formData.acceptRGPD) {\n            newErrors.acceptRGPD = \"Vous devez accepter la politique de confidentialit\\xe9\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"/api/auth/register/volunteer\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (response.ok) {\n                alert(\"Inscription r\\xe9ussie ! Vous recevrez un email de confirmation.\");\n                // Réinitialiser le formulaire\n                setFormData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    firstName: \"\",\n                    lastName: \"\",\n                    phone: \"\",\n                    dateOfBirth: \"\",\n                    address: \"\",\n                    city: \"\",\n                    postalCode: \"\",\n                    country: \"France\",\n                    skills: [],\n                    customSkills: \"\",\n                    availability: \"\",\n                    experience: \"\",\n                    motivation: \"\",\n                    preferredDepartments: [],\n                    remoteWork: false,\n                    acceptTerms: false,\n                    acceptRGPD: false\n                });\n            } else {\n                // Afficher les erreurs de validation\n                if (data.details) {\n                    const newErrors = {};\n                    data.details.forEach((error)=>{\n                        if (error.path && error.path.length > 0) {\n                            newErrors[error.path[0]] = error.message;\n                        }\n                    });\n                    setErrors(newErrors);\n                } else {\n                    alert(data.error || \"Une erreur est survenue lors de l'inscription.\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Erreur lors de l'inscription:\", error);\n            alert(\"Une erreur est survenue. Veuillez r\\xe9essayer.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-karma py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"text-white\",\n                                        size: 32\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Devenir B\\xe9n\\xe9vole\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Rejoignez notre \\xe9quipe de b\\xe9n\\xe9voles et contribuez \\xe0 des projets solidaires\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Informations de connexion\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Email *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            name: \"email\",\n                                                            value: formData.email,\n                                                            onChange: handleInputChange,\n                                                            className: `karma-input ${errors.email ? \"border-red-500\" : \"\"}`,\n                                                            placeholder: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 36\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"T\\xe9l\\xe9phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            name: \"phone\",\n                                                            value: formData.phone,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"+33 1 23 45 67 89\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Mot de passe *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            name: \"password\",\n                                                            value: formData.password,\n                                                            onChange: handleInputChange,\n                                                            className: `karma-input ${errors.password ? \"border-red-500\" : \"\"}`,\n                                                            placeholder: \"Minimum 8 caract\\xe8res\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.password\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 39\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Confirmer le mot de passe *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            name: \"confirmPassword\",\n                                                            value: formData.confirmPassword,\n                                                            onChange: handleInputChange,\n                                                            className: `karma-input ${errors.confirmPassword ? \"border-red-500\" : \"\"}`,\n                                                            placeholder: \"R\\xe9p\\xe9tez le mot de passe\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.confirmPassword\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 46\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Informations personnelles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Pr\\xe9nom *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"firstName\",\n                                                            value: formData.firstName,\n                                                            onChange: handleInputChange,\n                                                            className: `karma-input ${errors.firstName ? \"border-red-500\" : \"\"}`,\n                                                            placeholder: \"Votre pr\\xe9nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.firstName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 40\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Nom *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"lastName\",\n                                                            value: formData.lastName,\n                                                            onChange: handleInputChange,\n                                                            className: `karma-input ${errors.lastName ? \"border-red-500\" : \"\"}`,\n                                                            placeholder: \"Votre nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.lastName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 39\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Date de naissance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            name: \"dateOfBirth\",\n                                                            value: formData.dateOfBirth,\n                                                            onChange: handleInputChange,\n                                                            className: `karma-input ${errors.dateOfBirth ? \"border-red-500\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.dateOfBirth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.dateOfBirth\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 42\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Adresse\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Adresse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"address\",\n                                                            value: formData.address,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"Num\\xe9ro et nom de rue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Ville\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"city\",\n                                                                    value: formData.city,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    placeholder: \"Ville\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Code postal\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"postalCode\",\n                                                                    value: formData.postalCode,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    placeholder: \"75001\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"karma-label\",\n                                                                    children: \"Pays\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    name: \"country\",\n                                                                    value: formData.country,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"karma-input\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"France\",\n                                                                            children: \"France\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 408,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Belgique\",\n                                                                            children: \"Belgique\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Suisse\",\n                                                                            children: \"Suisse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Canada\",\n                                                                            children: \"Canada\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"Autre\",\n                                                                            children: \"Autre\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 412,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Comp\\xe9tences et exp\\xe9rience\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Comp\\xe9tences (s\\xe9lectionnez toutes celles qui s'appliquent)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 md:grid-cols-3 gap-3 mt-2\",\n                                                            children: availableSkills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-2 cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: formData.skills.includes(skill),\n                                                                            onChange: ()=>handleSkillChange(skill),\n                                                                            className: \"rounded border-gray-300 text-accent-600 focus:ring-accent-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: skill\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, skill, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Autres comp\\xe9tences\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"customSkills\",\n                                                            value: formData.customSkills,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"D\\xe9crivez d'autres comp\\xe9tences non list\\xe9es...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Exp\\xe9rience en b\\xe9n\\xe9volat\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"experience\",\n                                                            value: formData.experience,\n                                                            onChange: handleInputChange,\n                                                            rows: 3,\n                                                            className: \"karma-input\",\n                                                            placeholder: \"D\\xe9crivez votre exp\\xe9rience en b\\xe9n\\xe9volat ou dans des projets similaires...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Disponibilit\\xe9s et pr\\xe9f\\xe9rences\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"Disponibilit\\xe9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"availability\",\n                                                            value: formData.availability,\n                                                            onChange: handleInputChange,\n                                                            className: \"karma-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"S\\xe9lectionnez votre disponibilit\\xe9\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                availabilityOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: option,\n                                                                        children: option\n                                                                    }, option, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"karma-label\",\n                                                            children: \"P\\xf4les d'int\\xe9r\\xeat (s\\xe9lectionnez ceux qui vous int\\xe9ressent)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-3 mt-2\",\n                                                            children: departments.map((dept)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-2 cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: formData.preferredDepartments.includes(dept),\n                                                                            onChange: ()=>handleDepartmentChange(dept),\n                                                                            className: \"rounded border-gray-300 text-accent-600 focus:ring-accent-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: dept\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, dept, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            name: \"remoteWork\",\n                                                            checked: formData.remoteWork,\n                                                            onChange: handleInputChange,\n                                                            className: \"rounded border-gray-300 text-accent-600 focus:ring-accent-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: \"Je suis disponible pour du travail \\xe0 distance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"form-title flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_MapPin_Star_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"mr-3 text-accent-600\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Motivation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"karma-label\",\n                                                    children: \"Pourquoi souhaitez-vous devenir b\\xe9n\\xe9vole chez Karma Com ? *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    name: \"motivation\",\n                                                    value: formData.motivation,\n                                                    onChange: handleInputChange,\n                                                    rows: 4,\n                                                    className: `karma-input ${errors.motivation ? \"border-red-500\" : \"\"}`,\n                                                    placeholder: \"Expliquez vos motivations et ce que vous esp\\xe9rez apporter \\xe0 notre association...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.motivation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm mt-1\",\n                                                    children: errors.motivation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 39\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-section\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        name: \"acceptTerms\",\n                                                        checked: formData.acceptTerms,\n                                                        onChange: handleInputChange,\n                                                        className: \"mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: [\n                                                            \"J'accepte les\",\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/conditions\",\n                                                                className: \"text-accent-600 hover:underline\",\n                                                                children: \"conditions d'utilisation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"de Karma Com Solidarit\\xe9 *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.acceptTerms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: errors.acceptTerms\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 40\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        name: \"acceptRGPD\",\n                                                        checked: formData.acceptRGPD,\n                                                        onChange: handleInputChange,\n                                                        className: \"mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: [\n                                                            \"J'accepte la\",\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/politique-confidentialite\",\n                                                                className: \"text-accent-600 hover:underline\",\n                                                                children: \"politique de confidentialit\\xe9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"et le traitement de mes donn\\xe9es personnelles selon le RGPD *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.acceptRGPD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: errors.acceptRGPD\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 39\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: `bg-gradient-to-r from-accent-600 to-accent-700 text-white font-medium py-3 px-12 rounded-lg hover:from-accent-700 hover:to-accent-800 transition-all duration-200 transform hover:scale-105 text-lg ${isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                                        children: isSubmitting ? \"Inscription en cours...\" : \"Rejoindre l'\\xe9quipe\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n                lineNumber: 602,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\inscription\\\\benevole\\\\page.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/inscription/benevole/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Logo */ \"(ssr)/./src/components/ui/Logo.tsx\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n\n\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-karma py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: \"md\",\n                                        showText: true,\n                                        className: \"text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 max-w-md\",\n                                    children: \"Karma Com Solidarit\\xe9 soutient ses associations partenaires dans la r\\xe9alisation de leurs projets. Ensemble, nous construisons un r\\xe9seau solidaire et engag\\xe9.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-primary-400 transition-colors duration-200\",\n                                            \"aria-label\": \"Facebook\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-primary-400 transition-colors duration-200\",\n                                            \"aria-label\": \"Twitter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-primary-400 transition-colors duration-200\",\n                                            \"aria-label\": \"LinkedIn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Liens rapides\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/inscription/association\",\n                                                className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                children: \"Inscription Association\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/inscription/organisation\",\n                                                className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                children: \"Inscription Organisation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/inscription/benevole\",\n                                                className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                children: \"Devenir B\\xe9n\\xe9vole\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/dashboard\",\n                                                className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                children: \"Dashboard RH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"tel:+33123456789\",\n                                                    className: \"text-gray-300 hover:text-primary-400 transition-colors duration-200\",\n                                                    children: \"+33 1 23 45 67 89\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"text-primary-400 mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"123 Rue de la Solidarit\\xe9\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 43\n                                                        }, undefined),\n                                                        \"75001 Paris, France\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" Karma Com Solidarit\\xe9. Tous droits r\\xe9serv\\xe9s.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/mentions-legales\",\n                                        className: \"text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200\",\n                                        children: \"Mentions l\\xe9gales\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/politique-confidentialite\",\n                                        className: \"text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200\",\n                                        children: \"Politique de confidentialit\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/rgpd\",\n                                        className: \"text-gray-400 hover:text-primary-400 text-sm transition-colors duration-200\",\n                                        children: \"RGPD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_Logo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Logo */ \"(ssr)/./src/components/ui/Logo.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: \"Accueil\",\n            href: \"/\"\n        },\n        {\n            name: \"Associations\",\n            href: \"/inscription/association\"\n        },\n        {\n            name: \"Organisations\",\n            href: \"/inscription/organisation\"\n        },\n        {\n            name: \"B\\xe9n\\xe9voles\",\n            href: \"/inscription/benevole\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    const isActive = (href)=>{\n        return pathname === href || href !== \"/\" && pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg border-b border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-karma\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: \"md\",\n                                showText: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-primary-600 bg-primary-50 border-b-2 border-primary-600\" : \"text-gray-700 hover:text-primary-600 hover:bg-gray-50\"}`,\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/connexion\",\n                                    className: \"flex items-center space-x-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Connexion\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"karma-button-primary\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-50 transition-colors duration-200\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 29\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 47\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-primary-600 bg-primary-50\" : \"text-gray-700 hover:text-primary-600 hover:bg-gray-50\"}`,\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-200 flex flex-col space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/connexion\",\n                                        className: \"flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Connexion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"karma-button-primary text-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Logo.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Logo.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Logo = ({ size = \"md\", showText = true, className = \"\" })=>{\n    const sizeClasses = {\n        sm: \"h-8 w-8\",\n        md: \"h-12 w-12\",\n        lg: \"h-16 w-16\",\n        xl: \"h-24 w-24\"\n    };\n    const textSizeClasses = {\n        sm: \"text-lg\",\n        md: \"text-xl\",\n        lg: \"text-2xl\",\n        xl: \"text-3xl\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center space-x-3 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${sizeClasses[size]} relative`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    viewBox: \"0 0 100 100\",\n                    className: \"w-full h-full\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"logoGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"100%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#0ea5e9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#ec4899\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                            x: \"5\",\n                            y: \"5\",\n                            width: \"90\",\n                            height: \"90\",\n                            rx: \"20\",\n                            ry: \"20\",\n                            fill: \"#f8fafc\",\n                            stroke: \"#e2e8f0\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M25 20 L25 80 L35 80 L35 55 L50 70 L65 70 L45 50 L65 30 L50 30 L35 45 L35 20 Z\",\n                            fill: \"url(#logoGradient)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"70\",\n                            cy: \"30\",\n                            r: \"8\",\n                            fill: \"#ec4899\",\n                            opacity: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `font-bold text-primary-700 ${textSizeClasses[size]} leading-tight`,\n                        children: \"Karma Com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `font-medium text-secondary-600 ${size === \"sm\" ? \"text-xs\" : size === \"md\" ? \"text-sm\" : \"text-base\"} leading-tight`,\n                        children: \"Solidarit\\xe9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Logo);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Logo.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f9200e9d0ff7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2FybWEtY29tLWRhc2hib2FyZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YTBiNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY5MjAwZTlkMGZmN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/inscription/benevole/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/inscription/benevole/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\mesDocs\AI\KCS\augment-kcs\src\app\inscription\benevole\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Karma Com Solidarit\\xe9 - Gestion d'Adh\\xe9sion\",\n    description: \"Plateforme de gestion d'adh\\xe9sion pour Karma Com Solidarit\\xe9 - Associations, Organisations et B\\xe9n\\xe9voles\",\n    keywords: \"karma com, solidarit\\xe9, adh\\xe9sion, associations, b\\xe9n\\xe9voles, organisations\",\n    authors: [\n        {\n            name: \"Karma Com Solidarit\\xe9\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\mesDocs\\\\AI\\\\KCS\\\\augment-kcs\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBdUI7S0FBRTtJQUMzQ0MsVUFBVTtBQUNaLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV2IsK0pBQWU7c0JBQzlCLDRFQUFDYztnQkFBSUQsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2thcm1hLWNvbS1kYXNoYm9hcmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0thcm1hIENvbSBTb2xpZGFyaXTDqSAtIEdlc3Rpb24gZFxcJ0FkaMOpc2lvbicsXG4gIGRlc2NyaXB0aW9uOiAnUGxhdGVmb3JtZSBkZSBnZXN0aW9uIGRcXCdhZGjDqXNpb24gcG91ciBLYXJtYSBDb20gU29saWRhcml0w6kgLSBBc3NvY2lhdGlvbnMsIE9yZ2FuaXNhdGlvbnMgZXQgQsOpbsOpdm9sZXMnLFxuICBrZXl3b3JkczogJ2thcm1hIGNvbSwgc29saWRhcml0w6ksIGFkaMOpc2lvbiwgYXNzb2NpYXRpb25zLCBiw6luw6l2b2xlcywgb3JnYW5pc2F0aW9ucycsXG4gIGF1dGhvcnM6IFt7IG5hbWU6ICdLYXJtYSBDb20gU29saWRhcml0w6knIH1dLFxuICB2aWV3cG9ydDogJ3dpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImZyXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsInZpZXdwb3J0IiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finscription%2Fbenevole%2Fpage&page=%2Finscription%2Fbenevole%2Fpage&appPaths=%2Finscription%2Fbenevole%2Fpage&pagePath=private-next-app-dir%2Finscription%2Fbenevole%2Fpage.tsx&appDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chamza.bedoui%5CDocuments%5CmesDocs%5CAI%5CKCS%5Caugment-kcs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();