#!/bin/bash

# Script de test pour la création d'archive
echo "🧪 Test de Création d'Archive - Karma Com Solidarité"
echo "=================================================="

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Vérification des prérequis
log "Vérification des prérequis..."

if [ ! -f "package.json" ]; then
    error "Ce script doit être exécuté depuis la racine du projet"
fi

# Créer un répertoire temporaire pour le test
TEMP_DIR=$(mktemp -d)
ARCHIVE_NAME="karma-com-test.tar.gz"

log "Répertoire temporaire: $TEMP_DIR"

# Créer la liste des fichiers à inclure
FILES_TO_COPY=(
    "src"
    "prisma" 
    "package.json"
    "next.config.js"
    "tailwind.config.js"
    "postcss.config.js"
    "tsconfig.json"
    "Dockerfile.simple"
)

log "Copie des fichiers essentiels..."

# Copier les fichiers essentiels un par un
for file in "${FILES_TO_COPY[@]}"; do
    if [ -e "$file" ]; then
        cp -r "$file" "$TEMP_DIR/"
        success "Copié: $file"
    else
        warning "Fichier non trouvé: $file"
    fi
done

# Copier les fichiers optionnels
log "Copie des fichiers optionnels..."

if [ -f "package-lock.json" ]; then
    cp package-lock.json "$TEMP_DIR/"
    success "Copié: package-lock.json"
fi

if [ -d "public" ]; then
    cp -r public "$TEMP_DIR/"
    success "Copié: public/"
else
    mkdir -p "$TEMP_DIR/public"
    echo "<h1>Karma Com Solidarité</h1>" > "$TEMP_DIR/public/index.html"
    success "Créé: public/index.html"
fi

if [ -f "docker-compose.production.yml" ]; then
    cp docker-compose.production.yml "$TEMP_DIR/"
    success "Copié: docker-compose.production.yml"
elif [ -f "docker-compose.yml" ]; then
    cp docker-compose.yml "$TEMP_DIR/docker-compose.production.yml"
    success "Copié: docker-compose.yml → docker-compose.production.yml"
fi

if [ -d "nginx" ]; then
    cp -r nginx "$TEMP_DIR/"
    success "Copié: nginx/"
else
    mkdir -p "$TEMP_DIR/nginx"
    success "Créé: nginx/"
fi

# Lister le contenu du répertoire temporaire
log "Contenu du répertoire temporaire:"
ls -la "$TEMP_DIR"

# Créer l'archive
log "Création de l'archive..."
cd "$TEMP_DIR"

if tar -czf "$ARCHIVE_NAME" --exclude="$ARCHIVE_NAME" * 2>/dev/null; then
    success "Archive créée avec succès"
else
    error "Échec de la création de l'archive"
fi

cd - > /dev/null

# Vérifier l'archive
if [ -f "$TEMP_DIR/$ARCHIVE_NAME" ]; then
    ARCHIVE_SIZE=$(du -h "$TEMP_DIR/$ARCHIVE_NAME" | cut -f1)
    success "Archive trouvée - Taille: $ARCHIVE_SIZE"
    
    # Tester l'extraction
    log "Test d'extraction de l'archive..."
    TEST_EXTRACT_DIR=$(mktemp -d)
    
    cd "$TEST_EXTRACT_DIR"
    if tar -xzf "$TEMP_DIR/$ARCHIVE_NAME" 2>/dev/null; then
        success "Extraction réussie"
        
        # Vérifier le contenu extrait
        log "Contenu extrait:"
        ls -la
        
        # Vérifier les fichiers critiques
        critical_files=("package.json" "Dockerfile.simple" "src")
        for file in "${critical_files[@]}"; do
            if [ -e "$file" ]; then
                success "Fichier critique présent: $file"
            else
                error "Fichier critique manquant: $file"
            fi
        done
        
    else
        error "Échec de l'extraction de l'archive"
    fi
    cd - > /dev/null
    
    # Nettoyer le répertoire de test
    rm -rf "$TEST_EXTRACT_DIR"
    
else
    error "Archive non trouvée après création"
fi

# Nettoyer
log "Nettoyage..."
rm -rf "$TEMP_DIR"

success "Test d'archive terminé avec succès!"

echo ""
echo "📊 Résumé du test:"
echo "✅ Fichiers copiés correctement"
echo "✅ Archive créée sans erreur"
echo "✅ Archive extractible"
echo "✅ Fichiers critiques présents"
echo ""
echo "🚀 L'archive peut être utilisée pour le déploiement"
