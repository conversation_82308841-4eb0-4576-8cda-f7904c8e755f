#!/bin/bash

# Script de déploiement pour l'environnement staging
# Usage: ./deploy-staging.sh

set -e

echo "🚀 Déploiement Staging - Karma Com Solidarité"
echo "============================================="

# Configuration
DEPLOY_DIR="/opt/karma-com-staging"
BACKUP_DIR="/opt/backups/staging"
LOG_FILE="/var/log/karma-com-staging-deploy.log"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

# Vérifications préliminaires
log "Vérification des prérequis..."

if [ ! -d "$DEPLOY_DIR" ]; then
    error "Répertoire de déploiement $DEPLOY_DIR non trouvé"
fi

if ! command -v docker &> /dev/null; then
    error "Docker n'est pas installé"
fi

if ! command -v docker-compose &> /dev/null; then
    error "Docker Compose n'est pas installé"
fi

success "Prérequis validés"

# Sauvegarde de la base de données
log "Sauvegarde de la base de données staging..."
mkdir -p "$BACKUP_DIR"

if docker ps | grep -q "karma-com-postgres-staging"; then
    BACKUP_FILE="$BACKUP_DIR/staging_backup_$(date +%Y%m%d_%H%M%S).sql"
    docker exec karma-com-postgres-staging pg_dump -U karma_user_staging karma_com_staging > "$BACKUP_FILE" || warning "Échec de la sauvegarde"
    success "Sauvegarde créée: $BACKUP_FILE"
else
    warning "Base de données staging non trouvée, pas de sauvegarde"
fi

# Mise à jour du code
log "Mise à jour du code source..."
cd "$DEPLOY_DIR"

# Sauvegarder les modifications locales
git stash push -m "Auto-stash before deployment $(date)"

# Récupérer les dernières modifications
git fetch origin
git checkout develop
git pull origin develop

success "Code source mis à jour"

# Vérification des variables d'environnement
log "Vérification des variables d'environnement..."

if [ -z "$REGISTRY_IMAGE" ]; then
    warning "REGISTRY_IMAGE non défini, utilisation de l'image locale"
    export REGISTRY_IMAGE="karma-com-app"
fi

if [ -z "$IMAGE_TAG" ]; then
    warning "IMAGE_TAG non défini, utilisation de 'latest'"
    export IMAGE_TAG="latest"
fi

# Variables d'environnement staging
export POSTGRES_PASSWORD_STAGING="${POSTGRES_PASSWORD_STAGING:-staging_password_2024}"
export NEXTAUTH_SECRET_STAGING="${NEXTAUTH_SECRET_STAGING:-staging_secret_key_2024}"
export PGADMIN_PASSWORD_STAGING="${PGADMIN_PASSWORD_STAGING:-staging_admin_2024}"

success "Variables d'environnement configurées"

# Arrêt des services existants
log "Arrêt des services existants..."
docker-compose -f docker-compose.staging.yml down || warning "Aucun service à arrêter"

# Nettoyage des images anciennes
log "Nettoyage des images Docker..."
docker image prune -f || warning "Échec du nettoyage"

# Récupération de la nouvelle image
log "Récupération de l'image Docker..."
docker-compose -f docker-compose.staging.yml pull || warning "Échec du pull de l'image"

# Démarrage des services
log "Démarrage des services..."
docker-compose -f docker-compose.staging.yml up -d

# Attente du démarrage
log "Attente du démarrage des services..."
sleep 30

# Vérification de la santé des services
log "Vérification de la santé des services..."

# Vérifier PostgreSQL
if docker-compose -f docker-compose.staging.yml exec -T postgres pg_isready -U karma_user_staging; then
    success "PostgreSQL est opérationnel"
else
    error "PostgreSQL n'est pas accessible"
fi

# Vérifier l'application
log "Test de l'application..."
sleep 30

if curl -f http://localhost:3001 > /dev/null 2>&1; then
    success "Application accessible sur le port 3001"
else
    error "Application non accessible"
fi

# Test de l'URL publique (si configurée)
if [ ! -z "$STAGING_URL" ]; then
    log "Test de l'URL publique: $STAGING_URL"
    if curl -f "$STAGING_URL" > /dev/null 2>&1; then
        success "URL publique accessible"
    else
        warning "URL publique non accessible (normal si pas encore configurée)"
    fi
fi

# Affichage des logs récents
log "Logs récents de l'application:"
docker-compose -f docker-compose.staging.yml logs --tail=10 app

# Affichage de l'état final
log "État final des services:"
docker-compose -f docker-compose.staging.yml ps

# Nettoyage final
log "Nettoyage final..."
docker system prune -f || warning "Échec du nettoyage système"

# Résumé du déploiement
echo ""
echo "📊 Résumé du Déploiement Staging"
echo "================================"
success "Déploiement staging terminé avec succès"
echo "🌐 Application: http://localhost:3001"
echo "🗄️ pgAdmin: http://localhost:5051"
echo "📋 Logs: docker-compose -f docker-compose.staging.yml logs -f"
echo "🔄 Redémarrer: docker-compose -f docker-compose.staging.yml restart"
echo "🛑 Arrêter: docker-compose -f docker-compose.staging.yml down"

# Log final
log "Déploiement staging terminé avec succès"
