version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: karma-com-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: karma_com_db
      POSTGRES_USER: karma_user
      POSTGRES_PASSWORD: karma_password_2024
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - karma-network

  # Application Next.js
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: karma-com-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*********************************************************/karma_com_db
      - NEXTAUTH_SECRET=karma_com_secret_key_2024
      - NEXTAUTH_URL=http://localhost:3000
    depends_on:
      - postgres
    networks:
      - karma-network
    volumes:
      - .:/app
      - /app/node_modules

  # pgAdmin pour la gestion de la base de données
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: karma-com-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - karma-network

volumes:
  postgres_data:

networks:
  karma-network:
    driver: bridge
