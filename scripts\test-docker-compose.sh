#!/bin/bash

# Script de test pour Docker Compose
echo "🐳 Test Docker Compose - Karma Com Solidarité"
echo "=============================================="

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier que Docker Compose est installé
print_status "Vérification de Docker Compose..."
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose n'est pas installé"
    exit 1
fi

print_success "Docker Compose est disponible"

# Vérifier que le fichier docker-compose.yml existe
if [ ! -f "docker-compose.yml" ]; then
    print_error "Fichier docker-compose.yml non trouvé"
    exit 1
fi

print_success "Fichier docker-compose.yml trouvé"

# Nettoyer les conteneurs précédents
print_status "Nettoyage des conteneurs précédents..."
docker-compose down -v 2>/dev/null || docker compose down -v 2>/dev/null || true

# Démarrer les services
print_status "Démarrage des services Docker Compose..."
echo "📦 Construction et démarrage en cours..."

if docker-compose up -d --build 2>/dev/null || docker compose up -d --build; then
    print_success "Services démarrés avec succès"
else
    print_error "Échec du démarrage des services"
    exit 1
fi

# Attendre que les services démarrent
print_status "Attente du démarrage des services..."
sleep 15

# Vérifier l'état des conteneurs
print_status "Vérification de l'état des conteneurs..."
echo "📋 État des conteneurs:"

if docker-compose ps 2>/dev/null || docker compose ps; then
    print_success "Conteneurs listés"
else
    print_warning "Impossible de lister les conteneurs"
fi

# Vérifier les logs de l'application
print_status "Vérification des logs de l'application..."
echo "📋 Logs de l'application (dernières 10 lignes):"
docker-compose logs --tail=10 app 2>/dev/null || docker compose logs --tail=10 app

# Vérifier les logs de PostgreSQL
print_status "Vérification des logs de PostgreSQL..."
echo "📋 Logs PostgreSQL (dernières 5 lignes):"
docker-compose logs --tail=5 postgres 2>/dev/null || docker compose logs --tail=5 postgres

# Tester la connectivité des services
print_status "Test de connectivité des services..."

# Test PostgreSQL
echo "🔍 Test PostgreSQL (port 5432)..."
if nc -z localhost 5432 2>/dev/null || timeout 3 bash -c "</dev/tcp/localhost/5432"; then
    print_success "PostgreSQL accessible sur le port 5432"
else
    print_warning "PostgreSQL non accessible sur le port 5432"
fi

# Test Application
echo "🔍 Test Application (port 3000)..."
sleep 5
if curl -f http://localhost:3000 &>/dev/null; then
    print_success "Application accessible sur http://localhost:3000"
else
    print_warning "Application pas encore accessible, vérification en cours..."
    # Attendre un peu plus et retester
    sleep 10
    if curl -f http://localhost:3000 &>/dev/null; then
        print_success "Application maintenant accessible sur http://localhost:3000"
    else
        print_warning "Application toujours non accessible"
        echo "📋 Logs récents de l'application:"
        docker-compose logs --tail=20 app 2>/dev/null || docker compose logs --tail=20 app
    fi
fi

# Test pgAdmin
echo "🔍 Test pgAdmin (port 5050)..."
if curl -f http://localhost:5050 &>/dev/null; then
    print_success "pgAdmin accessible sur http://localhost:5050"
else
    print_warning "pgAdmin non accessible sur le port 5050"
fi

# Afficher les URLs d'accès
echo ""
print_status "URLs d'accès aux services:"
echo "🌐 Application principale: http://localhost:3000"
echo "📊 Dashboard: http://localhost:3000/dashboard"
echo "🗄️ pgAdmin (gestion BDD): http://localhost:5050"
echo "   - Email: <EMAIL>"
echo "   - Mot de passe: admin123"

# Afficher les informations de connexion à la base de données
echo ""
print_status "Informations de connexion PostgreSQL:"
echo "🔗 Host: localhost"
echo "🔗 Port: 5432"
echo "🔗 Database: karma_com_db"
echo "🔗 User: karma_user"
echo "🔗 Password: karma_password_2024"

# Test de santé final
echo ""
print_status "Test de santé final..."
healthy_services=0
total_services=3

# Vérifier chaque service
services=("postgres:5432" "app:3000" "pgadmin:5050")
service_names=("PostgreSQL" "Application" "pgAdmin")

for i in "${!services[@]}"; do
    service="${services[$i]}"
    name="${service_names[$i]}"
    port="${service##*:}"
    
    if nc -z localhost "$port" 2>/dev/null || timeout 2 bash -c "</dev/tcp/localhost/$port" 2>/dev/null; then
        print_success "$name est en ligne"
        ((healthy_services++))
    else
        print_warning "$name n'est pas accessible"
    fi
done

# Résumé final
echo ""
echo "📊 Résumé du déploiement:"
echo "✅ Services en ligne: $healthy_services/$total_services"

if [ $healthy_services -eq $total_services ]; then
    print_success "Tous les services sont opérationnels!"
    echo ""
    echo "🎉 Déploiement réussi!"
    echo "🌐 Accédez à votre application: http://localhost:3000"
    echo ""
    echo "🔧 Commandes utiles:"
    echo "   - Voir les logs: docker-compose logs -f"
    echo "   - Arrêter: docker-compose down"
    echo "   - Redémarrer: docker-compose restart"
elif [ $healthy_services -gt 0 ]; then
    print_warning "Déploiement partiel - certains services ne sont pas accessibles"
    echo ""
    echo "💡 Suggestions:"
    echo "   - Attendez quelques minutes pour que tous les services démarrent"
    echo "   - Vérifiez les logs: docker-compose logs"
    echo "   - Redémarrez si nécessaire: docker-compose restart"
else
    print_error "Aucun service n'est accessible"
    echo ""
    echo "🔧 Dépannage:"
    echo "   - Vérifiez les logs: docker-compose logs"
    echo "   - Vérifiez l'état: docker-compose ps"
    echo "   - Redémarrez: docker-compose down && docker-compose up -d"
fi

echo ""
print_status "Test terminé"
