# 🚀 Guide de Démarrage Rapide - Karma Com Dashboard

## 📋 Prérequis

Avant de commencer, assurez-vous d'avoir installé :
- **Node.js 18+** : [Télécharger Node.js](https://nodejs.org/)
- **Docker & Docker Compose** : [Télécharger Docker](https://www.docker.com/)
- **Git** : [Télécharger Git](https://git-scm.com/)

## 🛠 Installation

### 1. C<PERSON><PERSON> le projet
```bash
git clone <repository-url>
cd karma-com-dashboard
```

### 2. Installer les dépendances
```bash
npm install
```

### 3. Configurer l'environnement
```bash
# Le fichier .env est déjà configuré avec les bonnes valeurs
# Vérifiez que DATABASE_URL pointe vers le port 5434
```

### 4. Démarrer la base de données
```bash
# Démarrer PostgreSQL et pgAdmin
npm run docker:up

# Ou manuellement
docker-compose up -d postgres pgadmin
```

### 5. Initialiser la base de données
```bash
# Générer le client Prisma
npm run db:generate

# Appliquer les migrations
npm run db:migrate

# Initialiser avec des données de test
npm run db:init
```

### 6. Démarrer l'application
```bash
npm run dev
```

## 🌐 Accès aux Services

Une fois tout démarré, vous pouvez accéder à :

- **Application principale** : http://localhost:3000
- **Dashboard RH** : http://localhost:3000/dashboard
- **pgAdmin** : http://localhost:5050
  - Email : `<EMAIL>`
  - Mot de passe : `admin123`

## 🧪 Tester les Inscriptions

### Test automatique
```bash
# Lancer les tests d'inscription automatiques
npm run test:inscriptions
```

### Test manuel

1. **Inscription Association** : http://localhost:3000/inscription/association
2. **Inscription Organisation** : http://localhost:3000/inscription/organisation  
3. **Inscription Bénévole** : http://localhost:3000/inscription/benevole

### Comptes de test créés automatiquement

| Type | Email | Mot de passe | Statut |
|------|-------|--------------|--------|
| Admin RH | <EMAIL> | admin123 | Actif |
| Association | <EMAIL> | test123 | En attente |
| Organisation | <EMAIL> | test123 | Approuvé |
| Bénévole | <EMAIL> | test123 | Actif |

## 🔧 Commandes Utiles

### Base de données
```bash
npm run db:studio      # Ouvrir Prisma Studio
npm run db:migrate     # Appliquer les migrations
npm run db:reset       # Réinitialiser la base de données
npm run db:init        # Initialiser avec des données de test
```

### Docker
```bash
npm run docker:up      # Démarrer tous les services
npm run docker:down    # Arrêter tous les services
```

### Développement
```bash
npm run dev           # Démarrer en mode développement
npm run build         # Construire pour la production
npm run start         # Démarrer en mode production
```

## 🐛 Résolution de Problèmes

### La base de données ne se connecte pas
```bash
# Vérifier que PostgreSQL est démarré
docker ps | grep postgres

# Redémarrer les services
docker-compose down
docker-compose up -d postgres
```

### Erreur de migration Prisma
```bash
# Réinitialiser complètement la base
npm run db:reset
```

### Port 5434 déjà utilisé
```bash
# Changer le port dans docker-compose.yml
# Ligne 15: "5435:5432" au lieu de "5434:5432"
# Puis mettre à jour .env avec le nouveau port
```

### L'application ne démarre pas
```bash
# Vérifier les dépendances
npm install

# Vérifier les variables d'environnement
cat .env

# Redémarrer en mode debug
npm run dev
```

## 📊 Vérification du Fonctionnement

### 1. Pages accessibles
- ✅ Page d'accueil : http://localhost:3000
- ✅ Formulaires d'inscription fonctionnels
- ✅ Page de connexion : http://localhost:3000/connexion
- ✅ Dashboard RH : http://localhost:3000/dashboard

### 2. Base de données
- ✅ PostgreSQL accessible sur le port 5434
- ✅ Tables créées par Prisma
- ✅ Données de test présentes

### 3. Fonctionnalités
- ✅ Inscription association avec validation
- ✅ Inscription organisation avec validation  
- ✅ Inscription bénévole avec validation
- ✅ Connexion utilisateur
- ✅ Dashboard avec statistiques

## 🎯 Prochaines Étapes

1. **Tester les inscriptions** avec le script automatique
2. **Se connecter au dashboard** avec le compte admin
3. **Vérifier les données** dans pgAdmin ou Prisma Studio
4. **Personnaliser** selon vos besoins

## 📞 Support

Si vous rencontrez des problèmes :

1. Vérifiez les logs de l'application
2. Consultez les logs Docker : `docker-compose logs`
3. Vérifiez que tous les services sont démarrés : `docker ps`
4. Testez la connexion à la base de données

---

**Karma Com Solidarité** - Ensemble pour la solidarité 💙
