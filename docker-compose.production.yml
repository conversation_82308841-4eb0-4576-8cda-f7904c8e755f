# Docker Compose pour l'environnement de production
version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: karma-com-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: karma_com_db
      POSTGRES_USER: karma_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD_PROD}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./backups:/backups
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "127.0.0.1:5432:5432"  # Accessible seulement en local
    networks:
      - karma-network-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U karma_user -d karma_com_db"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Application Next.js
  app:
    image: ${REGISTRY_IMAGE:-karma-com-app}:${IMAGE_TAG:-latest}
    container_name: karma-com-app-prod
    restart: unless-stopped
    ports:
      - "127.0.0.1:3000:3000"  # Accessible seulement en local
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://karma_user:${POSTGRES_PASSWORD_PROD}@postgres:5432/karma_com_db
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET_PROD}
      - NEXTAUTH_URL=https://karma-com-solidarite.fr
      - LOG_LEVEL=warn
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - karma-network-prod
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # pgAdmin pour la gestion de la base de données (accès restreint)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: karma-com-pgadmin-prod
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD_PROD}
      PGADMIN_CONFIG_SERVER_MODE: 'True'
    volumes:
      - pgadmin_prod_data:/var/lib/pgadmin
    ports:
      - "127.0.0.1:5050:80"  # Accessible seulement en local
    networks:
      - karma-network-prod
    depends_on:
      - postgres

  # Nginx reverse proxy avec SSL
  nginx:
    image: nginx:alpine
    container_name: karma-com-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/production.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - karma-network-prod
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Service de backup automatique
  backup:
    image: postgres:15-alpine
    container_name: karma-com-backup
    restart: "no"
    environment:
      PGPASSWORD: ${POSTGRES_PASSWORD_PROD}
    volumes:
      - ./backups:/backups
      - ./scripts:/scripts
    networks:
      - karma-network-prod
    depends_on:
      - postgres
    command: >
      sh -c "
        while true; do
          sleep 86400;
          pg_dump -h postgres -U karma_user -d karma_com_db > /backups/backup_$$(date +%Y%m%d_%H%M%S).sql;
          find /backups -name '*.sql' -mtime +7 -delete;
        done
      "

networks:
  karma-network-prod:
    driver: bridge

volumes:
  postgres_prod_data:
    driver: local
  pgadmin_prod_data:
    driver: local
