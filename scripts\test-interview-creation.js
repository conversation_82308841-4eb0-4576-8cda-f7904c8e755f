// Script de test spécifique pour la création d'entretiens
const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function testInterviewCreation() {
  console.log('📅 Test de Création d\'Entretiens')
  console.log('================================')
  
  try {
    // 1. Récupérer les candidats disponibles
    console.log('👥 Récupération des candidats...')
    const candidatesResponse = await fetch(`${BASE_URL}/api/candidates`)
    
    if (!candidatesResponse.ok) {
      throw new Error(`Erreur candidats: HTTP ${candidatesResponse.status}`)
    }
    
    const candidatesData = await candidatesResponse.json()
    console.log(`✅ ${candidatesData.candidates.length} candidats trouvés`)
    
    if (candidatesData.candidates.length === 0) {
      console.log('❌ Aucun candidat disponible pour créer un entretien')
      return false
    }
    
    const candidate = candidatesData.candidates[0]
    console.log(`📝 Candidat sélectionné: ${candidate.name} (${candidate.email})`)
    
    // 2. Créer un entretien de test
    console.log('\n📅 Création d\'un entretien de test...')
    
    const interviewData = {
      title: `Entretien Test - ${candidate.name}`,
      description: 'Entretien de test créé automatiquement par le script de validation',
      candidateId: candidate.id,
      scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Demain
      type: 'DISCOVERY'
    }
    
    console.log('📋 Données de l\'entretien:', {
      title: interviewData.title,
      candidateId: interviewData.candidateId,
      scheduledAt: interviewData.scheduledAt,
      type: interviewData.type
    })
    
    const createResponse = await fetch(`${BASE_URL}/api/interviews`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(interviewData),
    })
    
    console.log(`📡 Réponse API: HTTP ${createResponse.status}`)
    
    if (createResponse.ok) {
      const newInterview = await createResponse.json()
      console.log('✅ Entretien créé avec succès!')
      console.log(`   ID: ${newInterview.id}`)
      console.log(`   Titre: ${newInterview.title}`)
      console.log(`   Candidat: ${newInterview.candidateName}`)
      console.log(`   Date: ${new Date(newInterview.scheduledAt).toLocaleString('fr-FR')}`)
      console.log(`   Statut: ${newInterview.status}`)
      
      // 3. Vérifier que l'entretien apparaît dans la liste
      console.log('\n🔍 Vérification de la liste des entretiens...')
      const listResponse = await fetch(`${BASE_URL}/api/interviews`)
      
      if (listResponse.ok) {
        const interviews = await listResponse.json()
        const createdInterview = interviews.find(i => i.id === newInterview.id)
        
        if (createdInterview) {
          console.log('✅ Entretien trouvé dans la liste')
          console.log(`   Total entretiens: ${interviews.length}`)
        } else {
          console.log('❌ Entretien non trouvé dans la liste')
          return false
        }
      } else {
        console.log('❌ Erreur lors de la récupération de la liste')
        return false
      }
      
      return newInterview.id
      
    } else {
      const errorText = await createResponse.text()
      console.log('❌ Erreur lors de la création:')
      console.log(`   Status: ${createResponse.status}`)
      console.log(`   Réponse: ${errorText}`)
      return false
    }
    
  } catch (error) {
    console.log('❌ Erreur lors du test:', error.message)
    return false
  }
}

async function testInterviewDisplay() {
  console.log('\n🖥️ Test d\'Affichage des Entretiens')
  console.log('===================================')
  
  try {
    // Récupérer la page dashboard
    const response = await fetch(`${BASE_URL}/dashboard`)
    
    if (!response.ok) {
      console.log(`❌ Dashboard inaccessible: HTTP ${response.status}`)
      return false
    }
    
    const html = await response.text()
    
    // Vérifications de l'affichage
    const checks = [
      { name: 'Onglet Entretiens présent', test: html.includes('Entretiens') },
      { name: 'Section Entretiens Planifiés', test: html.includes('Entretiens Planifiés') },
      { name: 'Formulaire Nouvel entretien', test: html.includes('Nouvel entretien') },
      { name: 'Bouton Planifier', test: html.includes('Planifier l\'entretien') },
      { name: 'Champs formulaire', test: html.includes('Titre de l\'entretien') },
      { name: 'Sélection candidat', test: html.includes('Sélectionner un candidat') }
    ]
    
    console.log('🔍 Vérifications de l\'affichage:')
    let passedChecks = 0
    
    checks.forEach(check => {
      if (check.test) {
        console.log(`   ✅ ${check.name}`)
        passedChecks++
      } else {
        console.log(`   ❌ ${check.name}`)
      }
    })
    
    const score = passedChecks / checks.length
    console.log(`\n📊 Score affichage: ${passedChecks}/${checks.length} (${Math.round(score * 100)}%)`)
    
    return score >= 0.8
    
  } catch (error) {
    console.log('❌ Erreur test affichage:', error.message)
    return false
  }
}

async function testInterviewAPI() {
  console.log('\n🔌 Test de l\'API Entretiens')
  console.log('============================')
  
  try {
    // Test GET
    console.log('📋 Test GET /api/interviews...')
    const getResponse = await fetch(`${BASE_URL}/api/interviews`)
    
    if (getResponse.ok) {
      const interviews = await getResponse.json()
      console.log(`✅ API accessible - ${interviews.length} entretiens`)
      
      if (interviews.length > 0) {
        const interview = interviews[0]
        console.log('📅 Premier entretien:')
        console.log(`   Titre: ${interview.title}`)
        console.log(`   Candidat: ${interview.candidateName}`)
        console.log(`   Date: ${new Date(interview.scheduledAt).toLocaleString('fr-FR')}`)
        console.log(`   Statut: ${interview.status}`)
      }
      
      return true
    } else {
      console.log(`❌ API inaccessible: HTTP ${getResponse.status}`)
      return false
    }
    
  } catch (error) {
    console.log('❌ Erreur test API:', error.message)
    return false
  }
}

async function main() {
  console.log('🧪 Tests de Création d\'Entretiens - Karma Com Solidarité')
  console.log('========================================================')
  
  // Vérifier que l'application est accessible
  try {
    const response = await fetch(`${BASE_URL}`)
    if (!response.ok) {
      throw new Error(`Application non accessible: HTTP ${response.status}`)
    }
    console.log('✅ Application accessible\n')
  } catch (error) {
    console.log('❌ Application non accessible:', error.message)
    console.log('💡 Assurez-vous que l\'application est démarrée avec "npm run dev"')
    process.exit(1)
  }
  
  // Exécuter les tests
  const apiOk = await testInterviewAPI()
  const creationOk = await testInterviewCreation()
  const displayOk = await testInterviewDisplay()
  
  // Résumé final
  console.log('\n📋 Résumé des Tests')
  console.log('===================')
  
  const tests = [
    { name: 'API Entretiens', passed: apiOk },
    { name: 'Création Entretien', passed: !!creationOk },
    { name: 'Affichage Dashboard', passed: displayOk }
  ]
  
  const passedCount = tests.filter(t => t.passed).length
  
  tests.forEach(test => {
    console.log(`${test.passed ? '✅' : '❌'} ${test.name}`)
  })
  
  console.log(`\n📊 Score global: ${passedCount}/${tests.length}`)
  
  if (passedCount === tests.length) {
    console.log('\n🎉 Tous les tests de création d\'entretiens sont passés!')
    console.log('\n✅ Fonctionnalités validées:')
    console.log('   • API de création d\'entretiens fonctionnelle')
    console.log('   • Entretiens enregistrés en base de données')
    console.log('   • Affichage dans le dashboard opérationnel')
    console.log('   • Formulaire de création complet')
    
    if (creationOk) {
      console.log(`\n📅 Entretien de test créé avec l'ID: ${creationOk}`)
    }
    
    console.log('\n🌐 Testez manuellement:')
    console.log('   1. Accédez au dashboard: http://localhost:3000/dashboard')
    console.log('   2. Cliquez sur l\'onglet "Entretiens"')
    console.log('   3. Cliquez sur "Nouvel entretien"')
    console.log('   4. Remplissez le formulaire et validez')
    
  } else {
    console.log('\n⚠️ Certains tests ont échoué.')
    console.log('💡 Vérifiez:')
    console.log('   • La base de données est-elle accessible?')
    console.log('   • Y a-t-il des candidats dans la base?')
    console.log('   • L\'application est-elle bien démarrée?')
    console.log('   • Consultez les logs de la console navigateur')
  }
}

// Vérifier si node-fetch est disponible
try {
  require('node-fetch')
} catch (error) {
  console.log('❌ node-fetch n\'est pas installé')
  console.log('💡 Il devrait être installé avec les devDependencies')
  process.exit(1)
}

main()
