{"name": "karma-com-dashboard", "version": "1.0.0", "description": "Système de gestion d'adhésion pour Karma Com Solidarité", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:init": "node scripts/init-db.js", "db:reset": "prisma migrate reset --force && npm run db:init", "db:seed-constants": "node scripts/seed-constants.js", "test:inscriptions": "node scripts/test-inscriptions.js"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "prisma": "^5.7.1", "eslint": "^8", "eslint-config-next": "14.0.4", "node-fetch": "^2.6.7"}}