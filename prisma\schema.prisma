// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Énumération pour les types d'utilisateurs
enum UserType {
  ASSOCIATION
  ORGANIZATION
  VOLUNTEER
  HR_ADMIN
}

// Énumération pour les statuts d'adhésion
enum MembershipStatus {
  PENDING
  APPROVED
  REJECTED
  ACTIVE
  INACTIVE
  SUSPENDED
}

// Énumération pour les pôles
enum Department {
  RH_ASSO
  COMMUNICATION
  PARTENARIATS_RSE
  IT
  DESIGN
  JURIDIQUE
  COMPTABILITE
  AUTO_AI
}

// Énumération pour les statuts de rendez-vous
enum AppointmentStatus {
  SCHEDULED
  CONFIRMED
  COMPLETED
  CANCELLED
  NO_SHOW
}

// Énumération pour les types de rendez-vous
enum AppointmentType {
  DISCOVERY
  INTEGRATION
  FOLLOW_UP
  INTERVIEW
}

// Modèle utilisateur principal
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String?
  name      String
  phone     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Type d'utilisateur
  userType UserType
  
  // Relations
  profile   Profile?
  appointments Appointment[]
  createdAppointments Appointment[] @relation("CreatedBy")
  
  @@map("users")
}

// Profil détaillé selon le type d'utilisateur
model Profile {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Informations communes
  firstName    String
  lastName     String
  address      String?
  city         String?
  postalCode   String?
  country      String?
  dateOfBirth  DateTime?
  
  // Informations spécifiques aux associations
  organizationName String?
  siret           String?
  website         String?
  description     String?
  
  // Informations spécifiques aux bénévoles
  skills          String[]
  availability    String?
  motivation      String?
  
  // Statut et département
  membershipStatus MembershipStatus @default(PENDING)
  department       Department?
  
  // Métadonnées
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("profiles")
}

// Modèle pour les rendez-vous
model Appointment {
  id          String   @id @default(cuid())
  title       String
  description String?
  
  // Date et heure
  scheduledAt DateTime
  duration    Int      @default(60) // en minutes
  
  // Statut et type
  status AppointmentStatus @default(SCHEDULED)
  type   AppointmentType
  
  // Relations
  candidateId String
  candidate   User   @relation(fields: [candidateId], references: [id])
  
  createdById String
  createdBy   User   @relation("CreatedBy", fields: [createdById], references: [id])
  
  // Métadonnées
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Notes et commentaires
  notes String?
  
  @@map("appointments")
}

// Modèle pour les documents
model Document {
  id       String @id @default(cuid())
  filename String
  filepath String
  mimetype String
  size     Int
  
  // Métadonnées
  uploadedAt DateTime @default(now())
  uploadedBy String
  
  @@map("documents")
}

// Modèle pour les logs d'activité
model ActivityLog {
  id          String   @id @default(cuid())
  action      String
  description String
  userId      String?
  metadata    Json?
  createdAt   DateTime @default(now())

  @@map("activity_logs")
}

// Modèle pour les types d'organisations
model OrganizationType {
  id        String   @id @default(cuid())
  name      String   @unique
  isActive  Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("organization_types")
}

// Modèle pour les secteurs d'activité
model Sector {
  id        String   @id @default(cuid())
  name      String   @unique
  isActive  Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("sectors")
}

// Modèle pour les types de partenariat
model PartnershipType {
  id        String   @id @default(cuid())
  name      String   @unique
  isActive  Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("partnership_types")
}

// Modèle pour les compétences
model Skill {
  id        String   @id @default(cuid())
  name      String   @unique
  category  String?
  isActive  Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("skills")
}

// Modèle pour les départements/pôles
model DepartmentOption {
  id        String   @id @default(cuid())
  name      String   @unique
  isActive  Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("department_options")
}
