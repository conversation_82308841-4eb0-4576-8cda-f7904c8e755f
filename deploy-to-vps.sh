#!/bin/bash

# Script de déploiement Karma Com Solidarité sur VPS
# Usage: ./deploy-to-vps.sh

set -e

# Configuration VPS
VPS_IP="*************"
VPS_USER="vpsadmin"
DOMAIN="kcs.zidani.org"
APP_NAME="kcs"
DEPLOY_DIR="/home/<USER>/$APP_NAME"

# Configuration de l'application (correspond à docker-compose.yml)
DB_NAME="karma_com_db"
DB_USER="karma_user"
DB_PASSWORD="karma_password_2024"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Vérification des prérequis locaux
log "Vérification des prérequis locaux..."

if [ ! -f "package.json" ]; then
    error "Ce script doit être exécuté depuis la racine du projet Karma Com"
fi

if [ ! -f "Dockerfile.simple" ]; then
    error "Dockerfile.simple non trouvé. Assurez-vous qu'il existe."
fi

if ! command -v ssh &> /dev/null; then
    error "SSH n'est pas installé"
fi

success "Prérequis locaux validés"

# Test de connexion SSH
log "Test de connexion SSH au VPS..."
if ssh -o ConnectTimeout=10 -o BatchMode=yes $VPS_USER@$VPS_IP exit 2>/dev/null; then
    success "Connexion SSH au VPS réussie"
else
    error "Impossible de se connecter au VPS. Vérifiez vos clés SSH et la configuration."
fi

# Création du répertoire de déploiement sur le VPS
log "Préparation du VPS pour le déploiement..."
ssh $VPS_USER@$VPS_IP << 'EOF'
    # Créer le répertoire de déploiement dans le home de l'utilisateur
    mkdir -p /home/<USER>/kcs

    # Créer les répertoires nécessaires
    mkdir -p /home/<USER>/kcs/{backups,logs,ssl}

    # S'assurer que les permissions sont correctes
    chmod 755 /home/<USER>/kcs
    chown -R vpsadmin:vpsadmin /home/<USER>/kcs

    # Créer un lien symbolique /kcs vers le répertoire dans home (optionnel)
    sudo ln -sf /home/<USER>/kcs /kcs 2>/dev/null || true

    echo "Répertoires créés sur le VPS (/home/<USER>/kcs)"
    ls -la /home/<USER>/kcs
EOF

success "VPS préparé"

# Vérifier que le répertoire existe bien
log "Vérification du répertoire de déploiement..."
if ssh $VPS_USER@$VPS_IP "test -d /home/<USER>/kcs"; then
    success "Répertoire /home/<USER>/kcs existe"
else
    error "Répertoire /home/<USER>/kcs n'existe pas"
fi

# Copie des fichiers nécessaires
log "Copie des fichiers vers le VPS..."

# Créer un archive temporaire avec seulement les fichiers nécessaires
TEMP_DIR=$(mktemp -d)
ARCHIVE_NAME="karma-com-deploy.tar.gz"

log "Création de l'archive de déploiement..."

# Créer la liste des fichiers à inclure
FILES_TO_COPY=(
    "src"
    "prisma"
    "package.json"
    "next.config.js"
    "tailwind.config.js"
    "postcss.config.js"
    "tsconfig.json"
    "Dockerfile.simple"
)

# Copier les fichiers essentiels un par un
for file in "${FILES_TO_COPY[@]}"; do
    if [ -e "$file" ]; then
        cp -r "$file" "$TEMP_DIR/"
        log "Copié: $file"
    else
        warning "Fichier non trouvé: $file"
    fi
done

# Copier les fichiers optionnels
if [ -f "package-lock.json" ]; then
    cp package-lock.json "$TEMP_DIR/"
fi

if [ -d "public" ]; then
    cp -r public "$TEMP_DIR/"
else
    mkdir -p "$TEMP_DIR/public"
    echo "<h1>Karma Com Solidarité</h1>" > "$TEMP_DIR/public/index.html"
fi

if [ -f "docker-compose.production.yml" ]; then
    cp docker-compose.production.yml "$TEMP_DIR/"
elif [ -f "docker-compose.yml" ]; then
    cp docker-compose.yml "$TEMP_DIR/docker-compose.production.yml"
fi

if [ -d "nginx" ]; then
    cp -r nginx "$TEMP_DIR/"
else
    mkdir -p "$TEMP_DIR/nginx"
fi

# Créer l'archive depuis le répertoire parent pour éviter les conflits
cd "$TEMP_DIR"
if tar -czf "$ARCHIVE_NAME" --exclude="$ARCHIVE_NAME" * 2>/dev/null; then
    success "Archive créée avec succès"
else
    error "Échec de la création de l'archive"
fi
cd - > /dev/null

# Vérifier que l'archive a été créée
if [ ! -f "$TEMP_DIR/$ARCHIVE_NAME" ]; then
    error "Archive non trouvée après création"
fi

ARCHIVE_SIZE=$(du -h "$TEMP_DIR/$ARCHIVE_NAME" | cut -f1)
log "Taille de l'archive: $ARCHIVE_SIZE"

# Copier l'archive vers le VPS
scp "$TEMP_DIR/$ARCHIVE_NAME" $VPS_USER@$VPS_IP:/home/<USER>/kcs/

# Nettoyer le répertoire temporaire
rm -rf $TEMP_DIR

success "Fichiers copiés vers le VPS"

# Déploiement sur le VPS
log "Déploiement de l'application sur le VPS..."

ssh $VPS_USER@$VPS_IP << EOF
    set -e

    echo "📦 Extraction de l'archive..."
    cd /home/<USER>/kcs
    tar -xzf /home/<USER>/kcs/karma-com-deploy.tar.gz
    rm /home/<USER>/kcs/karma-com-deploy.tar.gz

    echo "🔧 Configuration de l'environnement..."

    # Créer le fichier .env.production
    cat > .env.production << 'ENVEOF'
NODE_ENV=production
DATABASE_URL=*********************************************************/karma_com_db
NEXTAUTH_SECRET=karma_com_secret_key_2024
NEXTAUTH_URL=https://$DOMAIN
ENVEOF

    echo "🐳 Construction de l'image Docker..."
    docker build -f Dockerfile.simple -t karma-com-app:latest .
    
    echo "📋 Configuration Docker Compose..."
    
    # Créer le docker-compose.yml pour production (basé sur le fichier local)
    cat > docker-compose.yml << 'COMPOSEEOF'
version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: karma-com-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: karma_com_db
      POSTGRES_USER: karma_user
      POSTGRES_PASSWORD: karma_password_2024
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - karma-network

  # Application Next.js
  app:
    image: karma-com-app:latest
    container_name: karma-com-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*********************************************************/karma_com_db
      - NEXTAUTH_SECRET=karma_com_secret_key_2024
      - NEXTAUTH_URL=https://$DOMAIN
    depends_on:
      - postgres
    networks:
      - karma-network

  # pgAdmin pour la gestion de la base de données
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: karma-com-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - karma-network

volumes:
  postgres_data:

networks:
  karma-network:
    driver: bridge
COMPOSEEOF
    
    echo "🚀 Démarrage des services..."

    # Vérifier si docker-compose est disponible, sinon utiliser docker compose
    if command -v docker-compose &> /dev/null; then
        echo "Utilisation de docker-compose"
        docker-compose down 2>/dev/null || true
        docker-compose up -d
    else
        echo "Utilisation de docker compose (plugin)"
        docker compose down 2>/dev/null || true
        docker compose up -d
    fi
    
    echo "⏳ Attente du démarrage des services..."
    sleep 30
    
    echo "✅ Services démarrés"
EOF

success "Application déployée sur le VPS"

# Configuration Nginx
log "Configuration de Nginx pour le domaine $DOMAIN..."

ssh $VPS_USER@$VPS_IP << EOF
    set -e

    echo "🌐 Configuration Nginx..."

    # Créer la configuration Nginx (avec sudo interactif)
    cat > /tmp/nginx-$DOMAIN.conf << 'NGINXEOF'
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    # Redirection vers HTTPS (sera configuré avec Certbot)
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # Configuration SSL (sera ajoutée par Certbot)
    
    # Headers de sécurité
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Logs
    access_log /var/log/nginx/karma-com-access.log;
    error_log /var/log/nginx/karma-com-error.log;
    
    # Taille maximale des uploads
    client_max_body_size 10M;
    
    # Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Proxy vers l'application
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health check
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host \$host;
    }
    
    # Assets statiques avec cache
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host \$host;
    }
}
NGINXEOF

    # Copier la configuration avec sudo
    sudo cp /tmp/nginx-$DOMAIN.conf /etc/nginx/sites-available/$DOMAIN

    # Activer le site
    sudo ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/$DOMAIN

    # Supprimer le site par défaut s'il existe
    sudo rm -f /etc/nginx/sites-enabled/default

    # Tester la configuration
    sudo nginx -t

    # Redémarrer Nginx
    sudo systemctl restart nginx

    # Nettoyer le fichier temporaire
    rm -f /tmp/nginx-$DOMAIN.conf

    echo "✅ Nginx configuré"
EOF

success "Nginx configuré"

# Configuration SSL avec Certbot
log "Configuration SSL avec Let's Encrypt..."

ssh -t $VPS_USER@$VPS_IP << EOF
    set -e

    echo "🔒 Configuration SSL..."

    # Vérifier si certbot est installé
    if ! command -v certbot &> /dev/null; then
        echo "⚠️ Certbot non installé, installation en cours..."
        sudo apt update
        sudo apt install -y certbot python3-certbot-nginx
    fi

    # Obtenir le certificat SSL
    sudo certbot --nginx --agree-tos --redirect --hsts --staple-ocsp --email <EMAIL> -d $DOMAIN -d www.$DOMAIN --non-interactive

    echo "✅ SSL configuré"
EOF

success "SSL configuré"

# Vérification finale
log "Vérification du déploiement..."

ssh $VPS_USER@$VPS_IP << 'EOF'
    echo "🔍 Vérification des services..."
    
    # Vérifier Docker
    docker-compose ps
    
    # Vérifier l'application
    sleep 10
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Application accessible localement"
    else
        echo "❌ Application non accessible"
    fi
    
    # Vérifier Nginx
    if sudo nginx -t; then
        echo "✅ Configuration Nginx valide"
    else
        echo "❌ Erreur configuration Nginx"
    fi
EOF

# Test final depuis l'extérieur
log "Test final de l'application..."
sleep 30

if curl -f https://$DOMAIN > /dev/null 2>&1; then
    success "Application accessible sur https://$DOMAIN"
else
    warning "Application pas encore accessible depuis l'extérieur (DNS en cours de propagation?)"
fi

# Résumé final
echo ""
echo "🎉 Résumé final - Déploiement terminé !"
echo "========================"
echo ""
echo "📊 Résumé final -  Informations de déploiement :"
echo "   🌐 URL : https://$DOMAIN"
echo "   🖥️  VPS : $VPS_USER@$VPS_IP"
echo "   📁 Répertoire : $DEPLOY_DIR"
echo ""
echo "🔧  Résumé final - Commandes utiles sur le VPS :"
echo "   📋 Voir les logs : docker-compose logs -f"
echo "   🔄 Redémarrer : docker-compose restart"
echo "   🛑 Arrêter : docker-compose down"
echo "   📊 État : docker-compose ps"
echo ""
echo "🗄️ Résumé final -  Base de données :"
echo "   🔗 Host : localhost (depuis le VPS)"
echo "   🔗 Port : 5432"
echo "   🔗 Database : karma_com_db"
echo "   🔗 User : karma_user"
echo "   🔗 Password : karma_password_2024"
echo "   🔗 pgAdmin : http://$VPS_IP:5050"
echo "     - Email : <EMAIL>"
echo "     - Password : admin123"
echo ""
echo "📚 Résumé final -  Logs importants :"
echo "   📄 Nginx : /var/log/nginx/karma-com-*.log"
echo "   📄 Application : docker-compose logs app"
echo "   📄 Base : docker-compose logs postgres"

success " Résumé final - Déploiement Karma Com Solidarité terminé avec succès !"
