import React from 'react'
import Image from 'next/image'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  showText?: boolean
  className?: string
}

const Logo: React.FC<LogoProps> = ({ 
  size = 'md', 
  showText = true, 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
    xl: 'h-24 w-24'
  }

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Logo SVG - Reproduction fidèle du logo Karma Com Solidarité */}
      <div className={`${sizeClasses[size]} relative`}>
        <svg
          viewBox="0 0 100 100"
          className="w-full h-full drop-shadow-sm"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Fond carré arrondi gris clair */}
          <rect
            x="5"
            y="5"
            width="90"
            height="90"
            rx="20"
            ry="20"
            fill="#f9fafb"
            stroke="#e5e7eb"
            strokeWidth="2"
          />

          {/* Élément bleu foncé (gauche) - forme elliptique inclinée */}
          <ellipse
            cx="28"
            cy="50"
            rx="10"
            ry="22"
            fill="#1e3a5f"
            transform="rotate(-20 28 50)"
          />

          {/* Élément rose/magenta (centre) - forme elliptique plus large */}
          <ellipse
            cx="50"
            cy="50"
            rx="14"
            ry="26"
            fill="#e91e63"
            transform="rotate(10 50 50)"
          />

          {/* Élément gris (droite) - forme elliptique subtile */}
          <ellipse
            cx="72"
            cy="50"
            rx="8"
            ry="18"
            fill="#9ca3af"
            opacity="0.6"
            transform="rotate(35 72 50)"
          />
        </svg>
      </div>
      
      {showText && (
        <div className="flex flex-col">
          <span className={`font-bold text-karma-blue ${textSizeClasses[size]} leading-tight`}>
            Karma Com
          </span>
          <span className={`font-semibold text-karma-blue ${size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'} leading-tight`}>
            Solidarité
          </span>
        </div>
      )}
    </div>
  )
}

export default Logo
